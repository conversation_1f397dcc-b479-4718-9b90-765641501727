(function(){
  'use strict';
  var g = (new Function('return this;'))();
  function __init_card_bundle__(lynxCoreInject) {
    g.__bundle__holder = undefined;
    var globDynamicComponentEntry = g.globDynamicComponentEntry || '__Card__';
    var tt = lynxCoreInject.tt;
    tt.define("main__main-thread.97a164f40675830d.hot-update.js", function(require, module, exports, __Card,setTimeout,setInterval,clearInterval,clearTimeout,NativeModules,tt,console,__Component,__ReactLynx,nativeAppId,__Behavior,LynxJSBI,lynx,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,fetch,XMLHttpRequest,__WebSocket__,webkit,Reporter,print,global,requestAnimationFrame,cancelAnimationFrame) {
lynx = lynx || {};
lynx.targetSdkVersion=lynx.targetSdkVersion||"3.2";
var Promise = lynx.Promise;
fetch = fetch || lynx.fetch;
requestAnimationFrame = requestAnimationFrame || lynx.requestAnimationFrame;
cancelAnimationFrame = cancelAnimationFrame || lynx.cancelAnimationFrame;

// This needs to be wrapped in an IIFE because it needs to be isolated against Lynx injected variables.
(() => {
// lynx chunks entries
if (!lynx.__chunk_entries__) {
  // Initialize once
  lynx.__chunk_entries__ = {};
}
if (!lynx.__chunk_entries__["main__main-thread"]) {
  lynx.__chunk_entries__["main__main-thread"] = globDynamicComponentEntry;
} else {
  globDynamicComponentEntry = lynx.__chunk_entries__["main__main-thread"];
}

"use strict";
exports.ids = ["main__main-thread"];
exports.modules = {
"(react:main-thread)/./src/components/TaskList.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  TaskList: () => (TaskList)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _context_AppContext_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/context/AppContext.tsx");
/* ESM import */var _TaskItem_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("(react:main-thread)/./src/components/TaskItem.tsx");
/* ESM import */var _AddTaskForm_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("(react:main-thread)/./src/components/AddTaskForm.tsx");
/* ESM import */var _EditTaskForm_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("(react:main-thread)/./src/components/EditTaskForm.tsx");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");






const __snapshot_36995_8ded4_2 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_2", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_3 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_3", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_4 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_4", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_5 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_5", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_6 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_6", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_9 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_9", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-items");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_8 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_8", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-section");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "section-header");
    __AppendElement(el, el1);
    const el2 = __CreateText(pageId);
    __SetClasses(el2, "section-title");
    __AppendElement(el1, el2);
    const el3 = __CreateRawText("Pending Tasks (");
    __AppendElement(el2, el3);
    const el4 = __CreateWrapperElement(pageId);
    __AppendElement(el2, el4);
    const el5 = __CreateRawText(")");
    __AppendElement(el2, el5);
    const el6 = __CreateWrapperElement(pageId);
    __AppendElement(el, el6);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        4
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        6
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_11 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_11", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-items");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_10 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_10", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-section");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "section-header");
    __AppendElement(el, el1);
    const el2 = __CreateText(pageId);
    __SetClasses(el2, "section-title");
    __AppendElement(el1, el2);
    const el3 = __CreateRawText("Completed Tasks (");
    __AppendElement(el2, el3);
    const el4 = __CreateWrapperElement(pageId);
    __AppendElement(el2, el4);
    const el5 = __CreateRawText(")");
    __AppendElement(el2, el5);
    const el6 = __CreateWrapperElement(pageId);
    __AppendElement(el, el6);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        4
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        6
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_12 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_12", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "empty-state");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "empty-icon");
    __AppendElement(el, el1);
    const el2 = __CreateRawText("\u{1F4DD}");
    __AppendElement(el1, el2);
    const el3 = __CreateText(pageId);
    __SetClasses(el3, "empty-title");
    __AppendElement(el, el3);
    const el4 = __CreateRawText("No tasks yet");
    __AppendElement(el3, el4);
    const el5 = __CreateText(pageId);
    __SetClasses(el5, "empty-description");
    __AppendElement(el, el5);
    const el6 = __CreateRawText("Create your first task to get started with organizing your work!");
    __AppendElement(el5, el6);
    const el7 = __CreateView(pageId);
    __SetClasses(el7, "empty-action");
    __AppendElement(el, el7);
    const el8 = __CreateText(pageId);
    __SetClasses(el8, "empty-action-text");
    __AppendElement(el7, el8);
    const el9 = __CreateRawText("Create First Task");
    __AppendElement(el8, el9);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 7, "bindEvent", "tap", '')
], null, undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_7 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_7", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateScrollView(pageId);
    __SetClasses(el, "task-list-content");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_13 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_13", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "modal-overlay");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "modal-content");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_14 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_14", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "modal-overlay");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "modal-content");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_36995_8ded4_1 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_8ded4_1", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-list");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "task-list-header");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "header-content");
    __AppendElement(el1, el2);
    const el3 = __CreateText(pageId);
    __SetClasses(el3, "header-title");
    __AppendElement(el2, el3);
    const el4 = __CreateRawText("My Tasks");
    __AppendElement(el3, el4);
    const el5 = __CreateView(pageId);
    __SetClasses(el5, "add-task-button");
    __AppendElement(el2, el5);
    const el6 = __CreateText(pageId);
    __SetClasses(el6, "add-button-text");
    __AppendElement(el5, el6);
    const el7 = __CreateRawText("+ Add Task");
    __AppendElement(el6, el7);
    const el8 = __CreateView(pageId);
    __SetClasses(el8, "task-stats");
    __AppendElement(el1, el8);
    const el9 = __CreateView(pageId);
    __SetClasses(el9, "stat-item");
    __AppendElement(el8, el9);
    const el10 = __CreateWrapperElement(pageId);
    __AppendElement(el9, el10);
    const el11 = __CreateText(pageId);
    __SetClasses(el11, "stat-label");
    __AppendElement(el9, el11);
    const el12 = __CreateRawText("Total");
    __AppendElement(el11, el12);
    const el13 = __CreateView(pageId);
    __SetClasses(el13, "stat-item");
    __AppendElement(el8, el13);
    const el14 = __CreateWrapperElement(pageId);
    __AppendElement(el13, el14);
    const el15 = __CreateText(pageId);
    __SetClasses(el15, "stat-label");
    __AppendElement(el13, el15);
    const el16 = __CreateRawText("Pending");
    __AppendElement(el15, el16);
    const el17 = __CreateView(pageId);
    __SetClasses(el17, "stat-item");
    __AppendElement(el8, el17);
    const el18 = __CreateWrapperElement(pageId);
    __AppendElement(el17, el18);
    const el19 = __CreateText(pageId);
    __SetClasses(el19, "stat-label");
    __AppendElement(el17, el19);
    const el20 = __CreateRawText("Completed");
    __AppendElement(el19, el20);
    const el21 = __CreateView(pageId);
    __SetClasses(el21, "stat-item overdue");
    __AppendElement(el8, el21);
    const el22 = __CreateWrapperElement(pageId);
    __AppendElement(el21, el22);
    const el23 = __CreateText(pageId);
    __SetClasses(el23, "stat-label");
    __AppendElement(el21, el23);
    const el24 = __CreateRawText("Overdue");
    __AppendElement(el23, el24);
    const el25 = __CreateView(pageId);
    __SetClasses(el25, "stat-item due-today");
    __AppendElement(el8, el25);
    const el26 = __CreateWrapperElement(pageId);
    __AppendElement(el25, el26);
    const el27 = __CreateText(pageId);
    __SetClasses(el27, "stat-label");
    __AppendElement(el25, el27);
    const el28 = __CreateRawText("Due Today");
    __AppendElement(el27, el28);
    const el29 = __CreateWrapperElement(pageId);
    __AppendElement(el, el29);
    const el30 = __CreateWrapperElement(pageId);
    __AppendElement(el, el30);
    const el31 = __CreateWrapperElement(pageId);
    __AppendElement(el, el31);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17,
        el18,
        el19,
        el20,
        el21,
        el22,
        el23,
        el24,
        el25,
        el26,
        el27,
        el28,
        el29,
        el30,
        el31
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 5, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        10
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        14
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        18
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        22
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        26
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        29
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        30
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        31
    ]
], undefined, globDynamicComponentEntry, null);
function TaskList() {
    const { getFilteredTasks, getTaskStats } = (0,_context_AppContext_js__WEBPACK_IMPORTED_MODULE_2__.useApp)();
    const [showAddForm, setShowAddForm] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [editingTask, setEditingTask] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const tasks = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getFilteredTasks(), [
        getFilteredTasks
    ]);
    const stats = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getTaskStats(), [
        getTaskStats
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setShowAddForm(true);
    }, []);
    const handleCloseAddForm = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setShowAddForm(false);
    }, []);
    const handleEditTask = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((task)=>{
        setEditingTask(task);
    }, []);
    const handleCloseEditForm = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setEditingTask(null);
    }, []);
    const handleTaskAdded = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setShowAddForm(false);
    }, []);
    const handleTaskUpdated = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setEditingTask(null);
    }, []);
    // Group tasks by status for better organization
    const pendingTasks = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>tasks.filter((task)=>task.status === 'pending'), [
        tasks
    ]);
    const completedTasks = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>tasks.filter((task)=>task.status === 'completed'), [
        tasks
    ]);
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_1, {
        values: [
            1
        ],
        children: [
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_2, {
                children: stats.total
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 64,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_3, {
                children: stats.pending
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 68,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_4, {
                children: stats.completed
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 72,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_5, {
                children: stats.overdue
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 76,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_6, {
                children: stats.dueToday
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 80,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_7, {
                children: [
                    pendingTasks.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_8, {
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: pendingTasks.length
                            }, void 0, false, void 0, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_9, {
                                children: pendingTasks.map((task)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskItem_js__WEBPACK_IMPORTED_MODULE_3__.TaskItem, {
                                        task: task,
                                        onEdit: handleEditTask
                                    }, task.id, false, {
                                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                                        lineNumber: 96,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                                lineNumber: 94,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 90,
                        columnNumber: 11
                    }, this),
                    completedTasks.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_10, {
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: completedTasks.length
                            }, void 0, false, void 0, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_11, {
                                children: completedTasks.map((task)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskItem_js__WEBPACK_IMPORTED_MODULE_3__.TaskItem, {
                                        task: task,
                                        onEdit: handleEditTask
                                    }, task.id, false, {
                                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                                        lineNumber: 114,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                                lineNumber: 112,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 108,
                        columnNumber: 11
                    }, this),
                    tasks.length === 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_12, {
                        values: [
                            1
                        ]
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 126,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 87,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: showAddForm && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_13, {
                    children: /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddTaskForm_js__WEBPACK_IMPORTED_MODULE_4__.AddTaskForm, {
                        onClose: handleCloseAddForm,
                        onTaskAdded: handleTaskAdded
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 143,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, this)
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: editingTask && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_8ded4_14, {
                    children: /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditTaskForm_js__WEBPACK_IMPORTED_MODULE_5__.EditTaskForm, {
                        task: editingTask,
                        onClose: handleCloseEditForm,
                        onTaskUpdated: handleTaskUpdated
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 155,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                    lineNumber: 153,
                    columnNumber: 9
                }, this)
            }, void 0, false, void 0, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),

};
exports.runtime = function(__webpack_require__) {
// webpack/runtime/get_full_hash
(() => {
__webpack_require__.h = () => ("992f5f6f82a0312f")
})();

}
;
;

})();
    });
    return tt.require("main__main-thread.97a164f40675830d.hot-update.js");
  };
  if (g && g.bundleSupportLoadScript){
    var res = {init: __init_card_bundle__};
    g.__bundle__holder = res;
    return res;
  } else {
    __init_card_bundle__({"tt": tt});
  };
})();

//# sourceMappingURL=http://**************:3000/main__main-thread.97a164f40675830d.hot-update.js.map
(function(){
  'use strict';
  var g = (new Function('return this;'))();
  function __init_card_bundle__(lynxCoreInject) {
    g.__bundle__holder = undefined;
    var globDynamicComponentEntry = g.globDynamicComponentEntry || '__Card__';
    var tt = lynxCoreInject.tt;
    tt.define("main.092f2cd1ec4fd088.hot-update.js", function(require, module, exports, __Card,setTimeout,setInterval,clearInterval,clearTimeout,NativeModules,tt,console,__Component,__ReactLynx,nativeAppId,__Behavior,LynxJSBI,lynx,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,fetch,XMLHttpRequest,__WebSocket__,webkit,Reporter,print,global,requestAnimationFrame,cancelAnimationFrame) {
lynx = lynx || {};
lynx.targetSdkVersion=lynx.targetSdkVersion||"3.2";
var Promise = lynx.Promise;
fetch = fetch || lynx.fetch;
requestAnimationFrame = requestAnimationFrame || lynx.requestAnimationFrame;
cancelAnimationFrame = cancelAnimationFrame || lynx.cancelAnimationFrame;

// This needs to be wrapped in an IIFE because it needs to be isolated against Lynx injected variables.
(() => {
// lynx chunks entries
if (!lynx.__chunk_entries__) {
  // Initialize once
  lynx.__chunk_entries__ = {};
}
if (!lynx.__chunk_entries__["main"]) {
  lynx.__chunk_entries__["main"] = globDynamicComponentEntry;
} else {
  globDynamicComponentEntry = lynx.__chunk_entries__["main"];
}

"use strict";
exports.ids = ["main"];
exports.modules = {
"(react:background)/./src/App.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  App: () => (App)
});
/* ESM import */var _lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/jsx-dev-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _App_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:background)/./src/App.css");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:background)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");



const __snapshot_835da_3ae6c_2 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_2", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_3 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_3", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_4 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_4", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_6 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_6", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_7 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_7", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "filter-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_5 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_5", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_8 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_8", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "filter-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_10 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_10", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "filter-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_9 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_9", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "filter-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_13 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_13", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_14 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_14", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-name");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_12 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_12", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "category-stat");
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    const el3 = __CreateText(pageId);
    __SetClasses(el3, "stat-count");
    __AppendElement(el, el3);
    const el4 = __CreateWrapperElement(pageId);
    __AppendElement(el3, el4);
    const el5 = __CreateRawText("/");
    __AppendElement(el3, el5);
    const el6 = __CreateWrapperElement(pageId);
    __AppendElement(el3, el6);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[0]);
    }
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        4
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        6
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_11 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_11", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "stats-grid");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_16 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_16", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "template-option");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "template-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_15 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_15", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "quick-templates");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_18 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_18", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "priority-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_17 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_17", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "priority-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_20 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_20", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_21 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_21", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_19 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_19", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_23 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_23", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "tag-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[1], ctx.__values[3]);
    }
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_22 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_22", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "tag-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_24 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_24", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "input-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_29 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_29", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "task-title");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_31 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_31", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_32 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_32", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-name");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_30 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_30", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-category");
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[0]);
    }
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_33 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_33", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "priority-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_28 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_28", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-item");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "priority-indicator");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "task-content");
    __AppendElement(el, el2);
    const el3 = __CreateView(pageId);
    __SetClasses(el3, "task-header");
    __AppendElement(el2, el3);
    const el4 = __CreateView(pageId);
    __SetClasses(el4, "task-checkbox");
    __AppendElement(el3, el4);
    const el5 = __CreateText(pageId);
    __SetClasses(el5, "checkbox-text");
    __AppendElement(el4, el5);
    const el6 = __CreateRawText("\u2610");
    __AppendElement(el5, el6);
    const el7 = __CreateWrapperElement(pageId);
    __AppendElement(el3, el7);
    const el8 = __CreateView(pageId);
    __SetClasses(el8, "task-actions");
    __AppendElement(el3, el8);
    const el9 = __CreateText(pageId);
    __SetClasses(el9, "action-button delete");
    __AppendElement(el8, el9);
    const el10 = __CreateRawText("\u{1F5D1}\uFE0F");
    __AppendElement(el9, el10);
    const el11 = __CreateView(pageId);
    __SetClasses(el11, "task-metadata");
    __AppendElement(el2, el11);
    const el12 = __CreateWrapperElement(pageId);
    __AppendElement(el11, el12);
    const el13 = __CreateView(pageId);
    __SetClasses(el13, "task-priority");
    __AppendElement(el11, el13);
    const el14 = __CreateWrapperElement(pageId);
    __AppendElement(el13, el14);
    const el15 = __CreateView(pageId);
    __SetClasses(el15, "task-date");
    __AppendElement(el11, el15);
    const el16 = __CreateText(pageId);
    __SetClasses(el16, "date-text");
    __AppendElement(el15, el16);
    const el17 = __CreateRawText("Created: ");
    __AppendElement(el16, el17);
    const el18 = __CreateWrapperElement(pageId);
    __AppendElement(el16, el18);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17,
        el18
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[1], ctx.__values[0]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 4, "bindEvent", "tap", ''),
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 9, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[13], ctx.__values[3]);
    }
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        7
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        12
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        14
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        18
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_27 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_27", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-items");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_26 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_26", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-section");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "section-title");
    __AppendElement(el, el1);
    const el2 = __CreateRawText("Pending Tasks (");
    __AppendElement(el1, el2);
    const el3 = __CreateWrapperElement(pageId);
    __AppendElement(el1, el3);
    const el4 = __CreateRawText(")");
    __AppendElement(el1, el4);
    const el5 = __CreateWrapperElement(pageId);
    __AppendElement(el, el5);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5
    ];
}, null, [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        3
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        5
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_37 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_37", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "task-title completed");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_39 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_39", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_40 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_40", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-name");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_38 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_38", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-category");
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[0]);
    }
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_41 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_41", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "priority-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_36 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_36", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-item completed");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "priority-indicator");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "task-content");
    __AppendElement(el, el2);
    const el3 = __CreateView(pageId);
    __SetClasses(el3, "task-header");
    __AppendElement(el2, el3);
    const el4 = __CreateView(pageId);
    __SetClasses(el4, "task-checkbox checked");
    __AppendElement(el3, el4);
    const el5 = __CreateText(pageId);
    __SetClasses(el5, "checkbox-text");
    __AppendElement(el4, el5);
    const el6 = __CreateRawText("\u2611");
    __AppendElement(el5, el6);
    const el7 = __CreateWrapperElement(pageId);
    __AppendElement(el3, el7);
    const el8 = __CreateView(pageId);
    __SetClasses(el8, "task-actions");
    __AppendElement(el3, el8);
    const el9 = __CreateText(pageId);
    __SetClasses(el9, "action-button delete");
    __AppendElement(el8, el9);
    const el10 = __CreateRawText("\u{1F5D1}\uFE0F");
    __AppendElement(el9, el10);
    const el11 = __CreateView(pageId);
    __SetClasses(el11, "task-metadata");
    __AppendElement(el2, el11);
    const el12 = __CreateWrapperElement(pageId);
    __AppendElement(el11, el12);
    const el13 = __CreateView(pageId);
    __SetClasses(el13, "task-priority");
    __AppendElement(el11, el13);
    const el14 = __CreateWrapperElement(pageId);
    __AppendElement(el13, el14);
    const el15 = __CreateView(pageId);
    __SetClasses(el15, "task-date");
    __AppendElement(el11, el15);
    const el16 = __CreateText(pageId);
    __SetClasses(el16, "date-text");
    __AppendElement(el15, el16);
    const el17 = __CreateRawText("Completed");
    __AppendElement(el16, el17);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[1], ctx.__values[0]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 4, "bindEvent", "tap", ''),
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 9, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[13], ctx.__values[3]);
    }
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        7
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        12
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        14
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_35 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_35", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-items");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_34 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_34", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-section");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "section-title");
    __AppendElement(el, el1);
    const el2 = __CreateRawText("Completed Tasks (");
    __AppendElement(el1, el2);
    const el3 = __CreateWrapperElement(pageId);
    __AppendElement(el1, el3);
    const el4 = __CreateRawText(")");
    __AppendElement(el1, el4);
    const el5 = __CreateWrapperElement(pageId);
    __AppendElement(el, el5);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5
    ];
}, null, [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        3
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        5
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_42 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_42", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "empty-state");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "empty-icon");
    __AppendElement(el, el1);
    const el2 = __CreateRawText("\u{1F4DD}");
    __AppendElement(el1, el2);
    const el3 = __CreateText(pageId);
    __SetClasses(el3, "empty-title");
    __AppendElement(el, el3);
    const el4 = __CreateRawText("No tasks yet");
    __AppendElement(el3, el4);
    const el5 = __CreateText(pageId);
    __SetClasses(el5, "empty-description");
    __AppendElement(el, el5);
    const el6 = __CreateRawText("Select a template above to create your first task!");
    __AppendElement(el5, el6);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6
    ];
}, null, null, undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_25 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_25", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateScrollView(pageId);
    __SetClasses(el, "task-list-content");
    return [
        el
    ];
}, null, (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_3ae6c_1 = /*#__PURE__*/ (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3ae6c_1", function() {
    const pageId = (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "Background");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "App");
    __AppendElement(el, el2);
    const el3 = __CreateView(pageId);
    __SetClasses(el3, "task-list-header");
    __AppendElement(el2, el3);
    const el4 = __CreateText(pageId);
    __SetClasses(el4, "header-title");
    __AppendElement(el3, el4);
    const el5 = __CreateRawText("My Todo List");
    __AppendElement(el4, el5);
    const el6 = __CreateView(pageId);
    __SetClasses(el6, "task-stats");
    __AppendElement(el3, el6);
    const el7 = __CreateView(pageId);
    __SetClasses(el7, "stat-item");
    __AppendElement(el6, el7);
    const el8 = __CreateWrapperElement(pageId);
    __AppendElement(el7, el8);
    const el9 = __CreateText(pageId);
    __SetClasses(el9, "stat-label");
    __AppendElement(el7, el9);
    const el10 = __CreateRawText("Total");
    __AppendElement(el9, el10);
    const el11 = __CreateView(pageId);
    __SetClasses(el11, "stat-item");
    __AppendElement(el6, el11);
    const el12 = __CreateWrapperElement(pageId);
    __AppendElement(el11, el12);
    const el13 = __CreateText(pageId);
    __SetClasses(el13, "stat-label");
    __AppendElement(el11, el13);
    const el14 = __CreateRawText("Pending");
    __AppendElement(el13, el14);
    const el15 = __CreateView(pageId);
    __SetClasses(el15, "stat-item");
    __AppendElement(el6, el15);
    const el16 = __CreateWrapperElement(pageId);
    __AppendElement(el15, el16);
    const el17 = __CreateText(pageId);
    __SetClasses(el17, "stat-label");
    __AppendElement(el15, el17);
    const el18 = __CreateRawText("Completed");
    __AppendElement(el17, el18);
    const el19 = __CreateView(pageId);
    __SetClasses(el19, "filter-section");
    __AppendElement(el2, el19);
    const el20 = __CreateText(pageId);
    __SetClasses(el20, "section-title");
    __AppendElement(el19, el20);
    const el21 = __CreateRawText("Filter & Sort");
    __AppendElement(el20, el21);
    const el22 = __CreateView(pageId);
    __SetClasses(el22, "filter-group");
    __AppendElement(el19, el22);
    const el23 = __CreateText(pageId);
    __SetClasses(el23, "filter-label");
    __AppendElement(el22, el23);
    const el24 = __CreateRawText("Filter by Category:");
    __AppendElement(el23, el24);
    const el25 = __CreateView(pageId);
    __SetClasses(el25, "filter-options");
    __AppendElement(el22, el25);
    const el26 = __CreateView(pageId);
    __AppendElement(el25, el26);
    const el27 = __CreateText(pageId);
    __SetClasses(el27, "filter-text");
    __AppendElement(el26, el27);
    const el28 = __CreateRawText("All");
    __AppendElement(el27, el28);
    const el29 = __CreateWrapperElement(pageId);
    __AppendElement(el25, el29);
    const el30 = __CreateView(pageId);
    __SetClasses(el30, "filter-group");
    __AppendElement(el19, el30);
    const el31 = __CreateText(pageId);
    __SetClasses(el31, "filter-label");
    __AppendElement(el30, el31);
    const el32 = __CreateRawText("Filter by Priority:");
    __AppendElement(el31, el32);
    const el33 = __CreateView(pageId);
    __SetClasses(el33, "filter-options");
    __AppendElement(el30, el33);
    const el34 = __CreateView(pageId);
    __AppendElement(el33, el34);
    const el35 = __CreateText(pageId);
    __SetClasses(el35, "filter-text");
    __AppendElement(el34, el35);
    const el36 = __CreateRawText("All");
    __AppendElement(el35, el36);
    const el37 = __CreateWrapperElement(pageId);
    __AppendElement(el33, el37);
    const el38 = __CreateView(pageId);
    __SetClasses(el38, "filter-group");
    __AppendElement(el19, el38);
    const el39 = __CreateText(pageId);
    __SetClasses(el39, "filter-label");
    __AppendElement(el38, el39);
    const el40 = __CreateRawText("Sort by:");
    __AppendElement(el39, el40);
    const el41 = __CreateWrapperElement(pageId);
    __AppendElement(el38, el41);
    const el42 = __CreateView(pageId);
    __SetClasses(el42, "category-stats");
    __AppendElement(el19, el42);
    const el43 = __CreateText(pageId);
    __SetClasses(el43, "filter-label");
    __AppendElement(el42, el43);
    const el44 = __CreateRawText("Category Overview:");
    __AppendElement(el43, el44);
    const el45 = __CreateWrapperElement(pageId);
    __AppendElement(el42, el45);
    const el46 = __CreateView(pageId);
    __SetClasses(el46, "add-task-section");
    __AppendElement(el2, el46);
    const el47 = __CreateText(pageId);
    __SetClasses(el47, "section-title");
    __AppendElement(el46, el47);
    const el48 = __CreateRawText("Add New Task");
    __AppendElement(el47, el48);
    const el49 = __CreateWrapperElement(pageId);
    __AppendElement(el46, el49);
    const el50 = __CreateView(pageId);
    __SetClasses(el50, "form-field");
    __AppendElement(el46, el50);
    const el51 = __CreateText(pageId);
    __SetClasses(el51, "field-label");
    __AppendElement(el50, el51);
    const el52 = __CreateRawText("Priority");
    __AppendElement(el51, el52);
    const el53 = __CreateWrapperElement(pageId);
    __AppendElement(el50, el53);
    const el54 = __CreateView(pageId);
    __SetClasses(el54, "form-field");
    __AppendElement(el46, el54);
    const el55 = __CreateText(pageId);
    __SetClasses(el55, "field-label");
    __AppendElement(el54, el55);
    const el56 = __CreateRawText("Category");
    __AppendElement(el55, el56);
    const el57 = __CreateView(pageId);
    __SetClasses(el57, "category-options");
    __AppendElement(el54, el57);
    const el58 = __CreateView(pageId);
    __AppendElement(el57, el58);
    const el59 = __CreateText(pageId);
    __SetClasses(el59, "category-text");
    __AppendElement(el58, el59);
    const el60 = __CreateRawText("No Category");
    __AppendElement(el59, el60);
    const el61 = __CreateWrapperElement(pageId);
    __AppendElement(el57, el61);
    const el62 = __CreateView(pageId);
    __SetClasses(el62, "form-field");
    __AppendElement(el46, el62);
    const el63 = __CreateText(pageId);
    __SetClasses(el63, "field-label");
    __AppendElement(el62, el63);
    const el64 = __CreateRawText("Tags");
    __AppendElement(el63, el64);
    const el65 = __CreateWrapperElement(pageId);
    __AppendElement(el62, el65);
    const el66 = __CreateView(pageId);
    __SetClasses(el66, "add-task-actions");
    __AppendElement(el46, el66);
    const el67 = __CreateView(pageId);
    __SetClasses(el67, "simple-input");
    __AppendElement(el66, el67);
    const el68 = __CreateWrapperElement(pageId);
    __AppendElement(el67, el68);
    const el69 = __CreateView(pageId);
    __AppendElement(el66, el69);
    const el70 = __CreateText(pageId);
    __SetClasses(el70, "button-text");
    __AppendElement(el69, el70);
    const el71 = __CreateRawText("Add Task");
    __AppendElement(el70, el71);
    const el72 = __CreateWrapperElement(pageId);
    __AppendElement(el2, el72);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17,
        el18,
        el19,
        el20,
        el21,
        el22,
        el23,
        el24,
        el25,
        el26,
        el27,
        el28,
        el29,
        el30,
        el31,
        el32,
        el33,
        el34,
        el35,
        el36,
        el37,
        el38,
        el39,
        el40,
        el41,
        el42,
        el43,
        el44,
        el45,
        el46,
        el47,
        el48,
        el49,
        el50,
        el51,
        el52,
        el53,
        el54,
        el55,
        el56,
        el57,
        el58,
        el59,
        el60,
        el61,
        el62,
        el63,
        el64,
        el65,
        el66,
        el67,
        el68,
        el69,
        el70,
        el71,
        el72
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[26], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 26, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[34], ctx.__values[2] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 34, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[58], ctx.__values[4] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 58, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[69], ctx.__values[6] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 69, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        8
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        12
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        16
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        29
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        37
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        41
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        45
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        49
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        53
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        61
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        65
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        68
    ],
    [
        (__webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        72
    ]
], undefined, globDynamicComponentEntry, null);
function App(props) {
    const [tasks, setTasks] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [newTaskTitle, setNewTaskTitle] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [selectedPriority, setSelectedPriority] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');
    const [selectedCategory, setSelectedCategory] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [selectedTags, setSelectedTags] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [filterCategory, setFilterCategory] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [filterPriority, setFilterPriority] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [filterTag, setFilterTag] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [sortBy, setSortBy] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('created');
    const categories = [
        {
            id: 'work',
            name: 'Work',
            icon: "\u{1F4BC}",
            color: '#3B82F6'
        },
        {
            id: 'personal',
            name: 'Personal',
            icon: "\u{1F3E0}",
            color: '#10B981'
        },
        {
            id: 'shopping',
            name: 'Shopping',
            icon: "\u{1F6D2}",
            color: '#F59E0B'
        },
        {
            id: 'health',
            name: 'Health',
            icon: "\u{1F3E5}",
            color: '#EF4444'
        },
        {
            id: 'learning',
            name: 'Learning',
            icon: "\u{1F4DA}",
            color: '#8B5CF6'
        },
        {
            id: 'finance',
            name: 'Finance',
            icon: "\u{1F4B0}",
            color: '#06B6D4'
        }
    ];
    const availableTags = [
        {
            id: 'urgent',
            name: 'Urgent',
            color: '#DC2626'
        },
        {
            id: 'important',
            name: 'Important',
            color: '#7C3AED'
        },
        {
            id: 'quick',
            name: 'Quick Task',
            color: '#059669'
        },
        {
            id: 'meeting',
            name: 'Meeting',
            color: '#2563EB'
        },
        {
            id: 'deadline',
            name: 'Deadline',
            color: '#EA580C'
        },
        {
            id: 'research',
            name: 'Research',
            color: '#0891B2'
        },
        {
            id: 'creative',
            name: 'Creative',
            color: '#C026D3'
        },
        {
            id: 'routine',
            name: 'Routine',
            color: '#65A30D'
        }
    ];
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        var _props_onMounted;
        console.info('Hello, Todo App with ReactLynx!');
        (_props_onMounted = props.onMounted) === null || _props_onMounted === void 0 ? void 0 : _props_onMounted.call(props);
    }, []);
    const addTask = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        if (newTaskTitle.trim()) {
            const newTask = {
                id: Date.now().toString(),
                title: newTaskTitle.trim(),
                completed: false,
                priority: selectedPriority,
                category: selectedCategory || undefined,
                tags: selectedTags,
                createdAt: new Date()
            };
            setTasks((prev)=>[
                    ...prev,
                    newTask
                ]);
            setNewTaskTitle('');
            setSelectedPriority('medium');
            setSelectedCategory('');
            setSelectedTags([]);
        }
    }, [
        newTaskTitle,
        selectedPriority,
        selectedCategory,
        selectedTags
    ]);
    const toggleTask = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{
        setTasks((prev)=>prev.map((task)=>task.id === id ? {
                    ...task,
                    completed: !task.completed
                } : task));
    }, []);
    const deleteTask = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{
        setTasks((prev)=>prev.filter((task)=>task.id !== id));
    }, []);
    // Filter and sort tasks
    const filteredTasks = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{
        let filtered = [
            ...tasks
        ];
        // Apply category filter
        if (filterCategory) filtered = filtered.filter((task)=>task.category === filterCategory);
        // Apply priority filter
        if (filterPriority) filtered = filtered.filter((task)=>task.priority === filterPriority);
        // Apply tag filter
        if (filterTag) filtered = filtered.filter((task)=>task.tags.includes(filterTag));
        // Sort tasks
        filtered.sort((a, b)=>{
            switch(sortBy){
                case 'priority':
                    const priorityOrder = {
                        high: 3,
                        medium: 2,
                        low: 1
                    };
                    return priorityOrder[b.priority] - priorityOrder[a.priority];
                case 'category':
                    const aCat = a.category || 'zzz';
                    const bCat = b.category || 'zzz';
                    return aCat.localeCompare(bCat);
                case 'created':
                default:
                    return b.createdAt.getTime() - a.createdAt.getTime();
            }
        });
        return filtered;
    }, [
        tasks,
        filterCategory,
        filterPriority,
        sortBy
    ]);
    const pendingTasks = filteredTasks.filter((task)=>!task.completed);
    const completedTasks = filteredTasks.filter((task)=>task.completed);
    // Get task statistics by category
    const categoryStats = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{
        return categories.map((category)=>{
            const categoryTasks = tasks.filter((task)=>task.category === category.id);
            return {
                ...category,
                total: categoryTasks.length,
                completed: categoryTasks.filter((task)=>task.completed).length,
                pending: categoryTasks.filter((task)=>!task.completed).length
            };
        });
    }, [
        tasks,
        categories
    ]);
    return /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_1, {
        values: [
            `filter-option ${!filterCategory ? 'selected' : ''}`,
            ()=>setFilterCategory(''),
            `filter-option ${!filterPriority ? 'selected' : ''}`,
            ()=>setFilterPriority(''),
            `category-option ${!selectedCategory ? 'selected' : ''}`,
            ()=>setSelectedCategory(''),
            `action-button primary ${!newTaskTitle.trim() ? 'disabled' : ''}`,
            !newTaskTitle.trim() ? undefined : addTask
        ],
        children: [
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_2, {
                children: tasks.length
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 146,
                columnNumber: 15
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_3, {
                children: pendingTasks.length
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 150,
                columnNumber: 15
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_4, {
                children: completedTasks.length
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 154,
                columnNumber: 15
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: categories.map((category)=>/*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_5, {
                        values: [
                            `filter-option ${filterCategory === category.id ? 'selected' : ''}`,
                            {
                                borderColor: category.color
                            },
                            ()=>setFilterCategory(category.id)
                        ],
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_6, {
                                children: category.icon
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 181,
                                columnNumber: 19
                            }, this),
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_7, {
                                children: category.name
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 182,
                                columnNumber: 19
                            }, this)
                        ]
                    }, category.id, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 175,
                        columnNumber: 17
                    }, this))
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: [
                    'high',
                    'medium',
                    'low'
                ].map((priority)=>/*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_8, {
                        values: [
                            `filter-option ${filterPriority === priority ? 'selected' : ''}`,
                            ()=>setFilterPriority(priority)
                        ],
                        children: priority.toUpperCase()
                    }, priority, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 199,
                        columnNumber: 17
                    }, this))
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_9, {
                children: [
                    {
                        id: 'created',
                        label: 'Created Date'
                    },
                    {
                        id: 'priority',
                        label: 'Priority'
                    },
                    {
                        id: 'category',
                        label: 'Category'
                    }
                ].map((option)=>/*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_10, {
                        values: [
                            `filter-option ${sortBy === option.id ? 'selected' : ''}`,
                            ()=>setSortBy(option.id)
                        ],
                        children: option.label
                    }, option.id, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 219,
                        columnNumber: 17
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 213,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_11, {
                children: categoryStats.map((stat)=>/*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_12, {
                        values: [
                            {
                                borderColor: stat.color
                            }
                        ],
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_13, {
                                children: stat.icon
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 236,
                                columnNumber: 19
                            }, this),
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_14, {
                                children: stat.name
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 237,
                                columnNumber: 19
                            }, this),
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: stat.pending
                            }, void 0, false, void 0, this),
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: stat.total
                            }, void 0, false, void 0, this)
                        ]
                    }, stat.id, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 235,
                        columnNumber: 17
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 233,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_15, {
                children: [
                    'Buy groceries',
                    'Call dentist',
                    'Review project',
                    'Exercise',
                    'Read book'
                ].map((template)=>/*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_16, {
                        values: [
                            ()=>setNewTaskTitle(template)
                        ],
                        children: template
                    }, template, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 258,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 250,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_17, {
                children: [
                    'low',
                    'medium',
                    'high'
                ].map((priority)=>/*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_18, {
                        values: [
                            `priority-option ${selectedPriority === priority ? 'selected' : ''}`,
                            ()=>setSelectedPriority(priority)
                        ],
                        children: priority.toUpperCase()
                    }, priority, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 273,
                        columnNumber: 17
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 271,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: categories.map((category)=>/*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_19, {
                        values: [
                            `category-option ${selectedCategory === category.id ? 'selected' : ''}`,
                            {
                                borderColor: category.color
                            },
                            ()=>setSelectedCategory(category.id)
                        ],
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_20, {
                                children: category.icon
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 301,
                                columnNumber: 19
                            }, this),
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_21, {
                                children: category.name
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 302,
                                columnNumber: 19
                            }, this)
                        ]
                    }, category.id, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 295,
                        columnNumber: 17
                    }, this))
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_22, {
                children: availableTags.map((tag)=>/*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_23, {
                        values: [
                            `tag-option ${selectedTags.includes(tag.id) ? 'selected' : ''}`,
                            {
                                backgroundColor: selectedTags.includes(tag.id) ? tag.color : 'transparent',
                                borderColor: tag.color
                            },
                            ()=>{
                                if (selectedTags.includes(tag.id)) setSelectedTags((prev)=>prev.filter((id)=>id !== tag.id));
                                else setSelectedTags((prev)=>[
                                        ...prev,
                                        tag.id
                                    ]);
                            },
                            {
                                color: selectedTags.includes(tag.id) ? 'white' : tag.color
                            }
                        ],
                        children: tag.name
                    }, tag.id, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 313,
                        columnNumber: 17
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 311,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_24, {
                children: newTaskTitle || 'Select a template above or tap here to enter custom task...'
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 344,
                columnNumber: 15
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_25, {
                children: [
                    pendingTasks.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_26, {
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: pendingTasks.length
                            }, void 0, false, void 0, this),
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_27, {
                                children: pendingTasks.map((task)=>{
                                    const category = categories.find((c)=>c.id === task.category);
                                    const priorityColor = task.priority === 'high' ? '#EF4444' : task.priority === 'medium' ? '#F59E0B' : '#10B981';
                                    return /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_28, {
                                        values: [
                                            {
                                                backgroundColor: priorityColor
                                            },
                                            ()=>toggleTask(task.id),
                                            ()=>deleteTask(task.id),
                                            {
                                                color: priorityColor
                                            }
                                        ],
                                        children: [
                                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_29, {
                                                children: task.title
                                            }, void 0, false, {
                                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                lineNumber: 383,
                                                columnNumber: 27
                                            }, this),
                                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                                children: category && /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_30, {
                                                    values: [
                                                        {
                                                            backgroundColor: category.color
                                                        }
                                                    ],
                                                    children: [
                                                        /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_31, {
                                                            children: category.icon
                                                        }, void 0, false, {
                                                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                            lineNumber: 399,
                                                            columnNumber: 31
                                                        }, this),
                                                        /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_32, {
                                                            children: category.name
                                                        }, void 0, false, {
                                                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                            lineNumber: 400,
                                                            columnNumber: 31
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                    lineNumber: 398,
                                                    columnNumber: 29
                                                }, this)
                                            }, void 0, false, void 0, this),
                                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_33, {
                                                children: task.priority.toUpperCase()
                                            }, void 0, false, {
                                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                lineNumber: 406,
                                                columnNumber: 29
                                            }, this),
                                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                                children: task.createdAt.toLocaleDateString()
                                            }, void 0, false, void 0, this)
                                        ]
                                    }, task.id, true, {
                                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                        lineNumber: 368,
                                        columnNumber: 21
                                    }, this);
                                })
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 361,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 359,
                        columnNumber: 13
                    }, this),
                    completedTasks.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_34, {
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: completedTasks.length
                            }, void 0, false, void 0, this),
                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_35, {
                                children: completedTasks.map((task)=>{
                                    const category = categories.find((c)=>c.id === task.category);
                                    const priorityColor = task.priority === 'high' ? '#EF4444' : task.priority === 'medium' ? '#F59E0B' : '#10B981';
                                    return /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_36, {
                                        values: [
                                            {
                                                backgroundColor: priorityColor,
                                                opacity: 0.5
                                            },
                                            ()=>toggleTask(task.id),
                                            ()=>deleteTask(task.id),
                                            {
                                                color: priorityColor,
                                                opacity: 0.7
                                            }
                                        ],
                                        children: [
                                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_37, {
                                                children: task.title
                                            }, void 0, false, {
                                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                lineNumber: 448,
                                                columnNumber: 27
                                            }, this),
                                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                                children: category && /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_38, {
                                                    values: [
                                                        {
                                                            backgroundColor: category.color,
                                                            opacity: 0.7
                                                        }
                                                    ],
                                                    children: [
                                                        /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_39, {
                                                            children: category.icon
                                                        }, void 0, false, {
                                                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                            lineNumber: 464,
                                                            columnNumber: 31
                                                        }, this),
                                                        /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_40, {
                                                            children: category.name
                                                        }, void 0, false, {
                                                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                            lineNumber: 465,
                                                            columnNumber: 31
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                    lineNumber: 463,
                                                    columnNumber: 29
                                                }, this)
                                            }, void 0, false, void 0, this),
                                            /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_41, {
                                                children: task.priority.toUpperCase()
                                            }, void 0, false, {
                                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                                lineNumber: 471,
                                                columnNumber: 29
                                            }, this)
                                        ]
                                    }, task.id, true, {
                                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                        lineNumber: 433,
                                        columnNumber: 21
                                    }, this);
                                })
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 426,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 424,
                        columnNumber: 13
                    }, this),
                    tasks.length === 0 && /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3ae6c_42, {}, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 489,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 356,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
        lineNumber: 138,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}


}),

};
exports.runtime = function(__webpack_require__) {
// webpack/runtime/get_full_hash
(() => {
__webpack_require__.h = () => ("b08e7d98de904245")
})();
// webpack/runtime/lynx css hot update
(() => {

__webpack_require__.cssHotUpdateList = [["main",".rspeedy/main/main.092f2cd1ec4fd088.css.hot-update.json"]];

})();

}
;
;

})();
    });
    return tt.require("main.092f2cd1ec4fd088.hot-update.js");
  };
  if (g && g.bundleSupportLoadScript){
    var res = {init: __init_card_bundle__};
    g.__bundle__holder = res;
    return res;
  } else {
    __init_card_bundle__({"tt": tt});
  };
})();

//# sourceMappingURL=http://**************:3000/main.092f2cd1ec4fd088.hot-update.js.map
{"version": 3, "file": "main__main-thread.7f49cdcf9e4f24f5.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx", "file://webpack/runtime/get_full_hash"], "sourcesContent": ["import { useState, useCallback } from '@lynx-js/react';\nimport type { Priority } from '../types/index.js';\nimport { useApp } from '../context/AppContext.js';\n\ninterface AddTaskFormProps {\n  onClose: () => void;\n  onTaskAdded?: () => void;\n}\n\nexport function AddTaskForm({ onClose, onTaskAdded }: AddTaskFormProps) {\n  const { addTask, state } = useApp();\n\n  const [title, setTitle] = useState('');\n  const [priority, setPriority] = useState<Priority>('medium');\n  const [categoryId, setCategoryId] = useState('');\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleSubmit = useCallback(() => {\n    if (!title.trim()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      // Create task with simplified data\n      const taskData = {\n        title: title.trim(),\n        status: 'pending' as const,\n        priority,\n        categoryId: categoryId || undefined,\n        tags: selectedTags,\n        subtasks: []\n      };\n\n      addTask(taskData);\n      onTaskAdded?.();\n      onClose();\n    } catch (error) {\n      console.error('Failed to create task:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  }, [title, priority, categoryId, selectedTags, addTask, onTaskAdded, onClose]);\n\n  const handleTagToggle = useCallback((tagId: string) => {\n    setSelectedTags(prev =>\n      prev.includes(tagId)\n        ? prev.filter(id => id !== tagId)\n        : [...prev, tagId]\n    );\n  }, []);\n\n  const handlePriorityChange = useCallback((newPriority: Priority) => {\n    setPriority(newPriority);\n  }, []);\n\n  const handleCategoryChange = useCallback((newCategoryId: string) => {\n    setCategoryId(newCategoryId);\n  }, []);\n\n  return (\n    <view className=\"add-task-form\">\n      <view className=\"form-header\">\n        <text className=\"form-title\">Add New Task</text>\n        <text className=\"close-button\" bindtap={onClose}>✕</text>\n      </view>\n\n      <view className=\"form-content\">\n        {/* Title Input (Simplified) */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Task Title *</text>\n          <view className=\"simple-input\">\n            <text className=\"input-text\">{title || 'Tap to enter title...'}</text>\n          </view>\n        </view>\n\n        {/* Priority Selection */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Priority</text>\n          <view className=\"priority-options\">\n            {(['low', 'medium', 'high'] as Priority[]).map(priorityOption => (\n              <view\n                key={priorityOption}\n                className={`priority-option ${priority === priorityOption ? 'selected' : ''}`}\n                bindtap={() => handlePriorityChange(priorityOption)}\n              >\n                <text className=\"priority-text\">{priorityOption.toUpperCase()}</text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Category Selection */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Category</text>\n          <view className=\"category-options\">\n            <view\n              className={`category-option ${!categoryId ? 'selected' : ''}`}\n              bindtap={() => handleCategoryChange('')}\n            >\n              <text className=\"category-text\">No Category</text>\n            </view>\n            {state.categories.map(category => (\n              <view\n                key={category.id}\n                className={`category-option ${categoryId === category.id ? 'selected' : ''}`}\n                style={{ borderColor: category.color }}\n                bindtap={() => handleCategoryChange(category.id)}\n              >\n                <text className=\"category-icon\">{category.icon}</text>\n                <text className=\"category-text\">{category.name}</text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Tags Selection */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Tags</text>\n          <view className=\"tag-options\">\n            {state.tags.map(tag => (\n              <view\n                key={tag.id}\n                className={`tag-option ${selectedTags.includes(tag.id) ? 'selected' : ''}`}\n                style={{\n                  backgroundColor: selectedTags.includes(tag.id) ? tag.color : 'transparent',\n                  borderColor: tag.color\n                }}\n                bindtap={() => handleTagToggle(tag.id)}\n              >\n                <text\n                  className=\"tag-text\"\n                  style={{\n                    color: selectedTags.includes(tag.id) ? 'white' : tag.color\n                  }}\n                >\n                  {tag.name}\n                </text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Quick Title Presets */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Quick Templates</text>\n          <view className=\"quick-templates\">\n            {[\n              'Buy groceries',\n              'Call dentist',\n              'Review project',\n              'Exercise',\n              'Read book'\n            ].map(template => (\n              <view\n                key={template}\n                className=\"template-option\"\n                bindtap={() => setTitle(template)}\n              >\n                <text className=\"template-text\">{template}</text>\n              </view>\n            ))}\n          </view>\n        </view>\n      </view>\n\n      {/* Actions */}\n      <view className=\"form-actions\">\n        <view className=\"action-button secondary\" bindtap={onClose}>\n          <text className=\"button-text\">Cancel</text>\n        </view>\n        <view\n          className={`action-button primary ${isSubmitting || !title.trim() ? 'disabled' : ''}`}\n          bindtap={isSubmitting || !title.trim() ? undefined : handleSubmit}\n        >\n          <text className=\"button-text\">\n            {isSubmitting ? 'Creating...' : 'Create Task'}\n          </text>\n        </view>\n      </view>\n    </view>\n  );\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "__webpack_require__.h = () => (\"6e818e5b23413278\")"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;;;;AAwEA;;;;;;;;;AAcA;;;;;;;;;;;;;;;;;;;;AAPA;;;;;;;;AA8BA;;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;;;;;;;;;;;;;;;;;;;;;AAZA;;;;;;;;AAqCA;;AAGA;;;;;;;;;;;;;;;;;AAbA;;;;;;;;AA6BA;;;;;;;;AAlHA;;AACA;;;AACA;;;;;AACA;;;;;AAGA;;;AAEA;;;AACA;;;;;AACA;;;;;AAMA;;;AACA;;;;;;;AAeA;;;AACA;;;;;AACA;;;;;AAKA;;;;;;;AAiBA;;;AACA;;;;;;;AA0BA;;;AACA;;;;;;;AAsBA;;;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlKA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAGA;AAAA;AAAA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;;;AAqCA;;;AA2EA;;;;AApGA;AAAA;;;;;;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;;AAGA;;;AAGA;AAJA;;;;;;;;;;;AAoBA;;AAGA;AACA;AAAA;AAAA;;;;AAGA;AAAA;;;;;;AACA;AAAA;;;;;;;AANA;;;;;;AAeA;AACA;;AAGA;AACA;AACA;AACA;AACA;;AAKA;AACA;AACA;;AAEA;AAdA;;;;;;;;;;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAMA;AAJA;;;;;;;;;;AAoBA;AACA;;;;;;;;;;;;AAMA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AC5OA"}
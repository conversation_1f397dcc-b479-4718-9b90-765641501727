{"version": 3, "file": ".rspeedy/main/main-thread.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\css-extract-webpack-plugin\\runtime\\hotModuleReplacement.lepus.cjs", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react-refresh-webpack-plugin\\runtime\\refresh.cjs", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\refresh\\dist\\index.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lepus\\jsx-runtime\\index.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\compat\\componentIs.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\compat\\initData.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\compat\\lynxComponent.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\debug\\profile.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\gesture\\processGesture.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\gesture\\types.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\hooks\\react.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\hooks\\useLynxGlobalEventListener.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\hydrate.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\index.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\internal.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycleConstant.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\destroy.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\event\\delayEvents.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\event\\delayLifecycleEvents.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\event\\jsReady.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\pass.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\patch\\commit.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\patch\\error.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\patch\\isMainThreadHydrationFinished.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\patch\\snapshotPatch.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\patch\\snapshotPatchApply.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\patch\\updateMainThread.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\ref\\delay.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\reload.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lifecycle\\render.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\list.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\listUpdateInfo.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx-api.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx\\calledByNative.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx\\component.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx\\dynamic-js.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx\\env.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx\\injectLepusMethods.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx\\lazy-bundle.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx\\performance.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\lynx\\runWithForce.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\opcodes.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\pendingListUpdates.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\renderToOpcodes\\constants.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\renderToOpcodes\\index.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\root.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshotInstanceHydrationMap.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot\\dynamicPartType.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot\\event.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot\\gesture.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot\\list.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot\\platformInfo.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot\\ref.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot\\spread.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot\\workletEvent.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\snapshot\\workletRef.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\utils.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\destroy.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\functionCall.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\functionality.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\hmr.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\indexMap.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\runOnBackground.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\runOnMainThread.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\transformToWorklet.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\workletRef.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\runtime\\lib\\worklet\\workletRefPool.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\worklet-runtime\\lib\\bindings\\bindings.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\worklet-runtime\\lib\\bindings\\events.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\worklet-runtime\\lib\\bindings\\index.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\worklet-runtime\\lib\\bindings\\loadRuntime.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\worklet-runtime\\lib\\bindings\\observers.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\@lynx-js\\react\\worklet-runtime\\lib\\global.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\Icon.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\createLucideIcon.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\defaultAttributes.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\book-open.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\briefcase.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\dollar-sign.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\download.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\heart.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\house.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\plus.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\repeat.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\shopping-cart.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\square-check-big.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\square.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\trash-2.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\icons\\upload.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\lucide-react\\dist\\esm\\shared\\src\\utils.js", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\preact\\compat\\dist\\compat.mjs", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\preact\\dist\\preact.mjs", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\node_modules\\preact\\hooks\\dist\\hooks.mjs", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\index.tsx", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.css?4e00", "file://webpack/runtime/compat_get_default_export", "file://webpack/runtime/define_property_getters", "file://webpack/runtime/esm_module_decorator", "file://webpack/runtime/get_chunk_update_filename", "file://webpack/runtime/get_full_hash", "file://webpack/runtime/get_main_filename/update manifest", "file://webpack/runtime/has_own_property", "file://webpack/runtime/hot_module_replacement", "file://webpack/runtime/lynx css hot update", "file://webpack/runtime/make_namespace_object", "file://webpack/runtime/public_path", "file://webpack/runtime/Lynx async chunks", "file://webpack/runtime/Lynx chunk loading"], "sourcesContent": ["function main() {\n  try {\n    lynx.getJSContext().addEventListener('lynx.hmr.css', (event) => {\n      try {\n        const { data: { cssId, content, deps, entry } } = event;\n        // Update the css deps first because the css deps are updated actually.\n        if (Array.isArray(deps[cssId])) {\n          deps[cssId].forEach(depCSSId => {\n            lynx.getDevtool().replaceStyleSheetByIdWithBase64(\n              Number(depCSSId),\n              content,\n              entry,\n            );\n          });\n        }\n\n        lynx.getDevtool().replaceStyleSheetByIdWithBase64(\n          Number(cssId),\n          content,\n          entry,\n        );\n\n        __FlushElementTree();\n      } catch (error) {\n        // TODO: use webpack-dev-server logger\n        console.error(error);\n      }\n    });\n  } catch (error) {\n    // TODO: use webpack-dev-server logger\n    console.warn(`[HMR] no lynx.getJSContext() found, will not HMR CSS`);\n    console.warn(error);\n  }\n}\n\nmain();\n", "const { isComponent, flush } = require('@lynx-js/react/refresh');\n\n// eslint-disable-next-line\nconst getExports = m => m.exports || m.__proto__.exports;\n\nfunction isSafeExport(key) {\n  return (\n    key === '__esModule'\n    || key === '__N_SSG'\n    || key === '__N_SSP'\n    || key === 'config'\n  );\n}\n\nfunction registerExports(moduleExports, moduleId) {\n  globalThis['__PREFRESH__'].register(moduleExports, moduleId + ' %exports%');\n  if (moduleExports == null || typeof moduleExports !== 'object') return;\n\n  for (const key in moduleExports) {\n    if (isSafeExport(key)) continue;\n    const exportValue = moduleExports[key];\n    const typeID = moduleId + ' %exports% ' + key;\n    globalThis['__PREFRESH__'].register(exportValue, typeID);\n  }\n}\n\nconst shouldBind = m => {\n  let isCitizen = false;\n  const moduleExports = getExports(m);\n\n  if (isComponent(moduleExports)) {\n    isCitizen = true;\n  }\n\n  if (\n    moduleExports === undefined\n    || moduleExports === null\n    || typeof moduleExports !== 'object'\n  ) {\n    isCitizen = isCitizen || false;\n  } else {\n    for (const key in moduleExports) {\n      if (key === '__esModule') continue;\n\n      const exportValue = moduleExports[key];\n      if (isComponent(exportValue)) {\n        isCitizen = isCitizen || true;\n      }\n    }\n  }\n\n  return isCitizen;\n};\n\nmodule.exports = Object.freeze({\n  getExports,\n  shouldBind,\n  flush,\n  registerExports,\n});\n", "import { Component, options } from \"@lynx-js/react/internal\";\nconst VNODE_COMPONENT = '__c';\nconst NAMESPACE = '__PREFRESH__';\nconst COMPONENT_HOOKS = '__H';\nconst HOOKS_LIST = '__';\nconst EFFECTS_LIST = '__h';\nconst RERENDER_COUNT = '__r';\nconst CATCH_ERROR_OPTION = '__e';\nconst COMPONENT_DIRTY = '__d';\nconst HOOK_VALUE = '__';\nconst HOOK_ARGS = '__H';\nconst HOOK_CLEANUP = '__c';\nconst oldCatchError = options[CATCH_ERROR_OPTION];\noptions[CATCH_ERROR_OPTION] = (error, vnode, oldVNode)=>{\n    if (vnode[VNODE_COMPONENT] && vnode[VNODE_COMPONENT][COMPONENT_DIRTY]) vnode[VNODE_COMPONENT][COMPONENT_DIRTY] = false;\n    if (oldCatchError) oldCatchError(error, vnode, oldVNode);\n};\nconst defer = 'function' == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout;\noptions.debounceRendering = (process)=>{\n    defer(()=>{\n        try {\n            process();\n        } catch (e) {\n            process[RERENDER_COUNT] = 0;\n            throw e;\n        }\n    });\n};\nconst vnodesForComponent = new WeakMap();\nconst mappedVNodes = new WeakMap();\nconst lastSeen = new Map();\nconst getMappedVnode = (type)=>{\n    if (mappedVNodes.has(type)) return getMappedVnode(mappedVNodes.get(type));\n    return type;\n};\nconst BUILT_IN_COMPONENTS = [\n    'Fragment',\n    'Suspense',\n    'SuspenseList'\n];\nconst isBuiltIn = (type)=>BUILT_IN_COMPONENTS.includes(type.name);\nconst oldVnode = options.vnode;\noptions.vnode = (vnode)=>{\n    if (vnode && 'function' == typeof vnode.type && !isBuiltIn(vnode.type)) {\n        const vnodes = vnodesForComponent.get(vnode.type);\n        if (vnodes) vnodes.push(vnode);\n        else vnodesForComponent.set(vnode.type, [\n            vnode\n        ]);\n        const foundType = getMappedVnode(vnode.type);\n        if (foundType !== vnode.type) {\n            const vnodes = vnodesForComponent.get(foundType);\n            if (vnodes) vnodes.push(vnode);\n            else vnodesForComponent.set(foundType, [\n                vnode\n            ]);\n        }\n        vnode.type = foundType;\n        if (vnode[VNODE_COMPONENT] && 'prototype' in vnode.type && vnode.type.prototype.render) vnode[VNODE_COMPONENT].constructor = vnode.type;\n    }\n    if (oldVnode) oldVnode(vnode);\n};\nconst oldDiffed = options.diffed;\noptions.diffed = (vnode)=>{\n    if (vnode && 'function' == typeof vnode.type) {\n        const vnodes = vnodesForComponent.get(vnode.type);\n        lastSeen.set(vnode.__v, vnode);\n        if (vnodes) {\n            const matchingDom = vnodes.filter((p)=>p.__c === vnode.__c);\n            if (matchingDom.length > 1) {\n                const i = vnodes.findIndex((p)=>p === matchingDom[0]);\n                vnodes.splice(i, 1);\n            }\n        }\n    }\n    if (oldDiffed) oldDiffed(vnode);\n};\nconst oldUnmount = options.unmount;\noptions.unmount = (vnode)=>{\n    const type = (vnode || {}).type;\n    if ('function' == typeof type && vnodesForComponent.has(type)) {\n        const vnodes = vnodesForComponent.get(type);\n        if (vnodes) {\n            const index = vnodes.indexOf(vnode);\n            if (-1 !== index) vnodes.splice(index, 1);\n        }\n    }\n    if (oldUnmount) oldUnmount(vnode);\n};\nconst signaturesForType = new WeakMap();\nconst computeKey = (signature)=>{\n    let fullKey = signature.key;\n    let hooks;\n    try {\n        hooks = signature.getCustomHooks();\n    } catch (err) {\n        signature.forceReset = true;\n        return fullKey;\n    }\n    for(let i = 0; i < hooks.length; i++){\n        const hook = hooks[i];\n        if ('function' != typeof hook) {\n            signature.forceReset = true;\n            break;\n        }\n        const nestedHookSignature = signaturesForType.get(hook);\n        if (void 0 === nestedHookSignature) continue;\n        const nestedHookKey = computeKey(nestedHookSignature);\n        if (nestedHookSignature.forceReset) signature.forceReset = true;\n        fullKey += '\\n---\\n' + nestedHookKey;\n    }\n    return fullKey;\n};\nlet typesById = new Map();\nlet pendingUpdates = [];\nfunction sign(type, key, forceReset, getCustomHooks, status) {\n    if (type) {\n        let signature = signaturesForType.get(type);\n        if ('begin' === status) {\n            signaturesForType.set(type, {\n                type,\n                key,\n                forceReset,\n                getCustomHooks: getCustomHooks || (()=>[])\n            });\n            return 'needsHooks';\n        }\n        if ('needsHooks' === status) signature.fullKey = computeKey(signature);\n    }\n}\nfunction replaceComponent(OldType, NewType, resetHookState) {\n    const vnodes = vnodesForComponent.get(OldType);\n    if (!vnodes) return;\n    vnodesForComponent[\"delete\"](OldType);\n    vnodesForComponent.set(NewType, vnodes);\n    mappedVNodes.set(OldType, NewType);\n    pendingUpdates = pendingUpdates.filter((p)=>p[0] !== OldType);\n    vnodes.forEach((node)=>{\n        let vnode = node;\n        if (!vnode.__c) {\n            vnode = lastSeen.get(vnode.__v, vnode);\n            lastSeen[\"delete\"](vnode.__v);\n        }\n        if (!vnode || !vnode.__c || !vnode.__c.__P) return;\n        vnode.type = NewType;\n        if (vnode[VNODE_COMPONENT]) {\n            vnode[VNODE_COMPONENT].constructor = vnode.type;\n            try {\n                if (vnode[VNODE_COMPONENT] instanceof OldType) {\n                    const oldInst = vnode[VNODE_COMPONENT];\n                    const newInst = new NewType(vnode[VNODE_COMPONENT].props, vnode[VNODE_COMPONENT].context);\n                    vnode[VNODE_COMPONENT] = newInst;\n                    for(let i in oldInst){\n                        const type = typeof oldInst[i];\n                        if (i in newInst) {\n                            if ('function' !== type && typeof newInst[i] === type) if ('object' === type && null != newInst[i] && newInst[i].constructor === oldInst[i].constructor) Object.assign(newInst[i], oldInst[i]);\n                            else newInst[i] = oldInst[i];\n                        } else newInst[i] = oldInst[i];\n                    }\n                }\n            } catch (e) {\n                vnode[VNODE_COMPONENT].constructor = NewType;\n            }\n            if (resetHookState) {\n                if (vnode[VNODE_COMPONENT][COMPONENT_HOOKS] && vnode[VNODE_COMPONENT][COMPONENT_HOOKS][HOOKS_LIST] && vnode[VNODE_COMPONENT][COMPONENT_HOOKS][HOOKS_LIST].length) vnode[VNODE_COMPONENT][COMPONENT_HOOKS][HOOKS_LIST].forEach((possibleEffect)=>{\n                    if (possibleEffect[HOOK_CLEANUP] && 'function' == typeof possibleEffect[HOOK_CLEANUP]) {\n                        possibleEffect[HOOK_CLEANUP]();\n                        possibleEffect[HOOK_CLEANUP] = void 0;\n                    } else if (possibleEffect[HOOK_ARGS] && possibleEffect[HOOK_VALUE] && 3 === Object.keys(possibleEffect).length) {\n                        const cleanupKey = Object.keys(possibleEffect).find((key)=>key !== HOOK_ARGS && key !== HOOK_VALUE);\n                        if (cleanupKey && 'function' == typeof possibleEffect[cleanupKey]) {\n                            possibleEffect[cleanupKey]();\n                            possibleEffect[cleanupKey] = void 0;\n                        }\n                    }\n                });\n                vnode[VNODE_COMPONENT][COMPONENT_HOOKS] = {\n                    [HOOKS_LIST]: [],\n                    [EFFECTS_LIST]: []\n                };\n            } else if (vnode[VNODE_COMPONENT][COMPONENT_HOOKS] && vnode[VNODE_COMPONENT][COMPONENT_HOOKS][HOOKS_LIST] && vnode[VNODE_COMPONENT][COMPONENT_HOOKS][HOOKS_LIST].length) {\n                vnode[VNODE_COMPONENT][COMPONENT_HOOKS][HOOKS_LIST].forEach((possibleEffect)=>{\n                    if (possibleEffect[HOOK_CLEANUP] && 'function' == typeof possibleEffect[HOOK_CLEANUP]) {\n                        possibleEffect[HOOK_CLEANUP]();\n                        possibleEffect[HOOK_CLEANUP] = void 0;\n                    } else if (possibleEffect[HOOK_ARGS] && possibleEffect[HOOK_VALUE] && 3 === Object.keys(possibleEffect).length) {\n                        const cleanupKey = Object.keys(possibleEffect).find((key)=>key !== HOOK_ARGS && key !== HOOK_VALUE);\n                        if (cleanupKey && 'function' == typeof possibleEffect[cleanupKey]) possibleEffect[cleanupKey]();\n                        possibleEffect[cleanupKey] = void 0;\n                    }\n                });\n                vnode[VNODE_COMPONENT][COMPONENT_HOOKS][HOOKS_LIST].forEach((hook)=>{\n                    if (hook.__H && Array.isArray(hook.__H)) hook.__H = void 0;\n                });\n            }\n            Component.prototype.forceUpdate.call(vnode[VNODE_COMPONENT]);\n        }\n    });\n}\nglobalThis[NAMESPACE] = {\n    getSignature: (type)=>signaturesForType.get(type),\n    register: (type, id)=>{\n        if ('function' != typeof type) return;\n        if (typesById.has(id)) {\n            const existing = typesById.get(id);\n            if (existing !== type) {\n                pendingUpdates.push([\n                    existing,\n                    type\n                ]);\n                typesById.set(id, type);\n            }\n        } else typesById.set(id, type);\n        if (!signaturesForType.has(type)) signaturesForType.set(type, {\n            getCustomHooks: ()=>[],\n            type\n        });\n    },\n    getPendingUpdates: ()=>pendingUpdates,\n    flush: ()=>{\n        pendingUpdates = [];\n    },\n    replaceComponent,\n    sign,\n    computeKey: computeKey\n};\nconst compareSignatures = (prev, next)=>{\n    const prevSignature = globalThis.__PREFRESH__.getSignature(prev) || {};\n    const nextSignature = globalThis.__PREFRESH__.getSignature(next) || {};\n    if (prevSignature.key !== nextSignature.key || globalThis.__PREFRESH__.computeKey(prevSignature) !== globalThis.__PREFRESH__.computeKey(nextSignature) || nextSignature.forceReset) globalThis.__PREFRESH__.replaceComponent(prev, next, true);\n    else globalThis.__PREFRESH__.replaceComponent(prev, next, false);\n};\nconst flush = ()=>{\n    const pending = [\n        ...globalThis.__PREFRESH__.getPendingUpdates()\n    ];\n    globalThis.__PREFRESH__.flush();\n    if (pending.length > 0) pending.forEach(([prev, next])=>{\n        compareSignatures(prev, next);\n    });\n};\nconst isComponent = (exportValue)=>{\n    if ('function' == typeof exportValue) {\n        if (null != exportValue.prototype && exportValue.prototype.isReactComponent) return true;\n        const name = exportValue.name || exportValue.displayName;\n        return 'string' == typeof name && name[0] && name[0] == name[0].toUpperCase();\n    }\n    return false;\n};\nexport { flush, isComponent };\n", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { SnapshotInstance } from '@lynx-js/react/internal';\n\nfunction createVNode(type, props, _key) {\n  if (typeof type === 'string') {\n    const r = new SnapshotInstance(type);\n\n    r.props = props;\n\n    r.__k = null;\n    r.__ = null;\n    r.__b = 0;\n    r.__e = null;\n    r.__d = undefined;\n    r.__c = null;\n    // r.__v = --vnodeId;\n    r.__i = -1;\n    r.__u = 0;\n\n    return r;\n  } else if (typeof type === 'function') {\n    let normalizedProps = props;\n\n    // let ref;\n    if ('ref' in normalizedProps) {\n      normalizedProps = {};\n      for (let i in props) {\n        if (i == 'ref') {\n          // ref = props[i];\n        } else {\n          normalizedProps[i] = props[i];\n        }\n      }\n    }\n\n    let defaultProps;\n    if ((defaultProps = type.defaultProps)) {\n      for (let i in defaultProps) {\n        if (typeof normalizedProps[i] === 'undefined') {\n          normalizedProps[i] = defaultProps[i];\n        }\n      }\n    }\n\n    return {\n      type,\n      props: normalizedProps,\n\n      __k: null,\n      __: null,\n      __b: 0,\n      __e: null,\n      __d: void 0,\n      __c: null,\n      constructor: void 0,\n      // __v: --vnodeId,\n      __i: -1,\n      __u: 0,\n    };\n  }\n}\n\nexport { createVNode as jsx, createVNode as jsxs, createVNode as jsxDEV };\nexport { Fragment } from 'preact';\n", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n// for better reuse if runtime is changed\nexport function factory({ createElement, useMemo, Suspense, lazy }, loadLazyBundle) {\n    /**\n     * @internal a polyfill for <component is=? />\n     */\n    const __ComponentIsPolyfill = ({ is, ...props }) => {\n        if (typeof is !== 'string') {\n            lynx.reportError(new Error('You must provide a string to props `is` when using syntax `<component is=? />`.'));\n            return null;\n        }\n        // @ts-ignore\n        const D = useMemo(() => lazy(() => loadLazyBundle(is)), [is]);\n        return createElement(Suspense, { key: is }, createElement(D, props));\n    };\n    return __ComponentIsPolyfill;\n}\n//# sourceMappingURL=componentIs.js.map", "import { useLynxGlobalEventListener } from '../hooks/useLynxGlobalEventListener.js';\nimport { globalFlushOptions } from '../lifecycle/patch/commit.js';\n// for better reuse if runtime is changed\nexport function factory({ createContext, useState, createElement, useLynxGlobalEventListener: useListener }, prop, eventName) {\n    const Context = createContext({});\n    const Provider = ({ children }) => {\n        const [__, set] = useState(lynx[prop]);\n        const handleChange = () => {\n            if (prop === '__initData') {\n                globalFlushOptions.triggerDataUpdated = true;\n            }\n            set(lynx[prop]);\n        };\n        useChanged(handleChange);\n        return createElement(Context.Provider, {\n            value: __,\n        }, children);\n    };\n    const Consumer = Context.Consumer;\n    const use = () => {\n        const [__, set] = useState(lynx[prop]);\n        useChanged(() => {\n            if (prop === '__initData') {\n                globalFlushOptions.triggerDataUpdated = true;\n            }\n            set(lynx[prop]);\n        });\n        return __;\n    };\n    const useChanged = (callback) => {\n        if (!__LEPUS__) {\n            useListener(eventName, callback);\n        }\n    };\n    return {\n        /* v8 ignore next */\n        Context: () => Context,\n        Provider: () => Provider,\n        Consumer: () => Consumer,\n        use: () => use,\n        useChanged: () => useChanged,\n    };\n}\n/**\n * Higher-Order Component (HOC) that injects `initData` into the state of the given class component.\n *\n * This HOC checks if the provided component is a class component. If it is, it wraps the component\n * and injects the `initData` into its state. It also adds a listener\n * to update the state when data changes, and removes the listener when the component unmounts.\n *\n * @typeParam P - The type of the props of the wrapped component.\n * @typeParam S - The type of the state of the wrapped component.\n *\n * @param App - The class component to be wrapped by the HOC.\n *\n * @returns The original component if it is not a class component, otherwise a new class component\n *          with `initData` injection and state update functionality.\n *\n * @example\n * ```typescript\n * class App extends React.Component<MyProps, MyState> {\n *   // component implementation\n * }\n *\n * export default withInitDataInState(App);\n * ```\n * @public\n */\nexport function withInitDataInState(App) {\n    const isClassComponent = 'prototype' in App && 'render' in App.prototype;\n    /* v8 ignore next 4 */\n    if (!isClassComponent) {\n        // return as-is when not class component\n        return App;\n    }\n    class C extends App {\n        h;\n        constructor(props) {\n            super(props);\n            this.state = {\n                ...this.state,\n                ...lynx.__initData,\n            };\n            if (!__LEPUS__) {\n                lynx.getJSModule('GlobalEventEmitter').addListener('onDataChanged', this.h = () => {\n                    globalFlushOptions.triggerDataUpdated = true;\n                    this.setState(lynx.__initData);\n                });\n            }\n        }\n        componentWillUnmount() {\n            super.componentWillUnmount?.();\n            if (!__LEPUS__) {\n                lynx.getJSModule('GlobalEventEmitter').removeListener('onDataChanged', this.h);\n            }\n        }\n    }\n    return C;\n}\n//# sourceMappingURL=initData.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { Component } from 'preact';\nexport function wrapWithLynxComponent(jsxSnapshot, jsxComponent) {\n    const C = jsxComponent.type;\n    if (typeof C === 'function' && (C === ComponentFromReactRuntime || C.prototype instanceof ComponentFromReactRuntime)) {\n        if (jsxSnapshot.length === 1) {\n            return jsxSnapshot(jsxComponent);\n        }\n        else {\n            // spread\n            if (!jsxComponent.props.removeComponentElement) {\n                return jsxSnapshot(jsxComponent, takeComponentAttributes(jsxComponent));\n            }\n        }\n    }\n    return jsxComponent;\n}\n// @ts-expect-error\nexport class ComponentFromReactRuntime extends Component {\n}\nconst __COMPONENT_ATTRIBUTES__ = /* @__PURE__ */ new Set([\n    'name',\n    'style',\n    'class',\n    'flatten',\n    'clip-radius',\n    'overlap',\n    'user-interaction-enabled',\n    'native-interaction-enabled',\n    'block-native-event',\n    'enableLayoutOnly',\n    'cssAlignWithLegacyW3C',\n    'intersection-observers',\n    'trigger-global-event',\n    'exposure-scene',\n    'exposure-id',\n    'exposure-screen-margin-top',\n    'exposure-screen-margin-bottom',\n    'exposure-screen-margin-left',\n    'exposure-screen-margin-right',\n    'focusable',\n    'focus-index',\n    'accessibility-label',\n    'accessibility-element',\n    'accessibility-traits',\n    'enable-new-animator',\n]);\nfunction takeComponentAttributes(jsxComponent) {\n    const attributes = {};\n    Object.keys(jsxComponent.props).forEach((k) => {\n        // let re1 = Regex::new(r\"^(global-bind|bind|catch|capture-bind|capture-catch)([A-Za-z]+)$\").unwrap();\n        // let re2 = Regex::new(r\"^data-([A-Za-z]+)$\").unwrap();\n        if (__COMPONENT_ATTRIBUTES__.has(k)\n            || k === 'id'\n            || k === 'className'\n            || k === 'dataSet'\n            || k === 'data-set'\n            || k === 'removeComponentElement'\n            || (/^(global-bind|bind|catch|capture-bind|capture-catch)([A-Za-z]+)$/.exec(k))\n            || (/^data-([A-Za-z]+)$/.exec(k))) {\n            attributes[k] = jsxComponent.props[k];\n            delete jsxComponent.props[k];\n        }\n    });\n    return attributes;\n}\n//# sourceMappingURL=lynxComponent.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { options } from 'preact';\nimport { COMPONENT, DIFF, DIFFED, RENDER } from '../renderToOpcodes/constants.js';\nexport function initProfileHook() {\n    const oldDiff = options[DIFF];\n    options[DIFF] = function (vnode) {\n        // This __PROFILE__ is used for DCE testing\n        if (__PROFILE__ && typeof vnode.type === 'function') {\n            // We only add profiling trace for Component\n            console.profile(`diff::${getDisplayName(vnode.type)}`);\n        }\n        oldDiff?.(vnode);\n    };\n    const oldDiffed = options[DIFFED];\n    options[DIFFED] = function (vnode) {\n        // This __PROFILE__ is used for DCE testing\n        if (__PROFILE__ && typeof vnode.type === 'function') {\n            console.profileEnd(); // for options[DIFF]\n        }\n        oldDiffed?.(vnode);\n    };\n    // Profile the user-provided `render`.\n    const oldRender = options[RENDER];\n    options[RENDER] = function (vnode) {\n        const displayName = getDisplayName(vnode.type);\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        const originalRender = vnode[COMPONENT].render;\n        vnode[COMPONENT].render = function render(props, state, context) {\n            // This __PROFILE__ is used for DCE testing\n            if (__PROFILE__) {\n                console.profile(`render::${displayName}`);\n            }\n            try {\n                return originalRender.call(this, props, state, context);\n            }\n            finally {\n                // This __PROFILE__ is used for DCE testing\n                if (__PROFILE__) {\n                    console.profileEnd();\n                }\n                vnode[COMPONENT].render = originalRender;\n            }\n        };\n        oldRender?.(vnode);\n    };\n}\nfunction getDisplayName(type) {\n    return type.displayName ?? type.name;\n}\n//# sourceMappingURL=profile.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { onWorkletCtxUpdate } from '@lynx-js/react/worklet-runtime/bindings';\nimport { GestureTypeInner } from './types.js';\nfunction isSerializedGesture(gesture) {\n    return gesture.__isSerialized ?? false;\n}\nfunction getGestureInfo(gesture, oldGesture, isFirstScreen, dom) {\n    const config = {\n        callbacks: [],\n    };\n    const baseGesture = gesture;\n    if (baseGesture.config) {\n        config.config = baseGesture.config;\n    }\n    for (const key of Object.keys(baseGesture.callbacks)) {\n        const callback = baseGesture.callbacks[key];\n        const oldCallback = oldGesture?.callbacks[key];\n        onWorkletCtxUpdate(callback, oldCallback, isFirstScreen, dom);\n        config.callbacks.push({\n            name: key,\n            callback: callback,\n        });\n    }\n    const relationMap = {\n        waitFor: baseGesture?.waitFor?.map(subGesture => subGesture.id) ?? [],\n        simultaneous: baseGesture?.simultaneousWith?.map(subGesture => subGesture.id) ?? [],\n        continueWith: baseGesture?.continueWith?.map(subGesture => subGesture.id) ?? [],\n    };\n    return {\n        config,\n        relationMap,\n    };\n}\nexport function processGesture(dom, gesture, oldGesture, isFirstScreen, gestureOptions) {\n    if (!gesture || !isSerializedGesture(gesture)) {\n        return;\n    }\n    if (!(gestureOptions && gestureOptions.domSet)) {\n        __SetAttribute(dom, 'has-react-gesture', true);\n        __SetAttribute(dom, 'flatten', false);\n    }\n    if (gesture.type === GestureTypeInner.COMPOSED) {\n        for (const [index, subGesture] of gesture.gestures.entries()) {\n            processGesture(dom, subGesture, oldGesture?.gestures[index], isFirstScreen, {\n                domSet: true,\n            });\n        }\n    }\n    else {\n        const baseGesture = gesture;\n        const oldBaseGesture = oldGesture;\n        const { config, relationMap } = getGestureInfo(baseGesture, oldBaseGesture, isFirstScreen, dom);\n        __SetGestureDetector(dom, baseGesture.id, baseGesture.type, config, relationMap);\n    }\n}\n//# sourceMappingURL=processGesture.js.map", "export var GestureTypeInner;\n(function (GestureTypeInner) {\n    GestureTypeInner[GestureTypeInner[\"COMPOSED\"] = -1] = \"COMPOSED\";\n    GestureTypeInner[GestureTypeInner[\"PAN\"] = 0] = \"PAN\";\n    GestureTypeInner[GestureTypeInner[\"FLING\"] = 1] = \"FLING\";\n    GestureTypeInner[GestureTypeInner[\"DEFAULT\"] = 2] = \"DEFAULT\";\n    GestureTypeInner[GestureTypeInner[\"TAP\"] = 3] = \"TAP\";\n    GestureTypeInner[GestureTypeInner[\"LONGPRESS\"] = 4] = \"LONGPRESS\";\n    GestureTypeInner[GestureTypeInner[\"ROTATION\"] = 5] = \"ROTATION\";\n    GestureTypeInner[GestureTypeInner[\"PINCH\"] = 6] = \"PINCH\";\n    GestureTypeInner[GestureTypeInner[\"NATIVE\"] = 7] = \"NATIVE\";\n})(GestureTypeInner || (GestureTypeInner = {}));\n//# sourceMappingURL=types.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { useCallback, useContext, useDebugValue, useErrorBoundary, useId, useImperativeHandle, useMemo, useEffect as usePreactEffect, useReducer, useRef, useState, } from 'preact/hooks';\n/**\n * `useLayoutEffect` is now an alias of `useEffect`. Use `useEffect` instead.\n *\n * Accepts a function that contains imperative, possibly effectful code. The effects run after main thread dom update without blocking it.\n *\n * @param effect - Imperative function that can return a cleanup function\n * @param deps - If present, effect will only activate if the values in the list change (using ===).\n *\n * @public\n *\n * @deprecated `useLayoutEffect` in the background thread cannot offer the precise timing for reading layout information and synchronously re-render, which is different from React.\n */\nfunction useLayoutEffect(effect, deps) {\n    return usePreactEffect(effect, deps);\n}\n/**\n * Accepts a function that contains imperative, possibly effectful code.\n * The effects run after main thread dom update without blocking it.\n *\n * @param effect - Imperative function that can return a cleanup function\n * @param deps - If present, effect will only activate if the values in the list change (using ===).\n *\n * @public\n */\nfunction useEffect(effect, deps) {\n    return usePreactEffect(effect, deps);\n}\nexport { \n// preact\nuseState, useReducer, useRef, useImperativeHandle, useLayoutEffect, useEffect, useCallback, useMemo, useContext, useDebugValue, useErrorBoundary, useId, };\n//# sourceMappingURL=react.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { useEffect, useMemo, useRef } from 'preact/hooks';\n/**\n * `useLynxGlobalEventListener` help you `addListener` as early as possible.\n *\n * @example\n *\n * Use this hooks to listen to event 'exposure' and event 'disexposure'\n *\n * ```jsx\n * function App() {\n *   useLynxGlobalEventListener('exposure', (e) => {\n *     console.log(\"exposure\", e)\n *   })\n *   useLynxGlobalEventListener('disexposure', (e) => {\n *     console.log(\"disexposure\", e)\n *   })\n *   return (\n *     <view\n *       style='width: 100px; height: 100px; background-color: red;'\n *       exposure-id='a'\n *     />\n *   )\n * }\n * ```\n *\n * @param eventName - Event name to listen\n * @param listener - Event handler\n * @public\n */\nexport function useLynxGlobalEventListener(eventName, listener) {\n    'background only';\n    const previousArgsRef = useRef();\n    useMemo(() => {\n        if (previousArgsRef.current) {\n            const [eventName, listener] = previousArgsRef.current;\n            lynx.getJSModule('GlobalEventEmitter').removeListener(eventName, listener);\n        }\n        lynx.getJSModule('GlobalEventEmitter').addListener(eventName, listener);\n        previousArgsRef.current = [eventName, listener];\n    }, [eventName, listener]);\n    useEffect(() => {\n        return () => {\n            if (previousArgsRef.current) {\n                const [eventName, listener] = previousArgsRef.current;\n                lynx.getJSModule('GlobalEventEmitter').removeListener(eventName, listener);\n            }\n        };\n    }, []);\n}\n//# sourceMappingURL=useLynxGlobalEventListener.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { componentAtIndexFactory, enqueueComponentFactory } from './list.js';\nimport { __pendingListUpdates } from './pendingListUpdates.js';\nimport { DynamicPartType } from './snapshot/dynamicPartType.js';\nimport { unref } from './snapshot/ref.js';\nimport { isEmptyObject } from './utils.js';\nexport function isEmptyDiffResult(diffResult) {\n    const hasChanged = !isEmptyObject(diffResult.i)\n        || !isEmptyObject(diffResult.m) || diffResult.r.length > 0;\n    return !hasChanged;\n}\nexport function diffArrayLepus(before, after, isSameType, onDiffChildren) {\n    let lastPlacedIndex = 0;\n    const result = {\n        $$diff: true,\n        i: {},\n        r: [],\n        m: {},\n    };\n    const beforeMap = {};\n    for (let i = 0; i < before.length; i++) {\n        const node = before[i];\n        (beforeMap[node.type] ??= new Set()).add([node, i]);\n    }\n    for (let i = 0; i < after.length; i++) {\n        const afterNode = after[i];\n        const beforeNodes = beforeMap[afterNode.type];\n        let beforeNode;\n        if (beforeNodes\n            && beforeNodes.size > 0\n            // @ts-expect-error TS does not know about iterator :)\n            && (([beforeNode] = beforeNodes), beforeNode)\n            && isSameType(beforeNode[0], afterNode)) {\n            // Reuse old node\n            beforeNodes.delete(beforeNode);\n            const oldIndex = beforeNode[1];\n            onDiffChildren(beforeNode[0], afterNode, oldIndex, i);\n            if (oldIndex < lastPlacedIndex) {\n                result.m[oldIndex] = i;\n                continue;\n            }\n            else {\n                lastPlacedIndex = oldIndex;\n            }\n        }\n        else {\n            // Create new node\n            result.i[i] = afterNode;\n        }\n    }\n    // Delete\n    for (const k in beforeMap) {\n        for (const [, i] of beforeMap[k]) {\n            result.r.push(i);\n        }\n    }\n    return result;\n}\n// export function diffIterableLepus<A extends Typed, B extends Typed>(\n//   before: A[],\n//   after: Iterable<B>,\n//   isSameType: (a: A, b: B) => boolean,\n//   onDiffChildren: (a: A, b: B) => void\n// ): DiffResult<B> {\n//   let returnResult = EMPTY_DIFF_RESULT as DiffResult<B>;\n//   let lastPlacedIndex = 0;\n//   const result: DiffResult<B> = {\n//     $$diff: true,\n//     i: {},\n//     r: [],\n//     m: {},\n//   };\n//   const beforeMap: Record<string, Set<[A, number]>> = {};\n//   for (let i = 0; i < before.length; i++) {\n//     let node = before[i];\n//     (beforeMap[node.type] ??= new Set()).add([node, i]);\n//   }\n//   let i = 0;\n//   for (const afterNode of after) {\n//     const beforeNodes = beforeMap[afterNode.type];\n//     let beforeNode: [A, number];\n//     if (beforeNodes && (([beforeNode] = beforeNodes), beforeNode) && isSameType(beforeNode[0], afterNode)) {\n//       // Reuse old node\n//       beforeNodes.delete(beforeNode);\n//       const oldIndex = beforeNode[1];\n//       onDiffChildren(beforeNode[0], afterNode);\n//       if (oldIndex < lastPlacedIndex) {\n//         result.m[oldIndex] = i;\n//         returnResult = result;\n//         i++;\n//         continue;\n//       } else {\n//         lastPlacedIndex = oldIndex;\n//       }\n//     } else {\n//       // Create new node\n//       result.i[i] = afterNode;\n//       returnResult = result;\n//     }\n//     i++;\n//   }\n//   // delete\n//   for (const k in beforeMap) {\n//     for (const [, i] of beforeMap[k]) {\n//       result.r.push(i);\n//       returnResult = result;\n//     }\n//   }\n//   return result;\n// }\nexport function diffArrayAction(before, diffResult, onInsert, onRemove, onMove) {\n    if (isEmptyDiffResult(diffResult)) {\n        return before;\n    }\n    const deleteSet = new Set(diffResult.r);\n    const { i: insertMap, m: placementMap } = diffResult;\n    const moveTempMap = new Map();\n    let old;\n    let k = 0;\n    old = before[k];\n    // let current: T | null | undefined = null;\n    const result = [];\n    let i = 0; // index of the old list\n    let j = 0; // index of the new list\n    let remain = Object.keys(insertMap).length;\n    while (old || remain > 0) {\n        let keep = false;\n        if (old && deleteSet.has(j)) {\n            // delete\n            onRemove(old);\n        }\n        else if (old && placementMap[j] !== undefined) {\n            // save node to re-use\n            moveTempMap.set(placementMap[j], old);\n            remain++;\n        }\n        else {\n            // insert node\n            let newNode = old;\n            if (moveTempMap.has(i)) {\n                // insert re-used node\n                newNode = moveTempMap.get(i);\n                keep = true;\n                onMove(newNode, old);\n                remain--;\n            }\n            else if (insertMap[i] !== undefined) {\n                // insert new node\n                newNode = onInsert(insertMap[i], old);\n                keep = true;\n                remain--;\n            }\n            result.push(newNode);\n            i++;\n        }\n        if (old && !keep) {\n            old = before[++k];\n            j++;\n        }\n    }\n    return result;\n}\nexport function hydrate(before, after, options) {\n    after.__elements = before.__elements;\n    after.__element_root = before.__element_root;\n    if (!(options?.skipUnRef)) {\n        unref(before, false);\n    }\n    let swap;\n    if ((swap = options?.swap)) {\n        swap[before.__id] = after.__id;\n    }\n    after.__values?.forEach((value, index) => {\n        const old = before.__values[index];\n        if (value !== old) {\n            after.__values[index] = old;\n            after.setAttribute(index, value);\n        }\n    });\n    const { slot } = after.__snapshot_def;\n    if (!slot) {\n        return;\n    }\n    const beforeChildNodes = before.childNodes;\n    const afterChildNodes = after.childNodes;\n    slot.forEach(([type, elementIndex], index) => {\n        switch (type) {\n            case DynamicPartType.Slot:\n            case DynamicPartType.MultiChildren: {\n                // TODO: the following null assertions are not 100% safe\n                const v1 = beforeChildNodes[index];\n                const v2 = afterChildNodes[index];\n                hydrate(v1, v2, options);\n                break;\n            }\n            case DynamicPartType.Children: {\n                const diffResult = diffArrayLepus(beforeChildNodes, afterChildNodes, (a, b) => a.type === b.type, (a, b) => {\n                    hydrate(a, b, options);\n                });\n                diffArrayAction(beforeChildNodes, diffResult, (node, target) => {\n                    node.ensureElements();\n                    __InsertElementBefore(before.__elements[elementIndex], node.__element_root, target?.__element_root);\n                    return node;\n                }, node => {\n                    __RemoveElement(before.__elements[elementIndex], node.__element_root);\n                }, (node, target) => {\n                    __RemoveElement(before.__elements[elementIndex], node.__element_root);\n                    __InsertElementBefore(before.__elements[elementIndex], node.__element_root, target?.__element_root);\n                });\n                break;\n            }\n            case DynamicPartType.ListChildren: {\n                const removals = [];\n                const insertions = [];\n                const updateAction = [];\n                const diffResult = diffArrayLepus(beforeChildNodes, afterChildNodes, (a, b) => a.type === b.type, (a, b, oldIndex, newIndex) => {\n                    if (JSON.stringify(a.__listItemPlatformInfo)\n                        !== JSON.stringify(b.__listItemPlatformInfo)) {\n                        updateAction.push({\n                            ...b.__listItemPlatformInfo,\n                            from: newIndex,\n                            to: newIndex,\n                            // no flush\n                            flush: false,\n                        });\n                    }\n                    // Mark list-item which is rendered (has `__elements`) as DELETE\n                    // so list platform will call `enqueueComponent` on it\n                    // and will call `componentAtIndex` on the inserted one\n                    // In this way:\n                    //  1. we make sure `<list/>` for hydrate is like a leaf node\n                    //  2. we avoid hydrate so modifying recycleMap can be avoid\n                    //  3. the delete list-item is recycled for later use, so no waste\n                    if (a.__elements) {\n                        removals.push(oldIndex);\n                        insertions.push(newIndex);\n                    }\n                });\n                for (const i of diffResult.r) {\n                    removals.push(i);\n                }\n                for (const i in diffResult.i) {\n                    insertions.push(Number(i));\n                }\n                for (const i in diffResult.m) {\n                    removals.push(Number(i));\n                    insertions.push(diffResult.m[i]);\n                }\n                insertions.sort((a, b) => a - b);\n                removals.sort((a, b) => a - b);\n                const info = {\n                    insertAction: insertions.map((it) => ({\n                        position: it,\n                        type: afterChildNodes[it].type,\n                        ...afterChildNodes[it].__listItemPlatformInfo,\n                    })),\n                    removeAction: removals,\n                    updateAction,\n                };\n                const listElement = before.__elements[elementIndex];\n                __SetAttribute(listElement, 'update-list-info', info);\n                const [componentAtIndex, componentAtIndexes] = componentAtIndexFactory(afterChildNodes, hydrate);\n                __UpdateListCallbacks(listElement, componentAtIndex, enqueueComponentFactory(), componentAtIndexes);\n                // The `before` & `after` target to the same list element, so we need to\n                // avoid the newly created list's (behind snapshot instance `after`) \"update-list-info\" being recorded.\n                delete __pendingListUpdates.values[after.__id];\n            }\n        }\n    });\n}\n//# sourceMappingURL=hydrate.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport './lynx.js';\nimport './lynx/component.js';\nimport { Children, Component, Fragment, PureComponent, Suspense, lazy as backgroundLazy, cloneElement, createContext, createElement, createRef, forwardRef, isValidElement, memo, useSyncExternalStore, } from 'preact/compat';\nimport { useCallback, useContext, useDebugValue, useEffect, useImperativeHandle, useLayoutEffect, useMemo, useReducer, useRef, useState, } from './hooks/react.js';\nimport { loadLazyBundle, mainThreadLazy } from './lynx/lazy-bundle.js';\nexport { Component, createContext } from 'preact';\nexport { PureComponent } from 'preact/compat';\nexport * from './hooks/react.js';\nconst lazy = /*#__PURE__*/ (() => {\n    lynx.loadLazyBundle = loadLazyBundle;\n    return __LEPUS__\n        ? mainThreadLazy\n        : backgroundLazy;\n})();\n/**\n * @internal\n */\nexport default {\n    // hooks\n    useState,\n    useReducer,\n    useEffect,\n    useLayoutEffect,\n    useRef,\n    useImperativeHandle,\n    useMemo,\n    useCallback,\n    useContext,\n    useDebugValue,\n    useSyncExternalStore,\n    createContext,\n    createRef,\n    Fragment,\n    isValidElement,\n    Children,\n    Component,\n    PureComponent,\n    memo,\n    forwardRef,\n    Suspense,\n    lazy,\n    createElement,\n};\nexport { Children, createRef, Fragment, isValidElement, memo, forwardRef, Suspense, lazy, createElement, cloneElement, useSyncExternalStore, };\nexport * from './lynx-api.js';\n//# sourceMappingURL=index.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { Suspense, createElement, lazy } from 'preact/compat';\nimport './lynx.js';\nimport { factory as factory2 } from './compat/componentIs.js';\nimport { useMemo } from './hooks/react.js';\nimport { loadLazyBundle } from './lynx/lazy-bundle.js';\nimport { __root } from './root.js';\nimport { DynamicPartType } from './snapshot/dynamicPartType.js';\nimport { snapshotCreateList } from './snapshot/list.js';\nimport { SnapshotInstance, __page, __pageId, createSnapshot, snapshotManager } from './snapshot.js';\nexport { __page, __pageId, __root };\nexport { SnapshotInstance, snapshotCreateList, createSnapshot, snapshotManager };\nexport const __DynamicPartSlot = DynamicPartType.Slot;\nexport const __DynamicPartMultiChildren = DynamicPartType.MultiChildren;\nexport const __DynamicPartChildren = DynamicPartType.Children;\nexport const __DynamicPartListChildren = DynamicPartType.ListChildren;\nexport const __DynamicPartChildren_0 = [[DynamicPartType.Children, 0]];\nexport { updateSpread } from './snapshot/spread.js';\nexport { updateEvent } from './snapshot/event.js';\nexport { updateRef, transformRef } from './snapshot/ref.js';\nexport { updateWorkletEvent } from './snapshot/workletEvent.js';\nexport { updateWorkletRef } from './snapshot/workletRef.js';\nexport { updateGesture } from './snapshot/gesture.js';\nexport { updateListItemPlatformInfo } from './snapshot/platformInfo.js';\nexport { options, \n// Component is not an internal API, but refresh needs it from 'react/internal'\nComponent, } from 'preact';\nexport { loadDynamicJS, __dynamicImport } from './lynx/dynamic-js.js';\nexport { withInitDataInState } from './compat/initData.js';\nexport { wrapWithLynxComponent } from './compat/lynxComponent.js';\n/**\n * @internal a polyfill for <component is=? />\n */\nexport const __ComponentIsPolyfill = /* @__PURE__ */ factory2(\n// eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n{ Suspense, lazy, createElement, useMemo }, loadLazyBundle);\nexport { loadLazyBundle } from './lynx/lazy-bundle.js';\nexport { transformToWorklet } from './worklet/transformToWorklet.js';\nexport { registerWorkletOnBackground } from './worklet/hmr.js';\nexport { loadWorkletRuntime } from '@lynx-js/react/worklet-runtime/bindings';\n//# sourceMappingURL=internal.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nexport var LifecycleConstant;\n(function (LifecycleConstant) {\n    LifecycleConstant[\"firstScreen\"] = \"rLynxFirstScreen\";\n    LifecycleConstant[\"updateFromRoot\"] = \"updateFromRoot\";\n    LifecycleConstant[\"globalEventFromLepus\"] = \"globalEventFromLepus\";\n    LifecycleConstant[\"jsReady\"] = \"rLynxJSReady\";\n    LifecycleConstant[\"patchUpdate\"] = \"rLynxChange\";\n    LifecycleConstant[\"publishEvent\"] = \"rLynxPublishEvent\";\n})(LifecycleConstant || (LifecycleConstant = {}));\nexport var NativeUpdateDataType;\n(function (NativeUpdateDataType) {\n    NativeUpdateDataType[NativeUpdateDataType[\"UPDATE\"] = 0] = \"UPDATE\";\n    NativeUpdateDataType[NativeUpdateDataType[\"RESET\"] = 1] = \"RESET\";\n})(NativeUpdateDataType || (NativeUpdateDataType = {}));\n//# sourceMappingURL=lifecycleConstant.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { render } from 'preact';\nimport { __root } from '../root.js';\nimport { delayedEvents } from './event/delayEvents.js';\nimport { delayedLifecycleEvents } from './event/delayLifecycleEvents.js';\nimport { globalCommitTaskMap } from './patch/commit.js';\nfunction destroyBackground() {\n    if (__PROFILE__) {\n        console.profile('destroyBackground');\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    render(null, __root);\n    globalCommitTaskMap.forEach(task => {\n        task();\n    });\n    globalCommitTaskMap.clear();\n    // Clear delayed events which should not be executed after destroyed.\n    // This is important when the page is performing a reload.\n    delayedLifecycleEvents.length = 0;\n    if (delayedEvents) {\n        delayedEvents.length = 0;\n    }\n    if (__PROFILE__) {\n        console.profileEnd();\n    }\n}\nexport { destroyBackground };\n//# sourceMappingURL=destroy.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nlet delayedEvents;\nfunction delayedPublishEvent(handlerName, data) {\n    delayedEvents ??= [];\n    delayedEvents.push([handlerName, data]);\n}\nexport { delayedPublishEvent, delayedEvents };\n//# sourceMappingURL=delayEvents.js.map", "const delayedLifecycleEvents = [];\nfunction delayLifecycleEvent(type, data) {\n    delayedLifecycleEvents.push([type, data]);\n}\n/**\n * @internal\n */\nexport { delayLifecycleEvent, delayedLifecycleEvents };\n//# sourceMappingURL=delayLifecycleEvents.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { LifecycleConstant } from '../../lifecycleConstant.js';\nimport { __root } from '../../root.js';\nlet isJSReady;\nlet jsReadyEventIdSwap;\nfunction jsReady() {\n    isJSReady = true;\n    __OnLifecycleEvent([\n        LifecycleConstant.firstScreen, /* FIRST_SCREEN */\n        {\n            root: JSON.stringify(__root),\n            jsReadyEventIdSwap,\n        },\n    ]);\n    jsReadyEventIdSwap = {};\n}\nfunction clearJSReadyEventIdSwap() {\n    jsReadyEventIdSwap = {};\n}\nfunction resetJSReady() {\n    isJSReady = false;\n    jsReadyEventIdSwap = {};\n}\n/**\n * @internal\n */\nexport { jsReady, isJSReady, jsReadyEventIdSwap, clearJSReadyEventIdSwap, resetJSReady };\n//# sourceMappingURL=jsReady.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nlet reloadVersion = 0;\nfunction getReloadVersion() {\n    return reloadVersion;\n}\nfunction increaseReloadVersion() {\n    return ++reloadVersion;\n}\nexport { getReloadVersion, increaseReloadVersion };\n//# sourceMappingURL=pass.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { options } from 'preact';\nimport { LifecycleConstant } from '../../lifecycleConstant.js';\nimport { globalPipelineOptions, markTiming, markTimingLegacy, setPipeline } from '../../lynx/performance.js';\nimport { COMMIT } from '../../renderToOpcodes/constants.js';\nimport { applyQueuedRefs } from '../../snapshot/ref.js';\nimport { backgroundSnapshotInstanceManager } from '../../snapshot.js';\nimport { isEmptyObject } from '../../utils.js';\nimport { takeWorkletRefInitValuePatch } from '../../worklet/workletRefPool.js';\nimport { getReloadVersion } from '../pass.js';\nimport { takeGlobalSnapshotPatch } from './snapshotPatch.js';\nlet globalFlushOptions = {};\nconst globalCommitTaskMap = /*@__PURE__*/ new Map();\nlet nextCommitTaskId = 1;\nlet globalBackgroundSnapshotInstancesToRemove = [];\n/**\n * Replaces Preact's default commit hook with our custom implementation\n */\nfunction replaceCommitHook() {\n    // This is actually not used since Preact use `hooks._commit` for callbacks of `useLayoutEffect`.\n    const originalPreactCommit = options[COMMIT];\n    const commit = async (vnode, commitQueue) => {\n        // Skip commit phase for MT runtime\n        if (__MAIN_THREAD__) {\n            // for testing only\n            commitQueue.length = 0;\n            return;\n        }\n        // Mark the end of virtual DOM diffing phase for performance tracking\n        markTimingLegacy('updateDiffVdomEnd');\n        markTiming('diffVdomEnd');\n        const backgroundSnapshotInstancesToRemove = globalBackgroundSnapshotInstancesToRemove;\n        globalBackgroundSnapshotInstancesToRemove = [];\n        const commitTaskId = genCommitTaskId();\n        // Register the commit task\n        globalCommitTaskMap.set(commitTaskId, () => {\n            if (backgroundSnapshotInstancesToRemove.length) {\n                setTimeout(() => {\n                    backgroundSnapshotInstancesToRemove.forEach(id => {\n                        backgroundSnapshotInstanceManager.values.delete(id);\n                    });\n                }, 10000);\n            }\n        });\n        // Collect patches for this update\n        const snapshotPatch = takeGlobalSnapshotPatch();\n        const flushOptions = globalFlushOptions;\n        const workletRefInitValuePatch = takeWorkletRefInitValuePatch();\n        globalFlushOptions = {};\n        if (!snapshotPatch && workletRefInitValuePatch.length === 0) {\n            // before hydration, skip patch\n            applyQueuedRefs();\n            originalPreactCommit?.(vnode, commitQueue);\n            return;\n        }\n        const patch = {\n            id: commitTaskId,\n        };\n        // TODO: check all fields in `flushOptions` from runtime3\n        if (snapshotPatch?.length) {\n            patch.snapshotPatch = snapshotPatch;\n        }\n        if (workletRefInitValuePatch.length) {\n            patch.workletRefInitValuePatch = workletRefInitValuePatch;\n        }\n        const patchList = {\n            patchList: [patch],\n        };\n        if (!isEmptyObject(flushOptions)) {\n            patchList.flushOptions = flushOptions;\n        }\n        const obj = commitPatchUpdate(patchList, {});\n        // Send the update to the native layer\n        lynx.getNativeApp().callLepusMethod(LifecycleConstant.patchUpdate, obj, () => {\n            const commitTask = globalCommitTaskMap.get(commitTaskId);\n            if (commitTask) {\n                commitTask();\n                globalCommitTaskMap.delete(commitTaskId);\n            }\n        });\n        applyQueuedRefs();\n        originalPreactCommit?.(vnode, commitQueue);\n    };\n    options[COMMIT] = commit;\n}\n/**\n * Prepares the patch update for transmission to the native layer\n */\nfunction commitPatchUpdate(patchList, patchOptions) {\n    // console.debug('********** JS update:');\n    // printSnapshotInstance(\n    //   (backgroundSnapshotInstanceManager.values.get(1) || backgroundSnapshotInstanceManager.values.get(-1))!,\n    // );\n    // console.debug('commitPatchUpdate: ', JSON.stringify(patchList));\n    if (__PROFILE__) {\n        console.profile('commitChanges');\n    }\n    markTiming('packChangesStart');\n    const obj = {\n        data: JSON.stringify(patchList),\n        patchOptions: {\n            ...patchOptions,\n            reloadVersion: getReloadVersion(),\n        },\n    };\n    markTiming('packChangesEnd');\n    if (globalPipelineOptions) {\n        obj.patchOptions.pipelineOptions = globalPipelineOptions;\n        setPipeline(undefined);\n    }\n    if (__PROFILE__) {\n        console.profileEnd();\n    }\n    return obj;\n}\n/**\n * Generates a unique ID for commit tasks\n */\nfunction genCommitTaskId() {\n    return nextCommitTaskId++;\n}\n/**\n * Resets the commit task ID counter\n */\nfunction clearCommitTaskId() {\n    nextCommitTaskId = 1;\n}\n/**\n * @internal\n */\nexport { clearCommitTaskId, commitPatchUpdate, genCommitTaskId, globalBackgroundSnapshotInstancesToRemove, globalCommitTaskMap, globalFlushOptions, replaceCommitHook, };\n//# sourceMappingURL=commit.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { backgroundSnapshotInstanceManager, snapshotManager } from '../../snapshot.js';\nexport const ctxNotFoundType = 'Lynx.Error.CtxNotFound';\nconst errorMsg = 'snapshotPatchApply failed: ctx not found';\nlet ctxNotFoundEventListener = null;\nexport function sendCtxNotFoundEventToBackground(id) {\n    /* v8 ignore next 3 */\n    if (!lynx.getJSContext) {\n        throw new Error(errorMsg);\n    }\n    lynx.getJSContext().dispatchEvent({\n        type: ctxNotFoundType,\n        data: {\n            id,\n        },\n    });\n}\nexport function reportCtxNotFound(data) {\n    const id = data.id;\n    const instance = backgroundSnapshotInstanceManager.values.get(id);\n    let snapshotType = 'null';\n    if (instance && instance.__snapshot_def) {\n        for (const [snapshotId, snapshot] of snapshotManager.values.entries()) {\n            if (snapshot === instance.__snapshot_def) {\n                snapshotType = snapshotId;\n                break;\n            }\n        }\n    }\n    lynx.reportError(new Error(`${errorMsg}, snapshot type: '${snapshotType}'`));\n}\nexport function addCtxNotFoundEventListener() {\n    ctxNotFoundEventListener = (e) => {\n        reportCtxNotFound(e.data);\n    };\n    lynx.getCoreContext?.().addEventListener(ctxNotFoundType, ctxNotFoundEventListener);\n}\nexport function removeCtxNotFoundEventListener() {\n    const coreContext = lynx.getCoreContext?.();\n    if (coreContext && ctxNotFoundEventListener) {\n        coreContext.removeEventListener(ctxNotFoundType, ctxNotFoundEventListener);\n        ctxNotFoundEventListener = null;\n    }\n}\n//# sourceMappingURL=error.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { onHydrationFinished } from '@lynx-js/react/worklet-runtime/bindings';\nexport let isMainThreadHydrationFinished = false;\nexport function setMainThreadHydrationFinished(isFinished) {\n    if (isFinished && !isMainThreadHydrationFinished) {\n        onHydrationFinished();\n    }\n    isMainThreadHydrationFinished = isFinished;\n}\n//# sourceMappingURL=isMainThreadHydrationFinished.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/**\n * Defines the core patch operations for the snapshot system.\n * The patch operations are designed to be serializable and minimal, allowing\n * efficient transmission between threads and application to element tree.\n */\nexport const SnapshotOperation = {\n    CreateElement: 0,\n    InsertBefore: 1,\n    RemoveChild: 2,\n    SetAttribute: 3,\n    SetAttributes: 4,\n    DEV_ONLY_AddSnapshot: 100,\n    DEV_ONLY_RegisterWorklet: 101,\n};\nexport let __globalSnapshotPatch;\nexport function takeGlobalSnapshotPatch() {\n    if (__globalSnapshotPatch) {\n        const list = __globalSnapshotPatch;\n        __globalSnapshotPatch = [];\n        return list;\n    }\n    else {\n        return undefined;\n    }\n}\nexport function initGlobalSnapshotPatch() {\n    __globalSnapshotPatch = [];\n}\nexport function deinitGlobalSnapshotPatch() {\n    __globalSnapshotPatch = undefined;\n}\n//# sourceMappingURL=snapshotPatch.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/**\n * Implements the patch application logic for the snapshot system.\n * This module is responsible for interpreting and executing patch operations\n * that were generated in the background thread, applying them to the DOM\n * in the main thread.\n *\n * The module handles various operations like element creation, insertion,\n * removal, and attribute updates, ensuring they are applied in the correct\n * order and with proper error handling.\n */\nimport { sendCtxNotFoundEventToBackground } from './error.js';\nimport { SnapshotOperation } from './snapshotPatch.js';\nimport { SnapshotInstance, createSnapshot, entryUniqID, snapshotInstanceManager, snapshotManager, } from '../../snapshot.js';\n/**\n * Applies a patch of snapshot operations to the main thread.\n * This is the counterpart to the patch generation in the background thread.\n * Each operation in the patch is processed sequentially to update the DOM.\n */\nexport function snapshotPatchApply(snapshotPatch) {\n    const length = snapshotPatch.length;\n    for (let i = 0; i < length; ++i) {\n        switch (snapshotPatch[i]) {\n            case SnapshotOperation.CreateElement: {\n                const type = snapshotPatch[++i];\n                const id = snapshotPatch[++i];\n                new SnapshotInstance(type, id);\n                break;\n            }\n            case SnapshotOperation.InsertBefore: {\n                const parentId = snapshotPatch[++i];\n                const childId = snapshotPatch[++i];\n                const beforeId = snapshotPatch[++i];\n                const parent = snapshotInstanceManager.values.get(parentId);\n                const child = snapshotInstanceManager.values.get(childId);\n                const existingNode = snapshotInstanceManager.values.get(beforeId);\n                if (!parent || !child) {\n                    sendCtxNotFoundEventToBackground(parent ? childId : parentId);\n                }\n                else {\n                    parent.insertBefore(child, existingNode);\n                }\n                break;\n            }\n            case SnapshotOperation.RemoveChild: {\n                const parentId = snapshotPatch[++i];\n                const childId = snapshotPatch[++i];\n                const parent = snapshotInstanceManager.values.get(parentId);\n                const child = snapshotInstanceManager.values.get(childId);\n                if (!parent || !child) {\n                    sendCtxNotFoundEventToBackground(parent ? childId : parentId);\n                }\n                else {\n                    parent.removeChild(child);\n                }\n                break;\n            }\n            case SnapshotOperation.SetAttribute: {\n                const id = snapshotPatch[++i];\n                const dynamicPartIndex = snapshotPatch[++i];\n                const value = snapshotPatch[++i];\n                const si = snapshotInstanceManager.values.get(id);\n                if (si) {\n                    si.setAttribute(dynamicPartIndex, value);\n                }\n                else {\n                    sendCtxNotFoundEventToBackground(id);\n                }\n                break;\n            }\n            case SnapshotOperation.SetAttributes: {\n                const id = snapshotPatch[++i];\n                const values = snapshotPatch[++i];\n                const si = snapshotInstanceManager.values.get(id);\n                if (si) {\n                    si.setAttribute('values', values);\n                }\n                else {\n                    sendCtxNotFoundEventToBackground(id);\n                }\n                break;\n            }\n            case SnapshotOperation.DEV_ONLY_AddSnapshot: {\n                if (__DEV__) {\n                    const uniqID = snapshotPatch[++i];\n                    const create = snapshotPatch[++i];\n                    const update = snapshotPatch[++i];\n                    const slot = snapshotPatch[++i];\n                    const cssId = (snapshotPatch[++i] ?? 0);\n                    const entryName = snapshotPatch[++i];\n                    if (!snapshotManager.values.has(entryUniqID(uniqID, entryName))) {\n                        // HMR-related\n                        // Update the evaluated snapshots from JS.\n                        createSnapshot(uniqID, evaluate(create), \n                        // eslint-disable-next-line unicorn/no-array-callback-reference\n                        update.map(evaluate), slot, cssId, entryName, null);\n                    }\n                }\n                break;\n            }\n            // case SnapshotOperation.DEV_ONLY_RegisterWorklet: {\n            //   // HMR-related\n            //   if (__DEV__) {\n            //     const hash: string = snapshotPatch[++i];\n            //     const fnStr: string = snapshotPatch[++i];\n            //     const fn = evaluate<(ctx: SnapshotInstance) => FiberElement[]>(fnStr);\n            //     registerWorklet('main-thread', hash, fn);\n            //   }\n            //   break;\n            // }\n        }\n    }\n}\n/* v8 ignore start */\n/**\n * Evaluates a string as code with ReactLynx runtime injected.\n * Used for HMR (Hot Module Replacement) to update snapshot definitions.\n */\nfunction evaluate(code) {\n    if (__DEV__) {\n        // We are using `eval` here to make the updated snapshot to access variables like `__webpack_require__`.\n        // See: https://github.com/lynx-family/lynx-stack/issues/983.\n        return eval(`(() => ${code})()`);\n    }\n    throw new Error('unreachable: evaluate is not supported in production');\n}\n/* v8 ignore stop */\n//# sourceMappingURL=snapshotPatchApply.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { updateWorkletRefInitValueChanges } from '@lynx-js/react/worklet-runtime/bindings';\nimport { setMainThreadHydrationFinished } from './isMainThreadHydrationFinished.js';\nimport { snapshotPatchApply } from './snapshotPatchApply.js';\nimport { LifecycleConstant } from '../../lifecycleConstant.js';\nimport { markTiming, setPipeline } from '../../lynx/performance.js';\nimport { __pendingListUpdates } from '../../pendingListUpdates.js';\nimport { applyRefQueue } from '../../snapshot/workletRef.js';\nimport { __page } from '../../snapshot.js';\nimport { getReloadVersion } from '../pass.js';\nfunction updateMainThread({ data, patchOptions }) {\n    if ((patchOptions.reloadVersion) < getReloadVersion()) {\n        return;\n    }\n    setPipeline(patchOptions.pipelineOptions);\n    markTiming('mtsRenderStart');\n    markTiming('parseChangesStart');\n    const { patchList, flushOptions = {} } = JSON.parse(data);\n    markTiming('parseChangesEnd');\n    markTiming('patchChangesStart');\n    for (const { snapshotPatch, workletRefInitValuePatch } of patchList) {\n        updateWorkletRefInitValueChanges(workletRefInitValuePatch);\n        __pendingListUpdates.clear();\n        if (snapshotPatch) {\n            snapshotPatchApply(snapshotPatch);\n        }\n        __pendingListUpdates.flush();\n        // console.debug('********** Lepus updatePatch:');\n        // printSnapshotInstance(snapshotInstanceManager.values.get(-1)!);\n    }\n    markTiming('patchChangesEnd');\n    markTiming('mtsRenderEnd');\n    if (patchOptions.isHydration) {\n        setMainThreadHydrationFinished(true);\n    }\n    applyRefQueue();\n    if (patchOptions.pipelineOptions) {\n        flushOptions.pipelineOptions = patchOptions.pipelineOptions;\n    }\n    __FlushElementTree(__page, flushOptions);\n}\nfunction injectUpdateMainThread() {\n    Object.assign(globalThis, { [LifecycleConstant.patchUpdate]: updateMainThread });\n}\n/**\n * @internal\n */\nexport { injectUpdateMainThread };\n//# sourceMappingURL=updateMainThread.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { hydrationMap } from '../../snapshotInstanceHydrationMap.js';\n/**\n * A flag to indicate whether UI operations should be delayed.\n * When set to true, UI operations will be queued in the `delayedUiOps` array\n * and executed later when `runDelayedUiOps` is called.\n * This is used before hydration to ensure UI operations are batched\n * and executed at the appropriate time.\n */\nconst shouldDelayUiOps = { value: true };\n/**\n * An array of functions that will be executed later when `runDelayedUiOps` is called.\n * These functions contain UI operations that need to be delayed.\n */\nconst delayedUiOps = [];\n/**\n * Runs a task either immediately or delays it based on the `shouldDelayUiOps` flag.\n * @param task - The function to execute.\n */\nfunction runOrDelay(task) {\n    if (shouldDelayUiOps.value) {\n        delayedUiOps.push(task);\n    }\n    else {\n        task();\n    }\n}\n/**\n * Executes all delayed UI operations.\n */\nfunction runDelayedUiOps() {\n    for (const task of delayedUiOps) {\n        task();\n    }\n    shouldDelayUiOps.value = false;\n    delayedUiOps.length = 0;\n}\n/**\n * A proxy class designed for managing and executing reference-based tasks.\n * It delays the execution of tasks until hydration is complete.\n */\nclass RefProxy {\n    refAttr;\n    task;\n    constructor(refAttr) {\n        this.refAttr = refAttr;\n    }\n    setTask(method, args) {\n        this.task = (nodesRef) => {\n            return nodesRef[method](...args);\n        };\n        return this;\n    }\n    invoke(...args) {\n        return new RefProxy(this.refAttr).setTask('invoke', args);\n    }\n    path(...args) {\n        return new RefProxy(this.refAttr).setTask('path', args);\n    }\n    fields(...args) {\n        return new RefProxy(this.refAttr).setTask('fields', args);\n    }\n    setNativeProps(...args) {\n        return new RefProxy(this.refAttr).setTask('setNativeProps', args);\n    }\n    exec() {\n        runOrDelay(() => {\n            const realRefId = hydrationMap.get(this.refAttr[0]) ?? this.refAttr[0];\n            const refSelector = `[react-ref-${realRefId}-${this.refAttr[1]}]`;\n            this.task(lynx.createSelectorQuery().select(refSelector)).exec();\n        });\n    }\n}\n/**\n * @internal\n */\nexport { RefProxy, runDelayedUiOps, shouldDelayUiOps };\n//# sourceMappingURL=delay.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/**\n * Implements the reload (thinking of \"refresh\" in browser) for both main thread\n * and background thread.\n */\nimport { render } from 'preact';\nimport { hydrate } from '../hydrate.js';\nimport { LifecycleConstant } from '../lifecycleConstant.js';\nimport { __pendingListUpdates } from '../pendingListUpdates.js';\nimport { __root, setRoot } from '../root.js';\nimport { destroyBackground } from './destroy.js';\nimport { applyRefQueue } from '../snapshot/workletRef.js';\nimport { SnapshotInstance, __page, snapshotInstanceManager } from '../snapshot.js';\nimport { isEmptyObject } from '../utils.js';\nimport { destroyWorklet } from '../worklet/destroy.js';\nimport { clearJSReadyEventIdSwap, isJSReady } from './event/jsReady.js';\nimport { increaseReloadVersion } from './pass.js';\nimport { setMainThreadHydrationFinished } from './patch/isMainThreadHydrationFinished.js';\nimport { deinitGlobalSnapshotPatch } from './patch/snapshotPatch.js';\nimport { shouldDelayUiOps } from './ref/delay.js';\nimport { renderMainThread } from './render.js';\nfunction reloadMainThread(data, options) {\n    if (__PROFILE__) {\n        console.profile('reloadTemplate');\n    }\n    increaseReloadVersion();\n    if (typeof data == 'object' && !isEmptyObject(data)) {\n        Object.assign(lynx.__initData, data);\n    }\n    destroyWorklet();\n    snapshotInstanceManager.clear();\n    __pendingListUpdates.clear();\n    clearJSReadyEventIdSwap();\n    setMainThreadHydrationFinished(false);\n    const oldRoot = __root;\n    setRoot(new SnapshotInstance('root'));\n    __root.__jsx = oldRoot.__jsx;\n    renderMainThread();\n    hydrate(oldRoot, __root, {\n        skipUnRef: true,\n    });\n    // always call this before `__FlushElementTree`\n    __pendingListUpdates.flush();\n    applyRefQueue();\n    if (isJSReady) {\n        __OnLifecycleEvent([\n            LifecycleConstant.firstScreen, /* FIRST_SCREEN */\n            {\n                root: JSON.stringify(__root),\n            },\n        ]);\n    }\n    __FlushElementTree(__page, options);\n    if (__PROFILE__) {\n        console.profileEnd();\n    }\n    return;\n}\nfunction reloadBackground(updateData) {\n    if (__PROFILE__) {\n        console.profile('reload');\n    }\n    deinitGlobalSnapshotPatch();\n    destroyBackground();\n    increaseReloadVersion();\n    // COW when modify `lynx.__initData` to make sure Provider & Consumer works\n    lynx.__initData = Object.assign({}, lynx.__initData, updateData);\n    shouldDelayUiOps.value = true;\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    render(__root.__jsx, __root);\n    if (__PROFILE__) {\n        console.profileEnd();\n    }\n}\nexport { reloadBackground, reloadMainThread };\n//# sourceMappingURL=reload.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/**\n * Implements the IFR (Instant First-Frame Rendering) on main thread.\n */\nimport { render } from 'preact';\nimport { renderOpcodesInto } from '../opcodes.js';\nimport { render as renderToString } from '../renderToOpcodes/index.js';\nimport { __root } from '../root.js';\nfunction renderMainThread() {\n    /* v8 ignore start */\n    if (process.env['NODE_ENV'] === 'test' && typeof __TESTING_FORCE_RENDER_TO_OPCODE__ !== 'undefined'\n        && !__TESTING_FORCE_RENDER_TO_OPCODE__) {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n        render(__root.__jsx, __root);\n    }\n    else {\n        let opcodes;\n        try {\n            if (__PROFILE__) {\n                console.profile('renderToString');\n            }\n            opcodes = renderToString(__root.__jsx, undefined);\n        }\n        catch (e) {\n            lynx.reportError(e);\n            opcodes = [];\n        }\n        finally {\n            if (__PROFILE__) {\n                console.profileEnd();\n            }\n        }\n        if (__PROFILE__) {\n            console.profile('renderOpcodesInto');\n        }\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n        renderOpcodesInto(opcodes, __root);\n        if (__ENABLE_SSR__) {\n            __root.__opcodes = opcodes;\n        }\n        if (__PROFILE__) {\n            console.profileEnd();\n        }\n    }\n    /* v8 ignore stop */\n}\nexport { renderMainThread };\n//# sourceMappingURL=render.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { LifecycleConstant } from './lifecycleConstant.js';\nimport { applyRefQueue } from './snapshot/workletRef.js';\nimport { maybePromise } from './utils.js';\nexport const gSignMap = {};\nexport const gRecycleMap = {};\nconst gParentWeakMap = new WeakMap();\nconst resolvedPromise = /* @__PURE__ */ Promise.resolve();\nexport function clearListGlobal() {\n    for (const key in gSignMap) {\n        delete gSignMap[key];\n    }\n    for (const key in gRecycleMap) {\n        delete gRecycleMap[key];\n    }\n}\nexport function componentAtIndexFactory(ctx, hydrateFunction) {\n    // A hack workaround to ensure childCtx has no direct reference through `__parent` to list,\n    // to avoid memory leak.\n    // TODO(hzy): make `__parent` a WeakRef or `#__parent` in the future.\n    ctx.forEach((childCtx) => {\n        if (gParentWeakMap.has(childCtx)) {\n            // do it only once\n        }\n        else {\n            gParentWeakMap.set(childCtx, childCtx.parentNode);\n            Object.defineProperty(childCtx, '__parent', {\n                get: () => gParentWeakMap.get(childCtx),\n                set: (value) => {\n                    gParentWeakMap.set(childCtx, value);\n                },\n            });\n        }\n    });\n    const componentAtChildCtx = (list, listID, childCtx, operationID, enableReuseNotification, enableBatchRender = false, asyncFlush = false) => {\n        const signMap = gSignMap[listID];\n        const recycleMap = gRecycleMap[listID];\n        if (!signMap || !recycleMap) {\n            throw new Error('componentAtIndex called on removed list');\n        }\n        const platformInfo = childCtx.__listItemPlatformInfo ?? {};\n        // The lifecycle of this `__extraProps.isReady`:\n        //   0 -> Promise<number> -> 1\n        // 0: The initial state, the list-item is not ready yet, we will send a event to background\n        //    when `componentAtIndex` is called on it\n        // Promise<number>: A promise that will be resolved when the list-item is ready\n        // 1: The list-item is ready, we can use it to render the list\n        if (childCtx.__extraProps?.['isReady'] === 0) {\n            if (typeof __GetAttributeByName === 'function'\n                && __GetAttributeByName(list, 'custom-list-name') === 'list-container') {\n                // we are in supported env\n                // do not throw\n            }\n            else {\n                throw new Error('Unsupported: `<list-item/>` with `defer={true}` must be used with `<list custom-list-name=\"list-container\"/>`');\n            }\n            __OnLifecycleEvent([LifecycleConstant.publishEvent, {\n                    handlerName: `${childCtx.__id}:__extraProps:onComponentAtIndex`,\n                    data: {},\n                }]);\n            let p;\n            return (p = new Promise((resolve) => {\n                Object.defineProperty(childCtx.__extraProps, 'isReady', {\n                    set(isReady) {\n                        if (isReady === 1) {\n                            delete childCtx.__extraProps['isReady'];\n                            childCtx.__extraProps['isReady'] = 1;\n                            void resolvedPromise.then(() => {\n                                // the cellIndex may be changed already, but the `childCtx` is the same\n                                resolve(componentAtChildCtx(list, listID, childCtx, operationID, enableReuseNotification));\n                            });\n                        }\n                    },\n                    get() {\n                        return p;\n                    },\n                });\n            }));\n        }\n        else if (maybePromise(childCtx.__extraProps?.['isReady'])) {\n            throw new Error('componentAtIndex was called on a pending deferred list item');\n        }\n        const uniqID = childCtx.type + (platformInfo['reuse-identifier'] ?? '');\n        const recycleSignMap = recycleMap.get(uniqID);\n        if (childCtx.__elements) {\n            /**\n             * If this situation is encountered, there might be two cases:\n             * 1. Reusing with itself\n             *    In this case, enqueueComponent will be triggered first, followed by componentAtIndex.\n             * 2. Moving\n             *    In this case, the trigger order is uncertain; componentAtIndex might be triggered first, or enqueueComponent might be triggered first.\n             *\n             * When enqueueComponent is triggered first, there must be an item in the reuse pool with the same sign as here, which can be returned directly.\n             * When componentAtIndex is triggered first, a clone needs to be made first, then follow the logic for adding or reusing. The cloned item will enter the reuse pool in the subsequent enqueueComponent.\n             */\n            const root = childCtx.__elements[0];\n            const sign = __GetElementUniqueID(root);\n            if (recycleSignMap?.has(sign)) {\n                signMap.set(sign, childCtx);\n                recycleSignMap.delete(sign);\n                if (!enableBatchRender) {\n                    __FlushElementTree(root, { triggerLayout: true, operationID, elementID: sign, listID });\n                }\n                else if (enableBatchRender && asyncFlush) {\n                    __FlushElementTree(root, { asyncFlush: true });\n                }\n                // enableBatchRender == true && asyncFlush == false\n                // in this case, no need to invoke __FlushElementTree because in the end of componentAtIndexes(), the list will invoke __FlushElementTree.\n                return sign;\n            }\n            else {\n                const newCtx = childCtx.takeElements();\n                signMap.set(sign, newCtx);\n            }\n        }\n        if (recycleSignMap && recycleSignMap.size > 0) {\n            const [first] = recycleSignMap;\n            const [sign, oldCtx] = first;\n            recycleSignMap.delete(sign);\n            hydrateFunction(oldCtx, childCtx);\n            oldCtx.unRenderElements();\n            if (!oldCtx.__id) {\n                oldCtx.tearDown();\n            }\n            else if (oldCtx.__extraProps?.['isReady'] === 1) {\n                __OnLifecycleEvent([LifecycleConstant.publishEvent, {\n                        handlerName: `${oldCtx.__id}:__extraProps:onRecycleComponent`,\n                        data: {},\n                    }]);\n            }\n            const root = childCtx.__element_root;\n            applyRefQueue();\n            if (!enableBatchRender) {\n                const flushOptions = {\n                    triggerLayout: true,\n                    operationID,\n                    elementID: sign,\n                    listID,\n                };\n                if (enableReuseNotification) {\n                    flushOptions.listReuseNotification = {\n                        listElement: list,\n                        itemKey: platformInfo['item-key'],\n                    };\n                }\n                __FlushElementTree(root, flushOptions);\n            }\n            else if (enableBatchRender && asyncFlush) {\n                const flushOptions = {\n                    asyncFlush: true,\n                };\n                if (enableReuseNotification) {\n                    flushOptions.listReuseNotification = {\n                        listElement: list,\n                        itemKey: platformInfo['item-key'],\n                    };\n                }\n                __FlushElementTree(root, flushOptions);\n            }\n            signMap.set(sign, childCtx);\n            return sign;\n        }\n        childCtx.ensureElements();\n        const root = childCtx.__element_root;\n        __AppendElement(list, root);\n        const sign = __GetElementUniqueID(root);\n        applyRefQueue();\n        if (!enableBatchRender) {\n            __FlushElementTree(root, {\n                triggerLayout: true,\n                operationID,\n                elementID: sign,\n                listID,\n            });\n        }\n        else if (enableBatchRender && asyncFlush) {\n            __FlushElementTree(root, {\n                asyncFlush: true,\n            });\n        }\n        signMap.set(sign, childCtx);\n        return sign;\n    };\n    function componentAtIndex(list, listID, cellIndex, operationID, enableReuseNotification) {\n        const childCtx = ctx[cellIndex];\n        if (!childCtx) {\n            throw new Error('childCtx not found');\n        }\n        const r = componentAtChildCtx(list, listID, childCtx, operationID, enableReuseNotification);\n        /* v8 ignore start */\n        if (process.env['NODE_ENV'] === 'test') {\n            return r;\n        }\n        else {\n            return typeof r === 'number' ? r : undefined;\n        }\n        /* v8 ignore end */\n    }\n    function componentAtIndexes(list, listID, cellIndexes, operationIDs, enableReuseNotification, asyncFlush) {\n        let hasUnready = false;\n        const p = [];\n        cellIndexes.forEach((cellIndex, index) => {\n            const operationID = operationIDs[index];\n            const childCtx = ctx[cellIndex];\n            if (!childCtx) {\n                throw new Error('childCtx not found');\n            }\n            const u = componentAtChildCtx(list, listID, childCtx, operationID, enableReuseNotification, true, asyncFlush);\n            if (typeof u === 'number') {\n                // ready\n            }\n            else {\n                hasUnready = true;\n            }\n            p.push(u);\n        });\n        // We need __FlushElementTree twice:\n        // 1. The first time is sync, we flush the items that are ready, with unready items' uiSign as -1.\n        // 2. The second time is async, with all the uiSigns.\n        // NOTE: The `operationIDs` passed to __FlushElementTree must be the one passed in,\n        // not the one generated by any code here, to workaround a bug of Lynx Engine.\n        // So we CANNOT split the `operationIDs` into two parts: one for ready items, one for unready items.\n        if (hasUnready) {\n            void Promise.all(p).then((uiSigns) => {\n                __FlushElementTree(list, {\n                    triggerLayout: true,\n                    operationIDs,\n                    elementIDs: uiSigns,\n                    listID,\n                });\n            });\n        }\n        __FlushElementTree(list, {\n            triggerLayout: true,\n            operationIDs,\n            elementIDs: cellIndexes.map((_, index) => typeof p[index] === 'number' ? p[index] : -1),\n            listID,\n        });\n    }\n    return [componentAtIndex, componentAtIndexes];\n}\nexport function enqueueComponentFactory() {\n    // eslint-disable-next-line unicorn/consistent-function-scoping\n    const enqueueComponent = (_, listID, sign) => {\n        const signMap = gSignMap[listID];\n        const recycleMap = gRecycleMap[listID];\n        if (!signMap || !recycleMap) {\n            throw new Error('enqueueComponent called on removed list');\n        }\n        const childCtx = signMap.get(sign);\n        if (!childCtx) {\n            return;\n        }\n        const platformInfo = childCtx.__listItemPlatformInfo ?? {};\n        const uniqID = childCtx.type + (platformInfo['reuse-identifier'] ?? '');\n        if (!recycleMap.has(uniqID)) {\n            recycleMap.set(uniqID, new Map());\n        }\n        recycleMap.get(uniqID).set(sign, childCtx);\n    };\n    return enqueueComponent;\n}\n//# sourceMappingURL=list.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { hydrate } from './hydrate.js';\nimport { componentAtIndexFactory, enqueueComponentFactory } from './list.js';\nexport class ListUpdateInfoRecording {\n    list;\n    constructor(list) {\n        this.list = list;\n        this.oldChildNodes = list.childNodes;\n        // this.oldChildNodesSet = new Set(this.oldChildNodes);\n    }\n    // private __commitAndReset() {\n    //   (this.__pendingAttributes ??= []).push(this.__toAttribute());\n    //   this.oldChildNodes = this.list.childNodes;\n    //   this.oldChildNodesSet = new Set(this.oldChildNodes);\n    //   this.removeChild1.clear();\n    //   this.removeChild2.clear();\n    //   this.insertBefore.clear();\n    //   this.appendChild.length = 0;\n    //   this.platformInfoUpdate.clear();\n    // }\n    flush() {\n        const elementIndex = this.list.__snapshot_def.slot[0][1];\n        const listElement = this.list.__elements[elementIndex];\n        // this.__pendingAttributes?.forEach(pendingAttribute => {\n        //   __SetAttribute(listElement, \"update-list-info\", pendingAttribute);\n        //   __FlushElementTree(listElement);\n        // });\n        __SetAttribute(listElement, 'update-list-info', this.__toAttribute());\n        const [componentAtIndex, componentAtIndexes] = componentAtIndexFactory(this.list.childNodes, hydrate);\n        __UpdateListCallbacks(listElement, componentAtIndex, enqueueComponentFactory(), componentAtIndexes);\n    }\n    oldChildNodes;\n    // private oldChildNodesSet: Set<SnapshotInstance>;\n    removeChild = new Set();\n    insertBefore = new Map(); // insert V before K\n    appendChild = [];\n    platformInfoUpdate = new Map();\n    onInsertBefore(newNode, existingNode) {\n        if (newNode.parentNode) {\n            // if (!this.oldChildNodesSet.has(newNode)) {\n            //   this.__commitAndReset();\n            // }\n            this.removeChild.add(newNode);\n        }\n        if (existingNode) {\n            // if (!this.oldChildNodesSet.has(existingNode)) {\n            //   this.__commitAndReset();\n            // }\n            const newChildren = this.insertBefore.get(existingNode) ?? [];\n            newChildren.push(newNode);\n            this.insertBefore.set(existingNode, newChildren);\n        }\n        else {\n            this.appendChild.push(newNode);\n        }\n    }\n    onRemoveChild(child) {\n        // if (!this.oldChildNodesSet.has(child)) {\n        //   this.__commitAndReset();\n        // }\n        this.removeChild.add(child);\n    }\n    onSetAttribute(child, attr, _oldAttr) {\n        this.platformInfoUpdate.set(child, attr);\n    }\n    __toAttribute() {\n        const { removeChild, insertBefore, appendChild, platformInfoUpdate } = this;\n        const removals = [];\n        const insertions = [];\n        const updates = [];\n        let j = 0;\n        for (let i = 0; i < this.oldChildNodes.length; i++, j++) {\n            const child = this.oldChildNodes[i];\n            if (platformInfoUpdate.has(child)) {\n                updates.push({\n                    ...platformInfoUpdate.get(child),\n                    from: +j,\n                    to: +j,\n                    // no flush\n                    flush: false,\n                    type: child.type,\n                });\n            }\n            if (insertBefore.has(child)) {\n                const children = insertBefore.get(child);\n                children.forEach(c => {\n                    insertions.push({\n                        position: j,\n                        type: c.type,\n                        ...c.__listItemPlatformInfo,\n                    });\n                    j++;\n                });\n            }\n            if (removeChild.has(child)) {\n                removals.push(i);\n                removeChild.delete(child);\n                j--;\n            }\n        }\n        for (let i = 0; i < appendChild.length; i++) {\n            const child = appendChild[i];\n            insertions.push({\n                position: j + i,\n                type: child.type,\n                ...child.__listItemPlatformInfo,\n            });\n        }\n        insertions.sort((a, b) => a.position - b.position);\n        removals.sort((a, b) => a - b);\n        if (SystemInfo.lynxSdkVersion === '2.14'\n            || SystemInfo.lynxSdkVersion === '2.15'\n            || SystemInfo.lynxSdkVersion === '2.16'\n            || SystemInfo.lynxSdkVersion === '2.17'\n            || SystemInfo.lynxSdkVersion === '2.18') {\n            const elementIndex = this.list.__snapshot_def.slot[0][1];\n            const listElement = this.list.__elements[elementIndex];\n            // `__GetAttributeByName` is available since Lynx 2.14\n            if (__GetAttributeByName(listElement, 'custom-list-name') === 'list-container') {\n                // `updateAction` must be full (not incremental) when Lynx version <= 2.18 and\n                // when `custom-list-name` is `list-container` (available when Lynx version >= 2.14) is true,\n                updates.length = 0;\n                this.list.childNodes.forEach((child, index) => {\n                    updates.push({\n                        ...child.__listItemPlatformInfo,\n                        from: index,\n                        to: index,\n                        // no flush\n                        flush: false,\n                        type: child.type,\n                    });\n                });\n            }\n        }\n        return {\n            insertAction: insertions,\n            removeAction: removals,\n            updateAction: updates,\n        };\n    }\n    toJSON() {\n        // if (this.__pendingAttributes) {\n        //   return [...this.__pendingAttributes, this.__toAttribute()];\n        // } else {\n        //   return [this.__toAttribute()];\n        // }\n        return [this.__toAttribute()];\n    }\n}\n//# sourceMappingURL=listUpdateInfo.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { render } from 'preact';\nimport { createContext, createElement } from 'preact/compat';\nimport { useState } from 'preact/hooks';\nimport { factory, withInitDataInState } from './compat/initData.js';\nimport { useLynxGlobalEventListener } from './hooks/useLynxGlobalEventListener.js';\nimport { LifecycleConstant } from './lifecycleConstant.js';\nimport { flushDelayedLifecycleEvents } from './lynx/tt.js';\nimport { __root } from './root.js';\n/**\n * The default and only root of ReactLynx for you to render JSX\n * @example\n * ```ts\n * import { root } from \"@lynx-js/react\"\n * ```\n *\n * @public\n */\nexport const root = {\n    render: (jsx) => {\n        if (__MAIN_THREAD__) {\n            __root.__jsx = jsx;\n        }\n        else {\n            __root.__jsx = jsx;\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            render(jsx, __root);\n            if (__FIRST_SCREEN_SYNC_TIMING__ === 'immediately') {\n                // This is for cases where `root.render()` is called asynchronously,\n                // `firstScreen` message might have been reached.\n                flushDelayedLifecycleEvents();\n            }\n            else {\n                lynx.getNativeApp().callLepusMethod(LifecycleConstant.jsReady, {});\n            }\n        }\n    },\n    registerDataProcessors: (dataProcessorDefinition) => {\n        lynx.registerDataProcessors(dataProcessorDefinition);\n    },\n};\nconst _InitData = /* @__PURE__ */ factory({\n    createContext,\n    useState,\n    createElement,\n    useLynxGlobalEventListener,\n}, '__initData', 'onDataChanged');\n/**\n * The {@link https://react.dev/reference/react/createContext#provider | Provider} Component that provide `initData`,\n * you must wrap your JSX inside it\n * @group Components\n *\n * @example\n *\n * ```ts\n * import { root } from \"@lynx-js/react\"\n *\n * function App() {\n *   return (\n *     <InitDataConsumer children={(initData) => <view>...</view>}/>\n *   )\n * }\n *\n * root.render(\n *   <InitDataProvider>\n *      <App/>\n *   </InitDataProvider>\n * );\n *\n * ```\n *\n * @public\n */\n// @ts-expect-error make preact and react types work\nexport const InitDataProvider = /* @__PURE__ */ _InitData.Provider();\n/**\n * The {@link https://react.dev/reference/react/createContext#consumer | Consumer} Component that provide `initData`.\n * This should be used with {@link InitDataProvider}\n * @group Components\n * @public\n */\n// @ts-expect-error make preact and react types work\nexport const InitDataConsumer = /* @__PURE__ */ _InitData.Consumer();\n/**\n * A React Hooks for you to get `initData`.\n * If `initData` is changed, a re-render will be triggered automatically.\n *\n * @example\n *\n * ```ts\n * function App() {\n *   const initData = useInitData();\n *\n *   initData.someProperty // use it\n * }\n * ```\n *\n * @public\n */\nexport const useInitData = /* @__PURE__ */ _InitData.use();\n/**\n * A React Hooks for you to get notified when `initData` changed.\n *\n * @example\n * ```ts\n * function App() {\n *   useInitDataChanged((data) => {\n *     data.someProperty // can use it\n *   })\n * }\n * ```\n * @public\n */\nexport const useInitDataChanged = /* @__PURE__ */ _InitData.useChanged();\nexport { withInitDataInState };\nexport { useLynxGlobalEventListener } from './hooks/useLynxGlobalEventListener.js';\nexport { runOnBackground } from './worklet/runOnBackground.js';\nexport { runOnMainThread } from './worklet/runOnMainThread.js';\nexport { MainThreadRef, useMainThreadRef } from './worklet/workletRef.js';\n//# sourceMappingURL=lynx-api.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { options } from 'preact';\n// to make sure preact's hooks to register earlier than ours\nimport './hooks/react.js';\nimport { initProfileHook } from './debug/profile.js';\nimport { document, setupBackgroundDocument } from './document.js';\nimport { replaceCommitHook } from './lifecycle/patch/commit.js';\nimport { addCtxNotFoundEventListener } from './lifecycle/patch/error.js';\nimport { injectUpdateMainThread } from './lifecycle/patch/updateMainThread.js';\nimport { injectCalledByNative } from './lynx/calledByNative.js';\nimport { setupLynxEnv } from './lynx/env.js';\nimport { injectLepusMethods } from './lynx/injectLepusMethods.js';\nimport { initTimingAPI } from './lynx/performance.js';\nimport { injectTt } from './lynx/tt.js';\nexport { runWithForce } from './lynx/runWithForce.js';\n// @ts-expect-error Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature\nif (__MAIN_THREAD__ && typeof globalThis.processEvalResult === 'undefined') {\n    // @ts-expect-error Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature\n    globalThis.processEvalResult = (result, schema) => {\n        return result?.(schema);\n    };\n}\nif (__MAIN_THREAD__) {\n    injectCalledByNative();\n    injectUpdateMainThread();\n    if (__DEV__) {\n        injectLepusMethods();\n    }\n}\n// TODO: replace this with __PROFILE__\nif (__PROFILE__) {\n    // We are profiling both main-thread and background.\n    initProfileHook();\n}\nif (__BACKGROUND__) {\n    // Trick Preact and TypeScript to accept our custom document adapter.\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n    options.document = document;\n    setupBackgroundDocument();\n    injectTt();\n    addCtxNotFoundEventListener();\n    if (process.env['NODE_ENV'] === 'test') { }\n    else {\n        replaceCommitHook();\n        initTimingAPI();\n    }\n}\nsetupLynxEnv();\n//# sourceMappingURL=lynx.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { hydrate } from '../hydrate.js';\nimport { isJSReady, jsReady, jsReadyEventIdSwap, resetJSReady } from '../lifecycle/event/jsReady.js';\nimport { reloadMainThread } from '../lifecycle/reload.js';\nimport { renderMainThread } from '../lifecycle/render.js';\nimport { LifecycleConstant } from '../lifecycleConstant.js';\nimport { ssrHydrateByOpcodes } from '../opcodes.js';\nimport { __pendingListUpdates } from '../pendingListUpdates.js';\nimport { __root, setRoot } from '../root.js';\nimport { applyRefQueue } from '../snapshot/workletRef.js';\nimport { SnapshotInstance, __page, setupPage } from '../snapshot.js';\nimport { isEmptyObject } from '../utils.js';\nimport { markTiming, setPipeline } from './performance.js';\nfunction ssrEncode() {\n    const { __opcodes } = __root;\n    delete __root.__opcodes;\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const oldToJSON = SnapshotInstance.prototype.toJSON;\n    SnapshotInstance.prototype.toJSON = function () {\n        return [\n            this.type,\n            this.__id,\n            this.__elements,\n        ];\n    };\n    try {\n        return JSON.stringify({ __opcodes, __root_values: __root.__values });\n    }\n    finally {\n        SnapshotInstance.prototype.toJSON = oldToJSON;\n    }\n}\nfunction ssrHydrate(info) {\n    const nativePage = __GetPageElement();\n    if (!nativePage) {\n        throw new Error('SSR Hydration Failed! Please check if the SSR content loaded successfully!');\n    }\n    resetJSReady();\n    setupPage(nativePage);\n    const refsMap = __GetTemplateParts(nativePage);\n    const { __opcodes, __root_values } = JSON.parse(info);\n    if (__root_values) {\n        __root.setAttribute('values', __root_values);\n    }\n    ssrHydrateByOpcodes(__opcodes, __root, refsMap);\n    __root.__elements = [nativePage];\n    __root.__element_root = nativePage;\n}\nfunction injectCalledByNative() {\n    if (process.env['NODE_ENV'] !== 'test' && __FIRST_SCREEN_SYNC_TIMING__ !== 'jsReady' && __ENABLE_SSR__) {\n        throw new Error('`firstScreenSyncTiming` must be `jsReady` when SSR is enabled');\n    }\n    const calledByNative = {\n        renderPage,\n        updatePage,\n        updateGlobalProps,\n        getPageData: function () {\n            return null;\n        },\n        removeComponents: function () { },\n        ...(__ENABLE_SSR__ ? { ssrEncode, ssrHydrate } : {}),\n    };\n    Object.assign(globalThis, calledByNative);\n    Object.assign(globalThis, {\n        [LifecycleConstant.jsReady]: jsReady,\n    });\n}\nfunction renderPage(data) {\n    // reset `jsReady` state\n    resetJSReady();\n    lynx.__initData = data ?? {};\n    setupPage(__CreatePage('0', 0));\n    __root.ensureElements();\n    renderMainThread();\n    // always call this before `__FlushElementTree`\n    // (There is an implicit `__FlushElementTree` in `renderPage`)\n    __pendingListUpdates.flush();\n    applyRefQueue();\n    if (__FIRST_SCREEN_SYNC_TIMING__ === 'immediately') {\n        jsReady();\n    }\n}\nfunction updatePage(data, options) {\n    if (options?.reloadTemplate) {\n        reloadMainThread(data, options);\n        return;\n    }\n    if (options?.resetPageData) {\n        lynx.__initData = {};\n    }\n    if (typeof data == 'object' && !isEmptyObject(data)) {\n        lynx.__initData ??= {};\n        Object.assign(lynx.__initData, data);\n    }\n    const flushOptions = options ?? {};\n    if (!isJSReady) {\n        const oldRoot = __root;\n        setRoot(new SnapshotInstance('root'));\n        __root.__jsx = oldRoot.__jsx;\n        setPipeline(options?.pipelineOptions);\n        markTiming('updateDiffVdomStart');\n        {\n            __pendingListUpdates.clear();\n            renderMainThread();\n            // As said by codename `jsReadyEventIdSwap`, this swap will only be used for event remap,\n            // because ref & unref cause by previous render will be ignored\n            hydrate(oldRoot, __root, { skipUnRef: true, swap: jsReadyEventIdSwap });\n            // always call this before `__FlushElementTree`\n            __pendingListUpdates.flush();\n            applyRefQueue();\n        }\n        flushOptions.triggerDataUpdated = true;\n        markTiming('updateDiffVdomEnd');\n    }\n    __FlushElementTree(__page, flushOptions);\n}\nfunction updateGlobalProps(_data, options) {\n    if (options) {\n        __FlushElementTree(__page, options);\n    }\n    else {\n        __FlushElementTree();\n    }\n}\n/**\n * @internal\n */\nexport { injectCalledByNative };\n//# sourceMappingURL=calledByNative.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/* eslint-disable */\nimport { Component } from 'preact';\nimport { PerfSpecificKey, markTimingLegacy } from './performance.js';\nimport { globalFlushOptions } from '../lifecycle/patch/commit.js';\nimport { NEXT_STATE } from '../renderToOpcodes/constants.js';\nif (__JS__) {\n    function reportRefDeprecationError(fnName, newFnName) {\n        if (!__DISABLE_CREATE_SELECTOR_QUERY_INCOMPATIBLE_WARNING__) {\n            lynx.reportError(new Error(`${fnName} is deprecated and has different behavior in ReactLynx 3.0, please use ref or ${newFnName} instead.`));\n        }\n    }\n    const __Component = Component;\n    __Component.prototype._reactAppInstance = lynxCoreInject.tt;\n    __Component.prototype.getNodeRef = function (a, b) {\n        reportRefDeprecationError('getNodeRef', 'lynx.createSelectorQuery');\n        // @ts-expect-error hack lynx-kernel\n        return lynxCoreInject.tt._reactLynx.ReactComponent.prototype.getNodeRef\n            .call({\n            _type: '',\n            // @ts-expect-error hack lynx-kernel\n            _nativeApp: lynxCoreInject.tt._nativeApp,\n            // @ts-expect-error hack lynx-kernel\n            _uiModule: lynxCoreInject.tt._nativeApp.nativeModuleProxy.LynxUIMethodModule,\n            _reactAppInstance: lynxCoreInject.tt,\n        }, a, b);\n    };\n    __Component.prototype.getNodeRefFromRoot = function (a) {\n        reportRefDeprecationError('getNodeRefFromRoot', 'lynx.createSelectorQuery');\n        // @ts-expect-error hack lynx-kernel\n        return lynxCoreInject.tt._reactLynx.ReactComponent.prototype\n            .getNodeRefFromRoot.call({\n            _type: '',\n            // @ts-expect-error hack lynx-kernel\n            _nativeApp: lynxCoreInject.tt._nativeApp,\n            // @ts-expect-error hack lynx-kernel\n            _uiModule: lynxCoreInject.tt._nativeApp.nativeModuleProxy.LynxUIMethodModule,\n            _reactAppInstance: lynxCoreInject.tt,\n        }, a);\n    };\n    __Component.prototype.registerModule = function (name, module) {\n        this._reactAppInstance.registerModule(name, module);\n    };\n    __Component.prototype.getJSModule = function (name) {\n        return this._reactAppInstance.getJSModule(name);\n    };\n    __Component.prototype.addGlobalEventListener = function (eventName, callback, context) {\n        return this._reactAppInstance.getJSModule('GlobalEventEmitter').addListener(eventName, callback, context);\n    };\n    __Component.prototype.getElementById = function (id) {\n        reportRefDeprecationError('getElementById', 'lynx.getElementById');\n        return lynx.getElementById(id);\n    };\n    __Component.prototype.GlobalEventEmitter = lynxCoreInject.tt.GlobalEventEmitter;\n    __Component.prototype.createSelectorQuery = function () {\n        reportRefDeprecationError('createSelectorQuery on component instance', 'lynx.createSelectorQuery');\n        return lynx.createSelectorQuery();\n    };\n    const oldSetState = __Component.prototype.setState;\n    __Component.prototype.setState = function (state, callback) {\n        oldSetState.call(this, state, callback);\n        // @ts-ignore\n        const timingFlag = this[NEXT_STATE][PerfSpecificKey];\n        if (timingFlag) {\n            globalFlushOptions.__lynx_timing_flag = timingFlag;\n            markTimingLegacy('updateSetStateTrigger', timingFlag);\n            this[NEXT_STATE][PerfSpecificKey] = '';\n        }\n    };\n}\n//# sourceMappingURL=component.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { loadLazyBundle } from './lazy-bundle.js';\nexport function loadDynamicJS(url) {\n    if (__LEPUS__) {\n        _ReportError(new Error(`A dynamic import (to \"${url}\") is leaked to Lepus bundle.`), { errorCode: 202 });\n        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n        return Promise.reject();\n    }\n    return new Promise((resolve, reject) => {\n        lynx.requireModuleAsync(url, (err, data) => {\n            if (err) {\n                reject(err);\n            }\n            else {\n                resolve(data);\n            }\n        });\n    });\n}\nexport function __dynamicImport(url, options) {\n    const t = options?.with?.type;\n    if (t === 'component' || t === 'tsx' || t === 'jsx') {\n        return loadLazyBundle(url);\n    }\n    else {\n        return loadDynamicJS(url);\n    }\n}\n//# sourceMappingURL=dynamic-js.js.map", "export function setupLynxEnv() {\n    if (!__LEPUS__) {\n        const { initData, updateData } = lynxCoreInject.tt._params;\n        lynx.__initData = { ...initData, ...updateData };\n        lynx.registerDataProcessors = function () { };\n    }\n    if (__LEPUS__) {\n        lynx.__initData = {\n        /* available only in renderPage */\n        };\n        // @ts-expect-error no type for lynx.SystemInfo\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n        globalThis.SystemInfo = lynx.SystemInfo ?? {};\n        lynx.reportError = function (e) {\n            _ReportError(e, {\n                errorCode: 1101, // ErrCode::LYNX_ERROR_CODE_LEPUS in Lynx/base/debug/error_code.h\n            });\n        };\n        lynx.triggerGlobalEventFromLepus = function (eventName, params) {\n            __OnLifecycleEvent(['globalEventFromLepus', [eventName, params]]);\n        };\n        {\n            // eslint-disable-next-line unicorn/consistent-function-scoping\n            function __name(empty) {\n                return `Native${empty}Modules`;\n            }\n            // TODO(hongzhiyuan.hzy): make sure this is run before any other code (especially code access `NativeModules`)\n            // @ts-expect-error hack\n            if (typeof globalThis[__name('')] === 'undefined') {\n                // @ts-expect-error hack\n                globalThis[__name('')] = undefined;\n            }\n        }\n        lynx.registerDataProcessors = function (dataProcessorDefinition) {\n            let hasDefaultDataProcessorExecuted = false;\n            globalThis.processData = (data, processorName) => {\n                if (__PROFILE__) {\n                    console.profile('processData');\n                }\n                let r;\n                try {\n                    if (processorName) {\n                        r = dataProcessorDefinition?.dataProcessors?.[processorName]?.(data) ?? data;\n                    }\n                    else {\n                        r = dataProcessorDefinition?.defaultDataProcessor?.(data) ?? data;\n                    }\n                }\n                catch (e) {\n                    lynx.reportError(e);\n                    // when there is an error\n                    // we should perform like dataProcessor returns nothing\n                    // so use `{}` rather than `data`\n                    r = {};\n                }\n                if (__PROFILE__) {\n                    console.profileEnd();\n                }\n                if (hasDefaultDataProcessorExecuted === false) {\n                    // @ts-expect-error todo: add types to i18n logic\n                    if (globalThis.__I18N_RESOURCE_TRANSLATION__) {\n                        r = {\n                            ...r,\n                            // @ts-expect-error todo: add types to i18n logic\n                            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                            __I18N_RESOURCE_TRANSLATION__: globalThis.__I18N_RESOURCE_TRANSLATION__,\n                        };\n                    }\n                    // @ts-expect-error todo: add types to __EXTRACT_STR__\n                    if (__EXTRACT_STR__) {\n                        r = {\n                            ...r,\n                            // @ts-expect-error todo: add types to __EXTRACT_STR__\n                            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                            _EXTRACT_STR: __EXTRACT_STR_IDENT_FLAG__,\n                        };\n                    }\n                }\n                if (processorName) { }\n                else {\n                    hasDefaultDataProcessorExecuted = true;\n                }\n                return r;\n            };\n        };\n        // register empty DataProcessors to make sure `globalThis.processData` is set\n        lynx.registerDataProcessors();\n    }\n}\n//# sourceMappingURL=env.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { snapshotInstanceManager } from '../snapshot.js';\nfunction injectLepusMethods() {\n    Object.assign(globalThis, {\n        getUniqueIdListBySnapshotId,\n        getSnapshotIdByUniqueId,\n    });\n}\n/**\n * Get the list of `unique_id` of the fiber element by the SnapshotInstance `__id`.\n */\nfunction getUniqueIdListBySnapshotId({ snapshotId }) {\n    const si = snapshotInstanceManager.values.get(snapshotId);\n    if (si?.__elements?.length) {\n        const uniqueIdList = [];\n        for (const element of si.__elements) {\n            const uniqueId = __GetElementUniqueID(element);\n            uniqueIdList.push(uniqueId);\n        }\n        return {\n            uniqueIdList,\n        };\n    }\n    return null;\n}\n/**\n * Get the SnapshotInstance `__id` of the fiber element by the `unique_id`.\n */\nfunction getSnapshotIdByUniqueId({ uniqueId }) {\n    for (const si of snapshotInstanceManager.values.values()) {\n        if (si?.__elements?.length) {\n            for (const element of si.__elements) {\n                const unique_id = __GetElementUniqueID(element);\n                if (unique_id === uniqueId) {\n                    return {\n                        snapshotId: si.__id,\n                    };\n                }\n            }\n        }\n    }\n    return null;\n}\n/**\n * @internal\n */\nexport { injectLepusMethods };\n//# sourceMappingURL=injectLepusMethods.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { Fragment, lazy as backgroundLazy, createElement } from 'preact/compat';\n/**\n * To make code below works\n * const App1 = lazy(() => import(\"./x\").then(({App1}) => ({default: App1})))\n * const App2 = lazy(() => import(\"./x\").then(({App2}) => ({default: App2})))\n * @internal\n */\nexport const makeSyncThen = function (result) {\n    return function (onF, _onR) {\n        if (onF) {\n            let ret;\n            try {\n                ret = onF(result);\n            }\n            catch (e) {\n                // if (onR) {\n                //   return Promise.resolve(onR(e));\n                // }\n                return Promise.reject(e);\n            }\n            if (ret && typeof ret.then === 'function' /* `thenable` object */) {\n                // lazy(() =>\n                //   import(\"./x\").then(() => new Promise(...))\n                // )\n                // Calling `then` and passing a callback is standard behavior\n                // but in Lepus runtime the callback will never be called\n                // So can be simplified to code below\n                return ret;\n                // TODO(hongzhiyuan.hzy): Avoid warning that cannot be turned-off, so the warning is commented\n                // lynx.reportError(\n                //   new Error(\n                //     'You returned a Promise in promise-chain of lazy-bundle import (eg. `import(\"./x\").then(() => new Promise(...))`), which will cause related Component unavailable at first-screen, '\n                //   ),\n                //   { level: \"warning\" }\n                // );\n            }\n            const p = Promise.resolve(ret);\n            const then = makeSyncThen(ret);\n            p.then = then;\n            return p;\n        }\n        return this;\n    };\n};\n/**\n * Load dynamic component from source. Designed to be used with `lazy`.\n * @param source - where dynamic component template.js locates\n * @returns\n * @public\n */\nexport const loadLazyBundle = /*#__PURE__*/ (() => {\n    lynx.loadLazyBundle = loadLazyBundle;\n    function loadLazyBundle(source) {\n        if (__LEPUS__) {\n            const query = __QueryComponent(source);\n            let result;\n            try {\n                result = query.evalResult;\n            }\n            catch (e) {\n                // Here we cannot return a rejected promise\n                // (which will eventually be an unhandled rejection and cause unnecessary redbox)\n                // But we still need a object in shape of Promise\n                // So we return a Promise which will never resolve or reject,\n                // which fit our principle \"lepus run only once at first-screen\" better\n                return new Promise(() => { });\n            }\n            const r = Promise.resolve(result);\n            // Why we should modify the implementation of `then`?\n            // We should make it `sync` so lepus first-screen render can use result above instantly\n            // We also should keep promise shape\n            r.then = makeSyncThen(result);\n            return r;\n        }\n        else if (__JS__) {\n            const resolver = withSyncResolvers();\n            const callback = result => {\n                const { code, detail } = result;\n                if (code === 0) {\n                    const { schema } = detail;\n                    const exports = lynxCoreInject.tt.getDynamicComponentExports(schema);\n                    // `code === 0` means that the lazy bundle has been successfully parsed. However,\n                    // its javascript files may still fail to run, which would prevent the retrieval of the exports object.\n                    if (exports) {\n                        resolver.resolve(exports);\n                        return;\n                    }\n                }\n                resolver.reject(new Error('Lazy bundle load failed: ' + JSON.stringify(result)));\n            };\n            if (typeof lynx.QueryComponent === 'function') {\n                lynx.QueryComponent(source, callback);\n            }\n            else {\n                lynx.getNativeLynx().QueryComponent(source, callback);\n            }\n            if (resolver.result !== null) {\n                const p = Promise.resolve(resolver.result);\n                p.then = makeSyncThen(resolver.result);\n                return p;\n            }\n            else if (resolver.error === null) {\n                return new Promise((_resolve, _reject) => {\n                    resolver.resolve = _resolve;\n                    resolver.reject = _reject;\n                });\n            }\n            else {\n                return Promise.reject(resolver.error);\n            }\n        }\n        throw new Error('unreachable');\n    }\n    return loadLazyBundle;\n})();\nfunction withSyncResolvers() {\n    'background-only';\n    const resolver = {\n        resolve: (result) => {\n            resolver.result = result;\n        },\n        reject: (error) => {\n            resolver.error = error;\n        },\n        result: null,\n        error: null,\n    };\n    return resolver;\n}\n/**\n * @internal\n */\nexport function mainThreadLazy(loader) {\n    const Lazy = backgroundLazy(loader);\n    function _Lazy(props) {\n        try {\n            // @ts-expect-error `Lazy` returned from `backgroundLazy` should be a FC\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n            return Lazy(props);\n        }\n        catch (e) {\n            // We should never throw at mainThread\n            return createElement(Fragment, {});\n        }\n    }\n    return _Lazy;\n}\n//# sourceMappingURL=lazy-bundle.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { options } from 'preact';\nimport { __globalSnapshotPatch } from '../lifecycle/patch/snapshotPatch.js';\nimport { DIFF } from '../renderToOpcodes/constants.js';\nimport { isSdkVersionGt } from '../utils.js';\nconst PerformanceTimingKeys = [\n    'updateSetStateTrigger',\n    'updateDiffVdomStart',\n    'updateDiffVdomEnd',\n    // updateSetStateTrigger, updateDiffVdomStart and updateDiffVdomEnd is deprecated\n    'diffVdomStart',\n    'diffVdomEnd',\n    'packChangesStart',\n    'packChangesEnd',\n    'parseChangesStart',\n    'parseChangesEnd',\n    'patchChangesStart',\n    'patchChangesEnd',\n    'hydrateParseSnapshotStart',\n    'hydrateParseSnapshotEnd',\n    'mtsRenderStart',\n    'mtsRenderEnd',\n];\nconst PerformanceTimingFlags = {\n    reactLynxHydrate: 'react_lynx_hydrate',\n};\nconst PipelineOrigins = {\n    reactLynxHydrate: 'reactLynxHydrate',\n    updateTriggeredByBts: 'updateTriggeredByBts',\n};\n/**\n * @deprecated used by old timing api(setState timing flag)\n */\nconst PerfSpecificKey = '__lynx_timing_flag';\nlet timingFlag;\nlet shouldMarkDiffVdomStart = false;\nlet shouldMarkDiffVdomEnd = false;\nlet globalPipelineOptions;\n/**\n * @deprecated used by old timing api(setState timing flag)\n */\nfunction markTimingLegacy(key, timingFlag_) {\n    switch (key) {\n        case 'updateSetStateTrigger': {\n            shouldMarkDiffVdomStart = true;\n            shouldMarkDiffVdomEnd = true;\n            timingFlag = timingFlag_;\n            break;\n        }\n        case 'updateDiffVdomStart': {\n            /* v8 ignore start */\n            if (!shouldMarkDiffVdomStart) {\n                return;\n            }\n            /* v8 ignore stop */\n            shouldMarkDiffVdomStart = false;\n            break;\n        }\n        case 'updateDiffVdomEnd': {\n            if (!shouldMarkDiffVdomEnd) {\n                return;\n            }\n            shouldMarkDiffVdomEnd = false;\n            break;\n        }\n    }\n    lynx.getNativeApp().markTiming?.(timingFlag, key);\n}\nfunction beginPipeline(needTimestamps, pipelineOrigin, timingFlag) {\n    globalPipelineOptions = lynx.performance?._generatePipelineOptions?.();\n    if (globalPipelineOptions) {\n        globalPipelineOptions.needTimestamps = needTimestamps;\n        globalPipelineOptions.pipelineOrigin = pipelineOrigin;\n        globalPipelineOptions.dsl = 'reactLynx';\n        switch (pipelineOrigin) {\n            case PipelineOrigins.reactLynxHydrate:\n                globalPipelineOptions.stage = 'hydrate';\n                break;\n            case PipelineOrigins.updateTriggeredByBts:\n                globalPipelineOptions.stage = 'update';\n                break;\n        }\n        if (isSdkVersionGt(3, 0)) {\n            lynx.performance?._onPipelineStart?.(globalPipelineOptions.pipelineID, globalPipelineOptions);\n        }\n        else {\n            lynx.performance?._onPipelineStart?.(globalPipelineOptions.pipelineID);\n        }\n        if (timingFlag) {\n            lynx.performance?._bindPipelineIdWithTimingFlag?.(globalPipelineOptions.pipelineID, timingFlag);\n        }\n    }\n}\nfunction setPipeline(pipeline) {\n    globalPipelineOptions = pipeline;\n}\nfunction markTiming(timestampKey, force) {\n    if (globalPipelineOptions && (force || globalPipelineOptions.needTimestamps)) {\n        lynx.performance?._markTiming?.(globalPipelineOptions.pipelineID, timestampKey);\n    }\n}\nfunction initTimingAPI() {\n    const oldDiff = options[DIFF];\n    options[DIFF] = (vnode) => {\n        // check `__globalSnapshotPatch` to make sure this only runs after hydrate\n        if (__JS__ && __globalSnapshotPatch) {\n            if (!globalPipelineOptions) {\n                beginPipeline(false, PipelineOrigins.updateTriggeredByBts);\n                markTiming('diffVdomStart', true);\n            }\n            if (shouldMarkDiffVdomStart) {\n                markTimingLegacy('updateDiffVdomStart');\n            }\n        }\n        oldDiff?.(vnode);\n    };\n}\n/**\n * @internal\n */\nexport { PerformanceTimingFlags, PipelineOrigins, PerfSpecificKey, markTimingLegacy, initTimingAPI, beginPipeline, markTiming, setPipeline, globalPipelineOptions, };\n//# sourceMappingURL=performance.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { options } from 'preact';\nimport { COMPONENT, DIFF, DIFFED, FORCE } from '../renderToOpcodes/constants.js';\nconst sForcedVNode = Symbol('FORCE');\nexport function runWithForce(cb) {\n    // save vnode and its `_component` in WeakMap\n    const m = new WeakMap();\n    const oldDiff = options[DIFF];\n    options[DIFF] = (vnode) => {\n        if (oldDiff) {\n            oldDiff(vnode);\n        }\n        // when `options[DIFF]` is called, a newVnode is passed in\n        // so its `vnode[COMPONENT]` should be null,\n        // but it will be set later\n        Object.defineProperty(vnode, COMPONENT, {\n            configurable: true,\n            set(c) {\n                m.set(vnode, c);\n                if (c) {\n                    c[FORCE] = true;\n                }\n            },\n            get() {\n                return m.get(vnode);\n            },\n        });\n        vnode[sForcedVNode] = true;\n    };\n    const oldDiffed = options[DIFFED];\n    options[DIFFED] = (vnode) => {\n        if (oldDiffed) {\n            oldDiffed(vnode);\n        }\n        // There would be cases when `options[DIFF]` has been reset while options[DIFFED] is not,\n        // so we need to check if `vnode` is patched by `options[DIFF]`.\n        // We only want to change the patched vnode\n        if (vnode[sForcedVNode]) {\n            // delete is a reverse operation of previous `Object.defineProperty`\n            delete vnode[COMPONENT];\n            delete vnode[sForcedVNode];\n            // restore\n            vnode[COMPONENT] = m.get(vnode);\n        }\n    };\n    try {\n        cb();\n    }\n    finally {\n        options[DIFF] = oldDiff;\n        options[DIFFED] = oldDiffed;\n    }\n}\n//# sourceMappingURL=runWithForce.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { hydrate } from './hydrate.js';\nimport { componentAtIndexFactory, enqueueComponentFactory, gRecycleMap, gSignMap } from './list.js';\nimport { CHILDREN } from './renderToOpcodes/constants.js';\nimport { SnapshotInstance } from './snapshot.js';\nvar Opcode;\n(function (Opcode) {\n    Opcode[Opcode[\"Begin\"] = 0] = \"Begin\";\n    Opcode[Opcode[\"End\"] = 1] = \"End\";\n    Opcode[Opcode[\"Attr\"] = 2] = \"Attr\";\n    Opcode[Opcode[\"Text\"] = 3] = \"Text\";\n})(Opcode || (Opcode = {}));\nexport function ssrHydrateByOpcodes(opcodes, into, refMap) {\n    let top = into;\n    const stack = [into];\n    for (let i = 0; i < opcodes.length;) {\n        const opcode = opcodes[i];\n        switch (opcode) {\n            case Opcode.Begin: {\n                const p = top;\n                const [type, __id, elements] = opcodes[i + 1];\n                top = new SnapshotInstance(type, __id);\n                top.__pendingElements = elements;\n                p.insertBefore(top);\n                stack.push(top);\n                i += 2;\n                break;\n            }\n            case Opcode.End: {\n                // @ts-ignore\n                top[CHILDREN] = undefined;\n                top.__elements = top.__pendingElements.map(({ ssrID }) => refMap[ssrID]);\n                top.__element_root = top.__elements[0];\n                delete top.__pendingElements;\n                if (top.__snapshot_def.isListHolder) {\n                    const listElement = top.__element_root;\n                    const listElementUniqueID = __GetElementUniqueID(listElement);\n                    const signMap = gSignMap[listElementUniqueID] = new Map();\n                    gRecycleMap[listElementUniqueID] = new Map();\n                    const enqueueFunc = enqueueComponentFactory();\n                    const [componentAtIndex, componentAtIndexes] = componentAtIndexFactory(top.childNodes, hydrate);\n                    for (const child of top.childNodes) {\n                        if (child.__element_root) {\n                            const childElementUniqueID = __GetElementUniqueID(child.__element_root);\n                            signMap.set(childElementUniqueID, child);\n                            enqueueFunc(listElement, listElementUniqueID, childElementUniqueID);\n                        }\n                    }\n                    __UpdateListCallbacks(listElement, componentAtIndex, enqueueFunc, componentAtIndexes);\n                }\n                stack.pop();\n                const p = stack[stack.length - 1];\n                top = p;\n                i += 1;\n                break;\n            }\n            case Opcode.Attr: {\n                const key = opcodes[i + 1];\n                const value = opcodes[i + 2];\n                top.setAttribute(key, value);\n                i += 3;\n                break;\n            }\n            case Opcode.Text: {\n                const [[type, __id, elements], text] = opcodes[i + 1];\n                const s = new SnapshotInstance(type, __id);\n                s.setAttribute(0, text);\n                top.insertBefore(s);\n                s.__elements = elements.map(({ ssrID }) => refMap[ssrID]);\n                s.__element_root = s.__elements[0];\n                i += 2;\n                break;\n            }\n        }\n    }\n}\nexport function renderOpcodesInto(opcodes, into) {\n    let top = into;\n    const stack = [into];\n    for (let i = 0; i < opcodes.length;) {\n        const opcode = opcodes[i];\n        switch (opcode) {\n            case Opcode.Begin: {\n                const p = top;\n                top = opcodes[i + 1];\n                // @ts-ignore\n                if (top.__parent) {\n                    // already inserted\n                    top = new SnapshotInstance(top.type);\n                    opcodes[i + 1] = top;\n                }\n                p.insertBefore(top);\n                stack.push(top);\n                i += 2;\n                break;\n            }\n            case Opcode.End: {\n                // @ts-ignore\n                top[CHILDREN] = undefined;\n                stack.pop();\n                const p = stack[stack.length - 1];\n                top = p;\n                i += 1;\n                break;\n            }\n            case Opcode.Attr: {\n                const key = opcodes[i + 1];\n                const value = opcodes[i + 2];\n                top.setAttribute(key, value);\n                i += 3;\n                break;\n            }\n            case Opcode.Text: {\n                const text = opcodes[i + 1];\n                const s = new SnapshotInstance(null);\n                if (__ENABLE_SSR__) {\n                    // We need store the just created SnapshotInstance, or it will be lost when we leave the function\n                    opcodes[i + 1] = [s, text];\n                }\n                s.setAttribute(0, text);\n                top.insertBefore(s);\n                i += 2;\n                break;\n            }\n        }\n    }\n}\n//# sourceMappingURL=opcodes.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nexport const __pendingListUpdates = {\n    values: {},\n    clear() {\n        this.values = {};\n    },\n    flush() {\n        Object.values(this.values).forEach(update => {\n            update.flush();\n        });\n        this.clear();\n    },\n};\n//# sourceMappingURL=pendingListUpdates.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nexport const DIFF = '__b';\nexport const RENDER = '__r';\nexport const DIFFED = 'diffed';\nexport const COMMIT = '__c';\nexport const SKIP_EFFECTS = '__s';\nexport const CATCH_ERROR = '__e';\n// VNode properties\nexport const COMPONENT = '__c';\nexport const CHILDREN = '__k';\nexport const PARENT = '__';\nexport const MASK = '__m';\n// Component properties\nexport const VNODE = '__v';\nexport const DIRTY = '__d';\nexport const FORCE = '__e';\nexport const NEXT_STATE = '__s';\nexport const CHILD_DID_SUSPEND = '__c';\nexport const RENDER_CALLBACKS = '__h';\nexport const HOOK = '__h';\n//# sourceMappingURL=constants.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/**\n * Implements rendering to opcodes.\n * This module is modified from preact-render-to-string@6.0.3 to generate\n * opcodes instead of HTML strings for Lynx.\n */\n// @ts-nocheck\nimport { Fragment, h, options } from 'preact';\nimport { CHILDREN, COMMIT, CO<PERSON><PERSON>ENT, DIFF, DIFFED, DIRTY, NEXT_STATE, PARENT, RENDER, SKIP_EFFECTS, VNODE, } from './constants.js';\n/** @typedef {import('preact').VNode} VNode */\nconst EMPTY_ARR = [];\nconst isArray = /* @__PURE__ */ Array.isArray;\nconst assign = /* @__PURE__ */ Object.assign;\n// Global state for the current render pass\nlet beforeDiff, afterDiff, renderHook, ummountHook;\n/**\n * Render Preact JSX + Components to an HTML string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {object} [context] Initial root context object\n */\nexport function renderToString(vnode, context) {\n    // Performance optimization: `renderToString` is synchronous and we\n    // therefore don't execute any effects. To do that we pass an empty\n    // array to `options._commit` (`__c`). But we can go one step further\n    // and avoid a lot of dirty checks and allocations by setting\n    // `options._skipEffects` (`__s`) too.\n    const previousSkipEffects = options[SKIP_EFFECTS];\n    options[SKIP_EFFECTS] = true;\n    // store options hooks once before each synchronous render call\n    beforeDiff = options[DIFF];\n    afterDiff = options[DIFFED];\n    renderHook = options[RENDER];\n    ummountHook = options.unmount;\n    const parent = h(Fragment, null);\n    parent[CHILDREN] = [vnode];\n    const opcodes = [];\n    try {\n        _renderToString(vnode, context || EMPTY_OBJ, false, undefined, parent, opcodes);\n    }\n    finally {\n        // options._commit, we don't schedule any effects in this library right now,\n        // so we can pass an empty queue to this hook.\n        if (options[COMMIT])\n            options[COMMIT](vnode, EMPTY_ARR);\n        options[SKIP_EFFECTS] = previousSkipEffects;\n        EMPTY_ARR.length = 0;\n    }\n    return opcodes;\n}\n// Installed as setState/forceUpdate for function components\nfunction markAsDirty() {\n    this.__d = true;\n}\nconst EMPTY_OBJ = {};\nexport const __OpBegin = 0;\nexport const __OpEnd = 1;\nexport const __OpAttr = 2;\nexport const __OpText = 3;\n/**\n * @param {VNode} vnode\n * @param {Record<string, unknown>} context\n */\nfunction renderClassComponent(vnode, context) {\n    const type = /** @type {import(\"preact\").ComponentClass<typeof vnode.props>} */ (vnode.type);\n    const c = new type(vnode.props, context);\n    vnode[COMPONENT] = c;\n    c[VNODE] = vnode;\n    c.props = vnode.props;\n    c.context = context;\n    // turn off stateful re-rendering:\n    c[DIRTY] = true;\n    if (c.state == null)\n        c.state = EMPTY_OBJ;\n    if (c[NEXT_STATE] == null) {\n        c[NEXT_STATE] = c.state;\n    }\n    if (type.getDerivedStateFromProps) {\n        c.state = assign({}, c.state, type.getDerivedStateFromProps(c.props, c.state));\n    }\n    if (renderHook)\n        renderHook(vnode);\n    return c.render(c.props, c.state, context);\n}\n/**\n * Recursively render VNodes to HTML.\n * @param {VNode|any} vnode\n * @param {any} context\n * @param {boolean} isSvgMode\n * @param {any} selectValue\n * @param {VNode} parent\n * @param opcodes\n */\nfunction _renderToString(vnode, context, isSvgMode, selectValue, parent, opcodes) {\n    // Ignore non-rendered VNodes/values\n    if (vnode == null || vnode === true || vnode === false || vnode === '') {\n        return;\n    }\n    // Text VNodes: escape as HTML\n    if (typeof vnode !== 'object') {\n        if (typeof vnode === 'function')\n            return;\n        opcodes.push(__OpText, vnode + '');\n        return;\n    }\n    // Recurse into children / Arrays\n    if (isArray(vnode)) {\n        parent[CHILDREN] = vnode;\n        for (let i = 0; i < vnode.length; i++) {\n            const child = vnode[i];\n            if (child == null || typeof child === 'boolean')\n                continue;\n            _renderToString(child, context, isSvgMode, selectValue, parent, opcodes);\n        }\n        return;\n    }\n    // VNodes have {constructor:undefined} to prevent JSON injection:\n    // if (vnode.constructor !== undefined) return;\n    vnode[PARENT] = parent;\n    if (beforeDiff)\n        beforeDiff(vnode);\n    let type = vnode.type, props = vnode.props, cctx = context, contextType, rendered, component;\n    // Invoke rendering on Components\n    if (typeof type === 'function') {\n        if (type === Fragment) {\n            rendered = props.children;\n        }\n        else {\n            contextType = type.contextType;\n            if (contextType != null) {\n                const provider = context[contextType.__c];\n                cctx = provider ? provider.props.value : contextType.__;\n            }\n            if (type.prototype && typeof type.prototype.render === 'function') {\n                rendered = /**#__NOINLINE__**/ renderClassComponent(vnode, cctx);\n                component = vnode[COMPONENT];\n            }\n            else {\n                component = {\n                    __v: vnode,\n                    props,\n                    context: cctx,\n                    // silently drop state updates\n                    setState: markAsDirty,\n                    forceUpdate: markAsDirty,\n                    __d: true,\n                    // hooks\n                    __h: [],\n                };\n                vnode[COMPONENT] = component;\n                component.constructor = type;\n                component.render = doRender;\n                // If a hook invokes setState() to invalidate the component during rendering,\n                // re-render it up to 25 times to allow \"settling\" of memoized states.\n                // Note:\n                //   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n                //   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n                let count = 0;\n                while (component[DIRTY] && count++ < 25) {\n                    component[DIRTY] = false;\n                    if (renderHook)\n                        renderHook(vnode);\n                    rendered = component.render(props, component.state, cctx);\n                }\n                component[DIRTY] = true;\n            }\n            if (component.getChildContext != null) {\n                context = assign({}, context, component.getChildContext());\n            }\n        }\n        // When a component returns a Fragment node we flatten it in core, so we\n        // need to mirror that logic here too\n        const isTopLevelFragment = rendered != null && rendered.type === Fragment\n            && rendered.key == null;\n        rendered = isTopLevelFragment ? rendered.props.children : rendered;\n        // Recurse into children before invoking the after-diff hook\n        _renderToString(rendered, context, isSvgMode, selectValue, vnode, opcodes);\n        if (afterDiff)\n            afterDiff(vnode);\n        vnode[PARENT] = undefined;\n        if (ummountHook)\n            ummountHook(vnode);\n        return;\n    }\n    let children;\n    opcodes.push(__OpBegin, vnode);\n    for (const name in props) {\n        const v = props[name];\n        switch (name) {\n            case 'children':\n                children = v;\n                continue;\n            // VDOM-specific props\n            /* c8 ignore next 5 */\n            case 'key':\n            case 'ref':\n            case '__self':\n            case '__source':\n                continue;\n            default: { }\n        }\n        // write this attribute to the buffer\n        if (v != null && v !== false && typeof v !== 'function') {\n            opcodes.push(__OpAttr, name, v);\n        }\n    }\n    if (typeof children === 'string') {\n        // single text child\n        opcodes.push(__OpText, children);\n    }\n    else if (children != null && children !== false && children !== true) {\n        // recurse into this element VNode's children\n        _renderToString(children, context, false, selectValue, vnode, opcodes);\n    }\n    if (afterDiff)\n        afterDiff(vnode);\n    vnode[PARENT] = undefined;\n    if (ummountHook)\n        ummountHook(vnode);\n    opcodes.push(__OpEnd);\n    return;\n}\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n    return this.constructor(props, context);\n}\nexport default renderToString;\nexport const render = renderToString;\nexport const renderToStaticMarkup = renderToString;\n//# sourceMappingURL=index.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { BackgroundSnapshotInstance } from './backgroundSnapshot.js';\nimport { SnapshotInstance } from './snapshot.js';\n/**\n * The internal ReactLynx's root.\n * {@link @lynx-js/react!Root | root}.\n */\nlet __root;\nfunction setRoot(root) {\n    __root = root;\n}\nif (__MAIN_THREAD__) {\n    setRoot(new SnapshotInstance('root'));\n}\nelse if (__BACKGROUND__) {\n    setRoot(new BackgroundSnapshotInstance('root'));\n}\nexport { __root, setRoot };\n//# sourceMappingURL=root.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { SnapshotOperation, __globalSnapshotPatch } from './lifecycle/patch/snapshotPatch.js';\nimport { ListUpdateInfoRecording } from './listUpdateInfo.js';\nimport { __pendingListUpdates } from './pendingListUpdates.js';\nimport { DynamicPartType } from './snapshot/dynamicPartType.js';\nimport { snapshotDestroyList } from './snapshot/list.js';\nimport { unref } from './snapshot/ref.js';\nimport { isDirectOrDeepEqual } from './utils.js';\nexport let __page;\nexport let __pageId = 0;\nexport function setupPage(page) {\n    __page = page;\n    __pageId = __GetElementUniqueID(page);\n}\nexport function clearPage() {\n    __page = undefined;\n    __pageId = 0;\n}\nexport const snapshotManager = {\n    values: /* @__PURE__ */ new Map([\n        [\n            'root',\n            {\n                create() {\n                    /* v8 ignore start */\n                    if (__JS__ && !__DEV__) {\n                        return [];\n                    }\n                    /* v8 ignore stop */\n                    return [__page];\n                },\n                update: [],\n                slot: [[DynamicPartType.Children, 0]],\n                isListHolder: false,\n                cssId: 0,\n            },\n        ],\n        [\n            'wrapper',\n            {\n                create() {\n                    /* v8 ignore start */\n                    if (__JS__ && !__DEV__) {\n                        return [];\n                    }\n                    /* v8 ignore stop */\n                    return [__CreateWrapperElement(__pageId)];\n                },\n                update: [],\n                slot: [[DynamicPartType.Children, 0]],\n                isListHolder: false,\n            },\n        ],\n        [\n            null,\n            {\n                create() {\n                    /* v8 ignore start */\n                    if (__JS__ && !__DEV__) {\n                        return [];\n                    }\n                    /* v8 ignore stop */\n                    return [__CreateElement('raw-text', __pageId)];\n                },\n                update: [\n                    ctx => {\n                        /* v8 ignore start */\n                        if (__JS__ && !__DEV__) {\n                            return;\n                        }\n                        /* v8 ignore stop */\n                        if (ctx.__elements) {\n                            __SetAttribute(ctx.__elements[0], 'text', ctx.__values[0]);\n                        }\n                    },\n                ],\n                slot: [],\n                isListHolder: false,\n            },\n        ],\n    ]),\n};\nexport const snapshotInstanceManager = {\n    nextId: 0,\n    values: /* @__PURE__ */ new Map(),\n    clear() {\n        // not resetting `nextId` to prevent id collision\n        this.values.clear();\n    },\n};\nexport const backgroundSnapshotInstanceManager = {\n    nextId: 0,\n    values: /* @__PURE__ */ new Map(),\n    clear() {\n        // not resetting `nextId` to prevent id collision\n        this.values.clear();\n    },\n    updateId(id, newId) {\n        const values = this.values;\n        const si = values.get(id);\n        values.delete(id);\n        values.set(newId, si);\n        si.__id = newId;\n    },\n    getValueBySign(str) {\n        const res = str?.split(':');\n        if (!res || (res.length != 2 && res.length != 3)) {\n            throw new Error('Invalid ctx format: ' + str);\n        }\n        const id = Number(res[0]);\n        const expIndex = Number(res[1]);\n        const ctx = this.values.get(id);\n        if (!ctx) {\n            return null;\n        }\n        const spreadKey = res[2];\n        if (res[1] === '__extraProps') {\n            if (spreadKey) {\n                return ctx.__extraProps[spreadKey];\n            }\n            throw new Error('unreachable');\n        }\n        else {\n            if (spreadKey) {\n                return ctx.__values[expIndex][spreadKey];\n            }\n            else {\n                return ctx.__values[expIndex];\n            }\n        }\n    },\n};\nexport function entryUniqID(uniqID, entryName) {\n    return entryName ? `${entryName}:${uniqID}` : uniqID;\n}\nexport function createSnapshot(uniqID, create, update, slot, cssId, entryName, refAndSpreadIndexes) {\n    if (__DEV__ && __JS__\n        // `__globalSnapshotPatch` does not exist before hydration,\n        // so the snapshot of the first screen will not be sent to the main thread.\n        && __globalSnapshotPatch\n        && !snapshotManager.values.has(entryUniqID(uniqID, entryName))\n        // `create` may be `null` when loading a lazy bundle after hydration.\n        && create !== null) {\n        // We only update the lepus snapshot if the `uniqID` is different.\n        // This means that `uniqID` is considered the \"hash\" of the snapshot.\n        // When HMR (Hot Module Replacement) or fast refresh updates occur, `createSnapshot` will be re-executed with the new snapshot definition.\n        __globalSnapshotPatch.push(SnapshotOperation.DEV_ONLY_AddSnapshot, uniqID, \n        // We use `Function.prototype.toString` to serialize the `create` and `update` functions for Lepus.\n        // This allows the updates to be applied to Lepus.\n        // As a result, both the static part (`create`) and the dynamic parts (`update` and `slot`) can be updated.\n        create.toString(), update?.map(f => f.toString()) ?? [], slot, cssId, entryName);\n    }\n    uniqID = entryUniqID(uniqID, entryName);\n    const s = { create, update, slot, cssId, entryName, refAndSpreadIndexes };\n    snapshotManager.values.set(uniqID, s);\n    if (slot && slot[0] && slot[0][0] === DynamicPartType.ListChildren) {\n        s.isListHolder = true;\n    }\n    return uniqID;\n}\nexport function traverseSnapshotInstance(si, callback) {\n    const c = si.childNodes;\n    callback(si);\n    for (const vv of c) {\n        traverseSnapshotInstance(vv, callback);\n    }\n}\nconst DEFAULT_ENTRY_NAME = '__Card__';\nconst DEFAULT_CSS_ID = 0;\n/**\n * The runtime instance of a {@link Snapshot} on the main thread that manages\n * the actual elements and handles updates to dynamic parts.\n *\n * This class is designed to be compatible with Preact's {@link ContainerNode}\n * interface for Preact's renderer to operate upon.\n */\nexport class SnapshotInstance {\n    type;\n    __id;\n    __snapshot_def;\n    __elements;\n    __element_root;\n    __values;\n    __current_slot_index = 0;\n    __worklet_ref_set;\n    __listItemPlatformInfo;\n    __extraProps;\n    constructor(type, id) {\n        this.type = type;\n        this.__snapshot_def = snapshotManager.values.get(type);\n        // Suspense uses 'div'\n        if (!this.__snapshot_def && type !== 'div') {\n            throw new Error('Snapshot not found: ' + type);\n        }\n        id ??= snapshotInstanceManager.nextId -= 1;\n        this.__id = id;\n        snapshotInstanceManager.values.set(id, this);\n    }\n    ensureElements() {\n        const { create, slot, isListHolder, cssId, entryName } = this.__snapshot_def;\n        const elements = create(this);\n        this.__elements = elements;\n        this.__element_root = elements[0];\n        if (cssId === undefined) {\n            // This means either:\n            //   CSS Scope is removed(We only need to call `__SetCSSId` when there is `entryName`)\n            //   Or an old bundle(`__SetCSSId` is called in `create`), we skip calling `__SetCSSId`\n            if (entryName !== DEFAULT_ENTRY_NAME && entryName !== undefined) {\n                __SetCSSId(this.__elements, DEFAULT_CSS_ID, entryName);\n            }\n        }\n        else {\n            // cssId !== undefined\n            if (entryName !== DEFAULT_ENTRY_NAME && entryName !== undefined) {\n                // For lazy bundle, we need add `entryName` to the third params\n                __SetCSSId(this.__elements, cssId, entryName);\n            }\n            else {\n                __SetCSSId(this.__elements, cssId);\n            }\n        }\n        const values = this.__values;\n        if (values) {\n            this.__values = undefined;\n            this.setAttribute('values', values);\n        }\n        if (isListHolder) {\n            // never recurse into list's children\n        }\n        else {\n            let index = 0;\n            let child = this.__firstChild;\n            while (child) {\n                child.ensureElements();\n                const [type, elementIndex] = slot[index];\n                switch (type) {\n                    case DynamicPartType.Slot: {\n                        __ReplaceElement(child.__element_root, elements[elementIndex]);\n                        elements[elementIndex] = child.__element_root;\n                        index++;\n                        break;\n                    }\n                    /* v8 ignore start */\n                    case DynamicPartType.MultiChildren: {\n                        if (__GetTag(elements[elementIndex]) === 'wrapper') {\n                            __ReplaceElement(child.__element_root, elements[elementIndex]);\n                        }\n                        else {\n                            __AppendElement(elements[elementIndex], child.__element_root);\n                        }\n                        index++;\n                        break;\n                    }\n                    /* v8 ignore end */\n                    case DynamicPartType.Children:\n                    case DynamicPartType.ListChildren: {\n                        __AppendElement(elements[elementIndex], child.__element_root);\n                        break;\n                    }\n                }\n                child = child.__nextSibling;\n            }\n        }\n    }\n    unRenderElements() {\n        const { isListHolder } = this.__snapshot_def;\n        this.__elements = undefined;\n        this.__element_root = undefined;\n        if (isListHolder) {\n            // never recurse into list's children\n        }\n        else {\n            let child = this.__firstChild;\n            while (child) {\n                child.unRenderElements();\n                child = child.__nextSibling;\n            }\n        }\n    }\n    takeElements() {\n        const a = Object.create(SnapshotInstance.prototype);\n        a.__id = this.__id;\n        a.__snapshot_def = this.__snapshot_def;\n        a.__values = this.__values;\n        // all clear\n        a.__parent = null;\n        a.__firstChild = null;\n        a.__lastChild = null;\n        a.__nextSibling = null;\n        a.__previousSibling = null;\n        this.childNodes.map(c => c.takeElements()).forEach(node => a.__insertBefore(node));\n        a.__elements = this.__elements;\n        a.__element_root = this.__element_root;\n        this.__elements = undefined;\n        this.__element_root = undefined;\n        return a;\n    }\n    tearDown() {\n        traverseSnapshotInstance(this, v => {\n            v.__parent = null;\n            v.__previousSibling = null;\n            v.__nextSibling = null;\n        });\n    }\n    // onCreate?: () => void;\n    // onAttach?: () => void;\n    // onDetach?: () => void;\n    // onRef?: () => void;\n    // onUnref?: () => void;\n    __parent = null;\n    __firstChild = null;\n    __lastChild = null;\n    __previousSibling = null;\n    __nextSibling = null;\n    get parentNode() {\n        return this.__parent;\n    }\n    get nextSibling() {\n        return this.__nextSibling;\n    }\n    // get isConnected() {\n    //   return !!this.__parent;\n    // }\n    contains(child) {\n        return child.parentNode === this;\n    }\n    get childNodes() {\n        const nodes = [];\n        let node = this.__firstChild;\n        while (node) {\n            nodes.push(node);\n            node = node.__nextSibling;\n        }\n        return nodes;\n    }\n    __insertBefore(node, beforeNode) {\n        // If the node already has a parent, remove it from its current parent\n        if (node.__parent) {\n            node.__parent.__removeChild(node);\n        }\n        // If beforeNode is not provided, add the new node as the last child\n        if (beforeNode) {\n            // If beforeNode is provided, insert the new node before beforeNode\n            if (beforeNode.__previousSibling) {\n                beforeNode.__previousSibling.__nextSibling = node;\n                node.__previousSibling = beforeNode.__previousSibling;\n            }\n            else {\n                this.__firstChild = node;\n                node.__previousSibling = null;\n            }\n            beforeNode.__previousSibling = node;\n            node.__nextSibling = beforeNode;\n            node.__parent = this;\n        }\n        else {\n            if (this.__lastChild) {\n                this.__lastChild.__nextSibling = node;\n                node.__previousSibling = this.__lastChild;\n            }\n            else {\n                this.__firstChild = node;\n                node.__previousSibling = null;\n            }\n            this.__lastChild = node;\n            node.__parent = this;\n            node.__nextSibling = null;\n        }\n    }\n    __removeChild(node) {\n        if (node.__parent !== this) {\n            throw new Error('The node to be removed is not a child of this node.');\n        }\n        if (node.__previousSibling) {\n            node.__previousSibling.__nextSibling = node.__nextSibling;\n        }\n        else {\n            this.__firstChild = node.__nextSibling;\n        }\n        if (node.__nextSibling) {\n            node.__nextSibling.__previousSibling = node.__previousSibling;\n        }\n        else {\n            this.__lastChild = node.__previousSibling;\n        }\n        node.__parent = null;\n        node.__previousSibling = null;\n        node.__nextSibling = null;\n    }\n    insertBefore(newNode, existingNode) {\n        const __snapshot_def = this.__snapshot_def;\n        if (__snapshot_def.isListHolder) {\n            (__pendingListUpdates.values[this.__id] ??= new ListUpdateInfoRecording(this)).onInsertBefore(newNode, existingNode);\n            this.__insertBefore(newNode, existingNode);\n            return;\n        }\n        const shouldRemove = newNode.__parent === this;\n        this.__insertBefore(newNode, existingNode);\n        const __elements = this.__elements;\n        if (__elements) {\n            if (!newNode.__elements) {\n                newNode.ensureElements();\n            }\n        }\n        else {\n            return;\n        }\n        const count = __snapshot_def.slot.length;\n        if (count === 1) {\n            const [, elementIndex] = __snapshot_def.slot[0];\n            const parent = __elements[elementIndex];\n            if (shouldRemove) {\n                __RemoveElement(parent, newNode.__element_root);\n            }\n            if (existingNode) {\n                __InsertElementBefore(parent, newNode.__element_root, existingNode.__element_root);\n            }\n            else {\n                __AppendElement(parent, newNode.__element_root);\n            }\n        }\n        else if (count > 1) {\n            const index = this.__current_slot_index++;\n            const [s, elementIndex] = __snapshot_def.slot[index];\n            if (s === DynamicPartType.Slot) {\n                __ReplaceElement(newNode.__element_root, __elements[elementIndex]);\n                __elements[elementIndex] = newNode.__element_root;\n                /* v8 ignore start */\n            }\n            else if (s === DynamicPartType.MultiChildren) {\n                if (__GetTag(__elements[elementIndex]) === 'wrapper') {\n                    __ReplaceElement(newNode.__element_root, __elements[elementIndex]);\n                }\n                else {\n                    __AppendElement(__elements[elementIndex], newNode.__element_root);\n                }\n            }\n            /* v8 ignore end */\n        }\n    }\n    removeChild(child) {\n        const __snapshot_def = this.__snapshot_def;\n        if (__snapshot_def.isListHolder) {\n            (__pendingListUpdates.values[this.__id] ??= new ListUpdateInfoRecording(this)).onRemoveChild(child);\n            this.__removeChild(child);\n            traverseSnapshotInstance(child, v => {\n                snapshotInstanceManager.values.delete(v.__id);\n            });\n            // mark this child as deleted\n            child.__id = 0;\n            return;\n        }\n        unref(child, true);\n        if (this.__elements) {\n            const [, elementIndex] = __snapshot_def.slot[0];\n            __RemoveElement(this.__elements[elementIndex], child.__element_root);\n        }\n        if (child.__snapshot_def.isListHolder) {\n            snapshotDestroyList(child);\n        }\n        this.__removeChild(child);\n        traverseSnapshotInstance(child, v => {\n            v.__parent = null;\n            v.__previousSibling = null;\n            v.__nextSibling = null;\n            delete v.__elements;\n            delete v.__element_root;\n            snapshotInstanceManager.values.delete(v.__id);\n        });\n    }\n    setAttribute(key, value) {\n        if (key === 'values') {\n            const oldValues = this.__values;\n            const values = value;\n            this.__values = values;\n            if (oldValues) {\n                for (let index = 0; index < values.length; index++) {\n                    this.callUpdateIfNotDirectOrDeepEqual(index, oldValues[index], values[index]);\n                }\n            }\n            else {\n                for (let index = 0; index < values.length; index++) {\n                    this.callUpdateIfNotDirectOrDeepEqual(index, undefined, values[index]);\n                }\n            }\n            return;\n        }\n        if (typeof key === 'string') {\n            // for more flexible usage, we allow setting non-indexed attributes\n            (this.__extraProps ??= {})[key] = value;\n            return;\n        }\n        this.__values ??= [];\n        this.callUpdateIfNotDirectOrDeepEqual(key, this.__values[key], this.__values[key] = value);\n    }\n    toJSON() {\n        return {\n            id: this.__id,\n            type: this.type,\n            values: this.__values,\n            extraProps: this.__extraProps,\n            children: this.__firstChild ? this.childNodes : undefined,\n        };\n    }\n    callUpdateIfNotDirectOrDeepEqual(index, oldValue, newValue) {\n        if (isDirectOrDeepEqual(oldValue, newValue)) { }\n        else {\n            this.__snapshot_def.update[index](this, index, oldValue);\n        }\n    }\n}\n//# sourceMappingURL=snapshot.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/**\n * A map to store hydration states between snapshot instances.\n * K->V: main thread snapshotInstance IDs -> background snapshotInstance IDs.\n *\n * The map is used by the ref system to translate between snapshot instance IDs when\n * operations need to cross the thread boundary during the commit phase.\n */\nconst hydrationMap = new Map();\n/**\n * @internal\n */\nexport { hydrationMap };\n//# sourceMappingURL=snapshotInstanceHydrationMap.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/**\n * Types of dynamic parts that can be updated in a snapshot\n * These are determined at compile time through static analysis\n */\nexport var DynamicPartType;\n(function (DynamicPartType) {\n    DynamicPartType[DynamicPartType[\"Attr\"] = 0] = \"Attr\";\n    DynamicPartType[DynamicPartType[\"Spread\"] = 1] = \"Spread\";\n    DynamicPartType[DynamicPartType[\"Slot\"] = 2] = \"Slot\";\n    DynamicPartType[DynamicPartType[\"Children\"] = 3] = \"Children\";\n    DynamicPartType[DynamicPartType[\"ListChildren\"] = 4] = \"ListChildren\";\n    DynamicPartType[DynamicPartType[\"MultiChildren\"] = 5] = \"MultiChildren\";\n})(DynamicPartType || (DynamicPartType = {}));\n//# sourceMappingURL=dynamicPartType.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { SnapshotInstance } from '../snapshot.js';\nfunction updateEvent(snapshot, expIndex, _oldValue, elementIndex, eventType, eventName, spreadKey) {\n    const value = snapshot.__values[expIndex];\n    let event;\n    if (!value) {\n        event = undefined;\n    }\n    else if (typeof value === 'string') {\n        event = value;\n    }\n    else {\n        event = `${snapshot.__id}:${expIndex}:${spreadKey}`;\n    }\n    // todo: reuseId?\n    snapshot.__values[expIndex] = event;\n    if (snapshot.__elements) {\n        __AddEvent(snapshot.__elements[elementIndex], eventType, eventName, event);\n    }\n}\nexport { updateEvent };\n//# sourceMappingURL=event.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { processGesture } from '../gesture/processGesture.js';\nimport { isMainThreadHydrationFinished } from '../lifecycle/patch/isMainThreadHydrationFinished.js';\nimport { SnapshotInstance } from '../snapshot.js';\nexport function updateGesture(snapshot, expIndex, oldValue, elementIndex, workletType) {\n    if (!snapshot.__elements) {\n        return;\n    }\n    if (__PROFILE__) {\n        console.profile('updateGesture');\n    }\n    const value = snapshot.__values[expIndex];\n    if (workletType === 'main-thread') {\n        processGesture(snapshot.__elements[elementIndex], value, oldValue, !isMainThreadHydrationFinished);\n    }\n    if (__PROFILE__) {\n        console.profileEnd();\n    }\n}\n//# sourceMappingURL=gesture.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { hydrate } from '../hydrate.js';\nimport { componentAtIndexFactory, enqueueComponentFactory, gRecycleMap, gSignMap } from '../list.js';\nexport function snapshotCreateList(pageId, _ctx, _expIndex) {\n    const signMap = new Map();\n    const recycleMap = new Map();\n    const [componentAtIndex, componentAtIndexes] = componentAtIndexFactory([], hydrate);\n    const list = __CreateList(pageId, componentAtIndex, enqueueComponentFactory(), {}, componentAtIndexes);\n    const listID = __GetElementUniqueID(list);\n    gSignMap[listID] = signMap;\n    gRecycleMap[listID] = recycleMap;\n    return list;\n}\nexport function snapshotDestroyList(si) {\n    const [, elementIndex] = si.__snapshot_def.slot[0];\n    const list = si.__elements[elementIndex];\n    const listID = __GetElementUniqueID(list);\n    delete gSignMap[listID];\n    delete gRecycleMap[listID];\n}\n//# sourceMappingURL=list.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { ListUpdateInfoRecording } from '../listUpdateInfo.js';\nimport { __pendingListUpdates } from '../pendingListUpdates.js';\nimport { SnapshotInstance } from '../snapshot.js';\nconst platformInfoVirtualAttributes = /* @__PURE__ */ new Set(['reuse-identifier']);\nconst platformInfoAttributes = /* @__PURE__ */ new Set([\n    'reuse-identifier',\n    'full-span',\n    'item-key',\n    'sticky-top',\n    'sticky-bottom',\n    'estimated-height',\n    'estimated-height-px',\n    'estimated-main-axis-size-px',\n]);\nfunction updateListItemPlatformInfo(ctx, index, oldValue, elementIndex) {\n    const newValue = ctx.__listItemPlatformInfo = ctx.__values[index];\n    const list = ctx.parentNode;\n    if (list?.__snapshot_def.isListHolder) {\n        (__pendingListUpdates.values[list.__id] ??= new ListUpdateInfoRecording(list)).onSetAttribute(ctx, newValue, oldValue);\n    }\n    // In this updater, unlike `updateSpread`, the shape of the value is guaranteed to be an fixed object.\n    // No adding / removing keys.\n    if (ctx.__elements) {\n        const e = ctx.__elements[elementIndex];\n        const value = ctx.__values[index];\n        for (const k in value) {\n            if (platformInfoVirtualAttributes.has(k)) {\n                continue;\n            }\n            __SetAttribute(e, k, value[k]);\n        }\n    }\n}\nexport { updateListItemPlatformInfo, platformInfoAttributes };\n//# sourceMappingURL=platformInfo.js.map", "import { workletUnRef } from './workletRef.js';\nimport { RefProxy } from '../lifecycle/ref/delay.js';\nconst refsToClear = [];\nconst refsToApply = [];\nfunction unref(snapshot, recursive) {\n    snapshot.__worklet_ref_set?.forEach(v => {\n        if (v) {\n            workletUnRef(v);\n        }\n    });\n    snapshot.__worklet_ref_set?.clear();\n    if (recursive) {\n        snapshot.childNodes.forEach(it => {\n            unref(it, recursive);\n        });\n    }\n}\n// This function is modified from preact source code.\nfunction applyRef(ref, value) {\n    const newRef = value && new RefProxy(value);\n    try {\n        if (typeof ref == 'function') {\n            const hasRefUnmount = typeof ref._unmount == 'function';\n            if (hasRefUnmount) {\n                ref._unmount();\n            }\n            if (!hasRefUnmount || newRef != null) {\n                // Store the cleanup function on the function\n                // instance object itself to avoid shape\n                // transitioning vnode\n                ref._unmount = ref(newRef);\n            }\n        }\n        else\n            ref.current = newRef;\n        /* v8 ignore start */\n    }\n    catch (e) {\n        lynx.reportError(e);\n    }\n    /* v8 ignore stop */\n}\nfunction updateRef(snapshot, expIndex, oldValue, elementIndex) {\n    const value = snapshot.__values[expIndex];\n    let ref;\n    if (typeof value === 'string') {\n        ref = value;\n    }\n    else {\n        ref = `react-ref-${snapshot.__id}-${expIndex}`;\n    }\n    snapshot.__values[expIndex] = ref;\n    if (snapshot.__elements && oldValue !== ref) {\n        if (oldValue) {\n            __SetAttribute(snapshot.__elements[elementIndex], oldValue, undefined);\n        }\n        if (ref) {\n            __SetAttribute(snapshot.__elements[elementIndex], ref, 1);\n        }\n    }\n}\nfunction transformRef(ref) {\n    if (ref === undefined || ref === null) {\n        return ref;\n    }\n    if (typeof ref === 'function' || (typeof ref === 'object' && 'current' in ref)) {\n        if ('__ref' in ref) {\n            return ref;\n        }\n        return Object.defineProperty(ref, '__ref', { value: 1 });\n    }\n    throw new Error(`Elements' \"ref\" property should be a function, or an object created `\n        + `by createRef(), but got [${typeof ref}] instead`);\n}\nfunction applyQueuedRefs() {\n    try {\n        for (const ref of refsToClear) {\n            applyRef(ref, null);\n        }\n        for (let i = 0; i < refsToApply.length; i += 2) {\n            const ref = refsToApply[i];\n            const value = refsToApply[i + 1];\n            applyRef(ref, value);\n        }\n    }\n    finally {\n        clearQueuedRefs();\n    }\n}\nfunction queueRefAttrUpdate(oldRef, newRef, snapshotInstanceId, expIndex) {\n    if (oldRef === newRef) {\n        return;\n    }\n    if (oldRef) {\n        refsToClear.push(oldRef);\n    }\n    if (newRef) {\n        refsToApply.push(newRef, [snapshotInstanceId, expIndex]);\n    }\n}\nfunction clearQueuedRefs() {\n    refsToClear.length = 0;\n    refsToApply.length = 0;\n}\n/**\n * @internal\n */\nexport { queueRefAttrUpdate, updateRef, unref, transformRef, applyRef, applyQueuedRefs, clearQueuedRefs };\n//# sourceMappingURL=ref.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { ListUpdateInfoRecording } from '../listUpdateInfo.js';\nimport { __pendingListUpdates } from '../pendingListUpdates.js';\nimport { SnapshotInstance } from '../snapshot.js';\nimport { isDirectOrDeepEqual, isEmptyObject, pick } from '../utils.js';\nimport { updateEvent } from './event.js';\nimport { updateGesture } from './gesture.js';\nimport { platformInfoAttributes, updateListItemPlatformInfo } from './platformInfo.js';\nimport { transformRef, updateRef } from './ref.js';\nimport { updateWorkletEvent } from './workletEvent.js';\nimport { updateWorkletRef } from './workletRef.js';\n// eslint-disable-next-line regexp/no-unused-capturing-group\nconst eventRegExp = /^(([A-Za-z-]*):)?(bind|catch|capture-bind|capture-catch|global-bind)([A-Za-z]+)$/;\nconst eventTypeMap = {\n    bind: 'bindEvent',\n    catch: 'catchEvent',\n    'capture-bind': 'capture-bind',\n    'capture-catch': 'capture-catch',\n    'global-bind': 'global-bindEvent',\n};\nconst noFlattenAttributes = /* @__PURE__ */ new Set([\n    'name',\n    'clip-radius',\n    'overlap',\n    'exposure-scene',\n    'exposure-id',\n]);\nfunction updateSpread(snapshot, index, oldValue, elementIndex) {\n    oldValue ??= {};\n    let newValue = snapshot.__values[index]; // compiler guarantee this must be an object;\n    const list = snapshot.parentNode;\n    if (list?.__snapshot_def.isListHolder) {\n        const oldPlatformInfo = pick(oldValue, platformInfoAttributes);\n        const platformInfo = pick(newValue, platformInfoAttributes);\n        if (!isDirectOrDeepEqual(oldPlatformInfo, platformInfo)) {\n            (__pendingListUpdates.values[list.__id] ??= new ListUpdateInfoRecording(list)).onSetAttribute(snapshot, platformInfo, oldPlatformInfo);\n            snapshot.__listItemPlatformInfo = platformInfo;\n            // The fakeSnapshot is missing `__parent`, so no `ListUpdateInfoRecording#onSetAttribute` will be called\n            const fakeSnapshot = {\n                __values: {\n                    get [index]() {\n                        return platformInfo;\n                    },\n                },\n                __id: snapshot.__id,\n                __elements: snapshot.__elements,\n            };\n            updateListItemPlatformInfo(fakeSnapshot, index, oldPlatformInfo, elementIndex);\n        }\n    }\n    if (!snapshot.__elements) {\n        return;\n    }\n    if ('__spread' in newValue) {\n        // first screen\n        newValue = transformSpread(snapshot, index, newValue);\n        snapshot.__values[index] = newValue;\n    }\n    const dataset = {};\n    let match = null;\n    for (const key in newValue) {\n        const v = newValue[key];\n        if (v !== oldValue[key]) {\n            if (key === 'className') {\n                __SetClasses(snapshot.__elements[elementIndex], v);\n            }\n            else if (key === 'style') {\n                __SetInlineStyles(snapshot.__elements[elementIndex], v);\n            }\n            else if (key === 'id') {\n                __SetID(snapshot.__elements[elementIndex], v);\n            }\n            else if (key.startsWith('data-')) {\n                // collected below\n            }\n            else if (key === 'ref') {\n                const fakeSnapshot = {\n                    __values: {\n                        get [index]() {\n                            return v;\n                        },\n                        set [index](value) {\n                            // Modifications to the ref value should be reflected in the corresponding position of the spread.\n                            newValue[key] = value;\n                        },\n                    },\n                    __id: snapshot.__id,\n                    __elements: snapshot.__elements,\n                };\n                updateRef(fakeSnapshot, index, oldValue[key], elementIndex);\n            }\n            else if (key.endsWith(':ref')) {\n                snapshot.__worklet_ref_set ??= new Set();\n                const fakeSnapshot = {\n                    __values: {\n                        get [index]() {\n                            return v;\n                        },\n                    },\n                    __id: snapshot.__id,\n                    __elements: snapshot.__elements,\n                    __worklet_ref_set: snapshot.__worklet_ref_set,\n                };\n                updateWorkletRef(fakeSnapshot, index, oldValue[key], elementIndex, key.slice(0, -4));\n            }\n            else if (key.endsWith(':gesture')) {\n                const workletType = key.slice(0, -8);\n                const fakeSnapshot = {\n                    __values: {\n                        get [index]() {\n                            return v;\n                        },\n                    },\n                    __id: snapshot.__id,\n                    __elements: snapshot.__elements,\n                };\n                updateGesture(fakeSnapshot, index, oldValue[key], elementIndex, workletType);\n            }\n            else if ((match = eventRegExp.exec(key))) {\n                const workletType = match[2];\n                const eventType = eventTypeMap[match[3]];\n                const eventName = match[4];\n                const fakeSnapshot = {\n                    __values: {\n                        get [index]() {\n                            return v;\n                        },\n                        set [index](value) {\n                            // Modifications to the event value should be reflected in the corresponding position of the spread.\n                            newValue[key] = value;\n                        },\n                    },\n                    __id: snapshot.__id,\n                    __elements: snapshot.__elements,\n                };\n                if (workletType) {\n                    updateWorkletEvent(fakeSnapshot, index, oldValue[key], elementIndex, workletType, eventType, eventName);\n                }\n                else {\n                    updateEvent(fakeSnapshot, index, oldValue[key], elementIndex, eventType, eventName, key);\n                }\n            }\n            else if (platformInfoAttributes.has(key)) {\n                // ignore\n            }\n            else {\n                __SetAttribute(snapshot.__elements[elementIndex], key, v);\n            }\n        }\n        // collect data regardless of whether it has changed\n        if (key.startsWith('data-')) {\n            dataset[key.slice(5)] = v;\n        }\n    }\n    let hasOldDataset = false;\n    for (const key in oldValue) {\n        if (!(key in newValue)) {\n            if (key === 'className') {\n                __SetClasses(snapshot.__elements[elementIndex], '');\n            }\n            else if (key === 'style') {\n                __SetInlineStyles(snapshot.__elements[elementIndex], '');\n            }\n            else if (key === 'id') {\n                __SetID(snapshot.__elements[elementIndex], null);\n            }\n            else if (key.startsWith('data-')) {\n                // collected below\n            }\n            else if (key === 'ref') {\n                const fakeSnapshot = {\n                    __values: {\n                        get [index]() {\n                            return undefined;\n                        },\n                        set [index](value) {\n                            // Modifications to the ref value should be reflected in the corresponding position of the spread.\n                            newValue[key] = value;\n                        },\n                    },\n                    __id: snapshot.__id,\n                    __elements: snapshot.__elements,\n                };\n                updateRef(fakeSnapshot, index, oldValue[key], elementIndex);\n            }\n            else if (key.endsWith(':ref')) {\n                snapshot.__worklet_ref_set ??= new Set();\n                const fakeSnapshot = {\n                    __values: {\n                        get [index]() {\n                            return undefined;\n                        },\n                    },\n                    __id: snapshot.__id,\n                    __elements: snapshot.__elements,\n                    __worklet_ref_set: snapshot.__worklet_ref_set,\n                };\n                updateWorkletRef(fakeSnapshot, index, oldValue[key], elementIndex, key.slice(0, -4));\n            }\n            else if (key.endsWith(':gesture')) {\n                const workletType = key.slice(0, -8);\n                const fakeSnapshot = {\n                    __values: {\n                        get [index]() {\n                            return undefined;\n                        },\n                    },\n                    __id: snapshot.__id,\n                    __elements: snapshot.__elements,\n                };\n                updateGesture(fakeSnapshot, index, oldValue[key], elementIndex, workletType);\n            }\n            else if ((match = eventRegExp.exec(key))) {\n                const workletType = match[2];\n                const eventType = eventTypeMap[match[3]];\n                const eventName = match[4];\n                const fakeSnapshot = {\n                    __values: {\n                        get [index]() {\n                            return undefined;\n                        },\n                        set [index](value) {\n                            newValue[key] = value;\n                        },\n                    },\n                    __id: snapshot.__id,\n                    __elements: snapshot.__elements,\n                };\n                if (workletType) {\n                    updateWorkletEvent(fakeSnapshot, index, oldValue[key], elementIndex, workletType, eventType, eventName);\n                }\n                else {\n                    updateEvent(fakeSnapshot, index, oldValue[key], elementIndex, eventType, eventName, key);\n                }\n            }\n            else if (platformInfoAttributes.has(key)) {\n                // ignore\n            }\n            else {\n                __SetAttribute(snapshot.__elements[elementIndex], key, null);\n            }\n        }\n        // collect data regardless of whether it has changed\n        if (key.startsWith('data-')) {\n            hasOldDataset = true;\n        }\n    }\n    // TODO: compare dataset before commit it to native?\n    if (hasOldDataset || !isEmptyObject(dataset)) {\n        __SetDataset(snapshot.__elements[elementIndex], dataset);\n    }\n}\nfunction transformSpread(snapshot, index, spread) {\n    const result = {};\n    let hasNoFlattenAttributes = false;\n    for (const key in spread) {\n        let value = spread[key];\n        if (key === '__spread') { }\n        else if (key === 'class' || key === 'className') {\n            value ??= '';\n            result['className'] = value;\n        }\n        else if (key === 'ref') {\n            if (__LEPUS__) {\n                result[key] = value ? 1 : undefined;\n            }\n            else {\n                result[key] = transformRef(value)?.__ref;\n            }\n        }\n        else if (typeof value === 'function') {\n            result[key] = `${snapshot.__id}:${index}:${key}`;\n        }\n        else {\n            if (!hasNoFlattenAttributes && noFlattenAttributes.has(key)) {\n                hasNoFlattenAttributes = true;\n            }\n            result[key] = value;\n        }\n    }\n    if (hasNoFlattenAttributes) {\n        result['flatten'] = false;\n    }\n    return result;\n}\nexport { transformSpread, updateSpread };\n//# sourceMappingURL=spread.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { onWorkletCtxUpdate } from '@lynx-js/react/worklet-runtime/bindings';\nimport { isMainThreadHydrationFinished } from '../lifecycle/patch/isMainThreadHydrationFinished.js';\nimport { SnapshotInstance } from '../snapshot.js';\nfunction updateWorkletEvent(snapshot, expIndex, oldValue, elementIndex, workletType, eventType, eventName) {\n    if (!snapshot.__elements) {\n        return;\n    }\n    const value = (snapshot.__values[expIndex] || undefined) ?? {};\n    value._workletType = workletType;\n    if (workletType === 'main-thread') {\n        onWorkletCtxUpdate(value, oldValue, !isMainThreadHydrationFinished, snapshot.__elements[elementIndex]);\n        const event = {\n            type: 'worklet',\n            value,\n        };\n        __AddEvent(snapshot.__elements[elementIndex], eventType, eventName, event);\n    }\n}\nexport { updateWorkletEvent };\n//# sourceMappingURL=workletEvent.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { onWorkletCtxUpdate, runWorkletCtx, updateWorkletRef as update } from '@lynx-js/react/worklet-runtime/bindings';\nimport { isMainThreadHydrationFinished } from '../lifecycle/patch/isMainThreadHydrationFinished.js';\nlet mtRefQueue = [];\nexport function applyRefQueue() {\n    const queue = mtRefQueue;\n    mtRefQueue = [];\n    for (let i = 0; i < queue.length; i += 2) {\n        const worklet = queue[i];\n        const element = queue[i + 1];\n        if ('_wvid' in worklet) {\n            update(worklet, element);\n        }\n        else if ('_wkltId' in worklet) {\n            worklet._unmount = runWorkletCtx(worklet, [{ elementRefptr: element }]);\n        }\n    }\n}\nfunction addToRefQueue(worklet, element) {\n    mtRefQueue.push(worklet, element);\n}\nexport function workletUnRef(value) {\n    if ('_wvid' in value) {\n        update(value, null);\n    }\n    else if ('_wkltId' in value) {\n        if (typeof value._unmount == 'function') {\n            value._unmount();\n        }\n        else {\n            runWorkletCtx(value, [null]);\n        }\n    }\n}\nexport function updateWorkletRef(snapshot, expIndex, oldValue, elementIndex, _workletType) {\n    if (!snapshot.__elements) {\n        return;\n    }\n    if (oldValue && snapshot.__worklet_ref_set?.has(oldValue)) {\n        workletUnRef(oldValue);\n        snapshot.__worklet_ref_set?.delete(oldValue);\n    }\n    const value = snapshot.__values[expIndex];\n    if (value === null || value === undefined) {\n        // do nothing\n    }\n    else if (value._wvid) {\n        const element = snapshot.__elements[elementIndex];\n        addToRefQueue(value, element);\n    }\n    else if (value._wkltId) {\n        const element = snapshot.__elements[elementIndex];\n        onWorkletCtxUpdate(value, oldValue, !isMainThreadHydrationFinished, element);\n        addToRefQueue(value, element);\n        /* v8 ignore next 3 */\n    }\n    else if (value._type === '__LEPUS__' || value._lepusWorkletHash) {\n        // for pre-0.99 compatibility\n        // During the initial render, we will not update the WorkletRef because the background thread is not ready yet.\n    }\n    else {\n        throw new Error('MainThreadRef: main-thread:ref must be of type MainThreadRef or main-thread function.');\n    }\n    if (value) {\n        snapshot.__worklet_ref_set ??= new Set();\n        snapshot.__worklet_ref_set.add(value);\n    }\n    // Add an arbitrary attribute to avoid this element being layout-only\n    __SetAttribute(snapshot.__elements[elementIndex], 'has-react-ref', true);\n}\n//# sourceMappingURL=workletRef.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nexport function isDirectOrDeepEqual(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (typeof a == 'object' && a !== null && typeof b == 'object' && b !== null && JSON.stringify(a) === JSON.stringify(b)) {\n        return true;\n    }\n    return false;\n}\nexport function isEmptyObject(obj) {\n    for (const _ in obj)\n        return false;\n    return true;\n}\nexport function isSdkVersionGt(major, minor) {\n    const lynxSdkVersion = SystemInfo.lynxSdkVersion || '1.0';\n    const version = lynxSdkVersion.split('.');\n    return Number(version[0]) > major || (Number(version[0]) == major && Number(version[1]) > minor);\n}\nexport function pick(obj, keys) {\n    const result = {};\n    for (const key of keys) {\n        if (key in obj) {\n            result[key] = obj[key];\n        }\n    }\n    return result;\n}\nexport function maybePromise(value) {\n    return (typeof value === 'object'\n        && value !== null\n        // @ts-expect-error the check is safe\n        && typeof value.then === 'function');\n}\n//# sourceMappingURL=utils.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { takeWorkletRefInitValuePatch } from './workletRefPool.js';\nexport const destroyTasks = [];\nexport function destroyWorklet() {\n    takeWorkletRefInitValuePatch();\n    for (const task of destroyTasks) {\n        task();\n    }\n    destroyTasks.length = 0;\n}\n//# sourceMappingURL=destroy.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { WorkletEvents } from '@lynx-js/react/worklet-runtime/bindings';\nimport { destroyTasks } from './destroy.js';\nimport { IndexMap } from './indexMap.js';\nlet resolveMap;\nfunction initReturnValueListener() {\n    const context = __JS__ ? lynx.getCoreContext() : lynx.getJSContext();\n    resolveMap = new IndexMap();\n    context.addEventListener(WorkletEvents.FunctionCallRet, onFunctionCallRet);\n    destroyTasks.push(() => {\n        context.removeEventListener(WorkletEvents.FunctionCallRet, onFunctionCallRet);\n        resolveMap = undefined;\n    });\n}\n/**\n * @internal\n */\nfunction onFunctionCall(resolve) {\n    if (!resolveMap) {\n        initReturnValueListener();\n    }\n    return resolveMap.add(resolve);\n}\nfunction onFunctionCallRet(event) {\n    const data = JSON.parse(event.data);\n    const resolve = resolveMap.get(data.resolveId);\n    resolveMap.remove(data.resolveId);\n    resolve(data.returnValue);\n}\nexport { onFunctionCall };\n//# sourceMappingURL=functionCall.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { isSdkVersionGt } from '../utils.js';\nlet mtsEnabled;\nlet runOnBackgroundEnabled;\n/**\n * @internal\n */\nfunction isMtsEnabled() {\n    return mtsEnabled ??= isSdkVersionGt(2, 13);\n}\n/**\n * @internal\n */\nfunction isRunOnBackgroundEnabled() {\n    return runOnBackgroundEnabled ??= isSdkVersionGt(2, 15);\n}\nfunction clearConfigCacheForTesting() {\n    mtsEnabled = undefined;\n    runOnBackgroundEnabled = undefined;\n}\nexport { isMtsEnabled, isRunOnBackgroundEnabled, clearConfigCacheForTesting };\n//# sourceMappingURL=functionality.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n// import { __globalSnapshotPatch } from '../lifecycle/patch/snapshotPatch.js';\n// const workletHashSet: Set<string> = /* @__PURE__ */ new Set();\n/* v8 ignore start */\n/**\n * @internal\n */\n// disable hmr until bugs are fixed\n// TODO: re-enable hmr or change a way to impl it; also need to fix the test case DEV_ONLY_RegisterWorklet\nfunction registerWorkletOnBackground(_type, _hash, _fn) {\n    // if (workletHashSet.has(hash)) {\n    //   return;\n    // }\n    // workletHashSet.add(hash);\n    // if (__globalSnapshotPatch) {\n    //   __globalSnapshotPatch.push(\n    //     SnapshotOperation.DEV_ONLY_RegisterWorklet,\n    //     hash,\n    //     // We use `Function.prototype.toString` to serialize the function for Lepus.\n    //     fn.toString(),\n    //   );\n    // }\n}\n/* v8 ignore stop */\nexport { registerWorkletOnBackground };\n//# sourceMappingURL=hmr.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nclass IndexMap {\n    lastIndex = 0;\n    indexMap = new Map();\n    add(value) {\n        const index = ++this.lastIndex;\n        this.indexMap.set(index, value);\n        return index;\n    }\n    get(index) {\n        return this.indexMap.get(index);\n    }\n    remove(index) {\n        this.indexMap.delete(index);\n    }\n}\nexport { IndexMap };\n//# sourceMappingURL=indexMap.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { WorkletEvents, delayRunOnBackground } from '@lynx-js/react/worklet-runtime/bindings';\nimport { destroyTasks } from './destroy.js';\nimport { WorkletExecIdMap } from './execMap.js';\nimport { isRunOnBackgroundEnabled } from './functionality.js';\nimport { onFunctionCall } from './functionCall.js';\nlet execIdMap;\nfunction init() {\n    'background only';\n    if (execIdMap) {\n        return;\n    }\n    execIdMap = new WorkletExecIdMap();\n    lynx.getCoreContext().addEventListener(WorkletEvents.runOnBackground, runJSFunction);\n    lynx.getCoreContext().addEventListener(WorkletEvents.releaseBackgroundWorkletCtx, releaseBackgroundWorkletCtx);\n    destroyTasks.push(() => {\n        lynx.getCoreContext().removeEventListener(WorkletEvents.runOnBackground, runJSFunction);\n        lynx.getCoreContext().removeEventListener(WorkletEvents.releaseBackgroundWorkletCtx, releaseBackgroundWorkletCtx);\n        execIdMap = undefined;\n    });\n}\n/**\n * @internal\n */\nfunction runJSFunction(event) {\n    'background only';\n    const data = JSON.parse(event.data);\n    const obj = execIdMap.findJsFnHandle(data.obj._execId, data.obj._jsFnId);\n    const f = obj?._fn;\n    if (!f) {\n        throw new Error('runOnBackground: JS function not found: ' + JSON.stringify(data.obj));\n    }\n    const returnValue = f(...data.params);\n    lynx.getCoreContext().dispatchEvent({\n        type: WorkletEvents.FunctionCallRet,\n        data: JSON.stringify({\n            resolveId: data.resolveId,\n            returnValue,\n        }),\n    });\n}\nfunction releaseBackgroundWorkletCtx(event) {\n    'background only';\n    for (const id of event.data) {\n        execIdMap.remove(id);\n    }\n}\n/**\n * @internal\n */\nfunction registerWorkletCtx(ctx) {\n    'background only';\n    init();\n    execIdMap.add(ctx);\n}\n/**\n * `runOnBackground` allows triggering js functions on the background thread asynchronously.\n * @param f - The js function to be called.\n * @returns A function. Calling which with the arguments to be passed to the js function to trigger it on the background thread. This function returns a promise that resolves to the return value of the js function.\n * @example\n * ```ts\n * import { runOnBackground } from '@lynx-js/react';\n *\n * async function someMainthreadFunction() {\n *   'main thread';\n *   const fn = runOnBackground(() => {\n *     return 'hello';\n *   });\n *   const result = await fn();\n}\n * ```\n * @public\n */\nfunction runOnBackground(f) {\n    if (!isRunOnBackgroundEnabled()) {\n        throw new Error('runOnBackground requires Lynx sdk version 2.16.');\n    }\n    if (__JS__) {\n        throw new Error('runOnBackground can only be used on the main thread.');\n    }\n    const obj = f;\n    if (obj._error) {\n        throw new Error(obj._error);\n    }\n    return async (...params) => {\n        return new Promise((resolve) => {\n            const resolveId = onFunctionCall(resolve);\n            if (obj._isFirstScreen) {\n                delayRunOnBackground(obj, (fnId, execId) => {\n                    dispatchRunBackgroundFunctionEvent(fnId, params, execId, resolveId);\n                });\n                return;\n            }\n            dispatchRunBackgroundFunctionEvent(obj._jsFnId, params, obj._execId, resolveId);\n        });\n    };\n}\nfunction dispatchRunBackgroundFunctionEvent(fnId, params, execId, resolveId) {\n    lynx.getJSContext().dispatchEvent({\n        type: WorkletEvents.runOnBackground,\n        data: JSON.stringify({\n            obj: {\n                _jsFnId: fnId,\n                _execId: execId,\n            },\n            params,\n            resolveId,\n        }),\n    });\n}\nexport { registerWorkletCtx, runJSFunction, runOnBackground };\n//# sourceMappingURL=runOnBackground.js.map", "import { WorkletEvents } from '@lynx-js/react/worklet-runtime/bindings';\nimport { onPostWorkletCtx } from './ctx.js';\nimport { isMtsEnabled } from './functionality.js';\nimport { onFunctionCall } from './functionCall.js';\n/**\n * `runOnMainThread` allows triggering main thread functions on the main thread asynchronously.\n * @param fn - The main thread functions to be called.\n * @returns A function. Calling which with the arguments to be passed to the main thread function to trigger it on the main thread. This function returns a promise that resolves to the return value of the main thread function.\n * @example\n * ```ts\n * import { runOnMainThread } from '@lynx-js/react';\n *\n * async function someFunction() {\n *   const fn = runOnMainThread(() => {\n *     'main thread';\n *     return 'hello';\n *   });\n *   const result = await fn();\n * }\n * ```\n * @public\n */\nexport function runOnMainThread(fn) {\n    if (__LEPUS__) {\n        throw new Error('runOnMainThread can only be used on the background thread.');\n    }\n    if (!isMtsEnabled()) {\n        throw new Error('runOnMainThread requires Lynx sdk version 2.14.');\n    }\n    return async (...params) => {\n        return new Promise((resolve) => {\n            onPostWorkletCtx(fn);\n            const resolveId = onFunctionCall(resolve);\n            lynx.getCoreContext().dispatchEvent({\n                type: WorkletEvents.runWorkletCtx,\n                data: JSON.stringify({\n                    worklet: fn,\n                    params,\n                    resolveId,\n                }),\n            });\n        });\n    };\n}\n//# sourceMappingURL=runOnMainThread.js.map", "let lastId = 0;\n/**\n * transform args of `runOnBackground()`.\n *\n * @internal\n */\nexport function transformToWorklet(obj) {\n    const id = ++lastId;\n    if (typeof obj !== 'function') {\n        // We save the error message in the object, so that we can throw it later when the function is called on the main thread.\n        return {\n            _jsFnId: id,\n            _error: `Argument of runOnBackground should be a function, but got [${typeof obj}] instead`,\n        };\n    }\n    return {\n        _jsFnId: id,\n        _fn: obj,\n    };\n}\n//# sourceMappingURL=transformToWorklet.js.map", "import { WorkletEvents } from '@lynx-js/react/worklet-runtime/bindings';\nimport { addWorkletRefInitValue } from './workletRefPool.js';\nimport { useMemo } from '../hooks/react.js';\n// Split into two variables for testing purposes\nlet lastIdBG = 0;\nlet lastIdMT = 0;\nexport function clearWorkletRefLastIdForTesting() {\n    lastIdBG = lastIdMT = 0;\n}\nclass WorkletRef {\n    /**\n     * @internal\n     */\n    _wvid;\n    /**\n     * @internal\n     */\n    _initValue;\n    /**\n     * @internal\n     */\n    _type;\n    /**\n     * @internal\n     */\n    _lifecycleObserver;\n    /**\n     * @internal\n     */\n    constructor(initValue, type) {\n        this._initValue = initValue;\n        this._type = type;\n        if (__JS__) {\n            this._wvid = ++lastIdBG;\n            addWorkletRefInitValue(this._wvid, initValue);\n        }\n        else {\n            this._wvid = --lastIdMT;\n        }\n    }\n    get current() {\n        if (__JS__ && __DEV__) {\n            throw new Error('MainThreadRef: value of a MainThreadRef cannot be accessed in the background thread.');\n        }\n        if (__LEPUS__ && __DEV__) {\n            /* v8 ignore next 3 */\n            throw new Error('MainThreadRef: value of a MainThreadRef cannot be accessed outside of main thread script.');\n        }\n        return undefined;\n    }\n    set current(_) {\n        if (__JS__ && __DEV__) {\n            throw new Error('MainThreadRef: value of a MainThreadRef cannot be accessed in the background thread.');\n        }\n        if (__LEPUS__ && __DEV__) {\n            throw new Error('MainThreadRef: value of a MainThreadRef cannot be accessed outside of main thread script.');\n        }\n    }\n    /**\n     * @internal\n     */\n    toJSON() {\n        return {\n            _wvid: this._wvid,\n        };\n    }\n}\n/**\n * A `MainThreadRef` is a ref that can only be accessed on the main thread. It is used to preserve\n * states between main thread function calls.\n * The data saved in `current` property of the `MainThreadRef` can be read and written in\n * multiple main thread functions.\n * @public\n */\nexport class MainThreadRef extends WorkletRef {\n    constructor(initValue) {\n        super(initValue, 'main-thread');\n        if (__JS__) {\n            const id = this._wvid;\n            this._lifecycleObserver = lynx.getNativeApp().createJSObjectDestructionObserver?.(() => {\n                lynx.getCoreContext?.().dispatchEvent({\n                    type: WorkletEvents.releaseWorkletRef,\n                    data: {\n                        id,\n                    },\n                });\n            });\n        }\n    }\n}\nexport function useMainThreadRef(initValue) {\n    return useMemo(() => {\n        return new MainThreadRef(initValue);\n    }, []);\n}\n//# sourceMappingURL=workletRef.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport { isMtsEnabled } from './functionality.js';\nlet initValuePatch = [];\nconst initValueIdSet = /*#__PURE__*/ new Set();\n/**\n * @internal\n */\nexport function addWorkletRefInitValue(id, value) {\n    if (!isMtsEnabled()) {\n        return;\n    }\n    initValueIdSet.add(id);\n    initValuePatch.push([id, value]);\n}\n/**\n * @internal\n */\nexport function takeWorkletRefInitValuePatch() {\n    const res = initValuePatch;\n    initValuePatch = [];\n    return res;\n}\n//# sourceMappingURL=workletRefPool.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/**\n * Executes the worklet ctx.\n * @param worklet - The Worklet ctx to run.\n * @param params - An array as parameters of the worklet run.\n */\nfunction runWorkletCtx(worklet, params) {\n    return globalThis.runWorklet?.(worklet, params);\n}\n/**\n * Save an element to a `WorkletRef`.\n *\n * @param workletRef - The `WorkletRef` to be updated.\n * @param element - The element.\n * @internal\n */\nfunction updateWorkletRef(workletRef, element) {\n    globalThis.lynxWorkletImpl?._refImpl.updateWorkletRef(workletRef, element);\n}\n/**\n * Update the initial value of the `WorkletRef`.\n *\n * @param patch - An array containing the index and new value of the worklet value.\n */\nfunction updateWorkletRefInitValueChanges(patch) {\n    if (patch) {\n        globalThis.lynxWorkletImpl?._refImpl.updateWorkletRefInitValueChanges(patch);\n    }\n}\n/**\n * Register a worklet.\n *\n * @internal\n */\nfunction registerWorklet(type, id, worklet) {\n    globalThis.registerWorklet(type, id, worklet);\n}\n/**\n * Delay a runOnBackground after hydration.\n *\n * @internal\n */\nfunction delayRunOnBackground(fnObj, fn) {\n    globalThis.lynxWorkletImpl?._runOnBackgroundDelayImpl.delayRunOnBackground(fnObj, fn);\n}\nexport { runWorkletCtx, updateWorkletRef, updateWorkletRefInitValueChanges, registerWorklet, delayRunOnBackground };\n//# sourceMappingURL=bindings.js.map", "var WorkletEvents;\n(function (WorkletEvents) {\n    WorkletEvents[\"runWorkletCtx\"] = \"Lynx.Worklet.runWorkletCtx\";\n    WorkletEvents[\"runOnBackground\"] = \"Lynx.Worklet.runOnBackground\";\n    WorkletEvents[\"FunctionCallRet\"] = \"Lynx.Worklet.FunctionCallRet\";\n    WorkletEvents[\"releaseBackgroundWorkletCtx\"] = \"Lynx.Worklet.releaseBackgroundWorkletCtx\";\n    WorkletEvents[\"releaseWorkletRef\"] = \"Lynx.Worklet.releaseWorkletRef\";\n})(WorkletEvents || (WorkletEvents = {}));\nexport { WorkletEvents };\n//# sourceMappingURL=events.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nexport { loadWorkletRuntime } from './loadRuntime.js';\nexport * from './bindings.js';\nexport * from './observers.js';\nexport { WorkletEvents } from './events.js';\n//# sourceMappingURL=index.js.map", "// Copyright 2024 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\nimport '../global.js';\n/**\n * Loads and initializes the Lepus chunk in the main thread.\n * @param __schema - The dynamic component entry for loading the Lepus chunk.\n * @returns A boolean indicating whether the Lepus chunk was loaded and initialized successfully.\n */\nfunction loadWorkletRuntime(__schema) {\n    if (typeof __LoadLepusChunk === 'undefined') {\n        return false;\n    }\n    if (globalThis.lynxWorkletImpl) {\n        return true;\n    }\n    return __LoadLepusChunk('worklet-runtime', {\n        dynamicComponentEntry: __schema,\n        chunkType: 0,\n    });\n}\nexport { loadWorkletRuntime };\n//# sourceMappingURL=loadRuntime.js.map", "// Copyright 2025 The Lynx Authors. All rights reserved.\n// Licensed under the Apache License Version 2.0 that can be found in the\n// LICENSE file in the root directory of this source tree.\n/**\n * This function must be called when a worklet context is updated.\n *\n * @param worklet - The worklet to be updated\n * @param oldWorklet - The old worklet context\n * @param isFirstScreen - Whether it is before the hydration is finished\n * @param element - The element\n */\nexport function onWorkletCtxUpdate(worklet, oldWorklet, isFirstScreen, element) {\n    globalThis.lynxWorkletImpl?._jsFunctionLifecycleManager?.addRef(worklet._execId, worklet);\n    if (isFirstScreen && oldWorklet) {\n        globalThis.lynxWorkletImpl?._hydrateCtx(worklet, oldWorklet);\n    }\n    // For old version dynamic component compatibility.\n    if (isFirstScreen) {\n        globalThis.lynxWorkletImpl?._eventDelayImpl.runDelayedWorklet(worklet, element);\n    }\n}\n/**\n * This must be called when the hydration is finished.\n */\nexport function onHydrationFinished() {\n    globalThis.lynxWorkletImpl?._runOnBackgroundDelayImpl.runDelayedBackgroundFunctions();\n    globalThis.lynxWorkletImpl?._refImpl.clearFirstScreenWorkletRefMap();\n    // For old version dynamic component compatibility.\n    globalThis.lynxWorkletImpl?._eventDelayImpl.clearDelayedWorklets();\n}\n//# sourceMappingURL=observers.js.map", "export {};\n//# sourceMappingURL=global.js.map", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => createElement(\n    \"svg\",\n    {\n      ref,\n      ...defaultAttributes,\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: mergeClasses(\"lucide\", className),\n      ...!children && !hasA11yProp(rest) && { \"aria-hidden\": \"true\" },\n      ...rest\n    },\n    [\n      ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n      ...Array.isArray(children) ? children : [children]\n    ]\n  )\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className\n      ),\n      ...props\n    })\n  );\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M12 7v14\", key: \"1akyts\" }],\n  [\n    \"path\",\n    {\n      d: \"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\",\n      key: \"ruj8y\"\n    }\n  ]\n];\nconst BookOpen = createLucideIcon(\"book-open\", __iconNode);\n\nexport { __iconNode, BookOpen as default };\n//# sourceMappingURL=book-open.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\", key: \"jecpp\" }],\n  [\"rect\", { width: \"20\", height: \"14\", x: \"2\", y: \"6\", rx: \"2\", key: \"i6l2r4\" }]\n];\nconst Briefcase = createLucideIcon(\"briefcase\", __iconNode);\n\nexport { __iconNode, Briefcase as default };\n//# sourceMappingURL=briefcase.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"line\", { x1: \"12\", x2: \"12\", y1: \"2\", y2: \"22\", key: \"7eqyqh\" }],\n  [\"path\", { d: \"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\", key: \"1b0p4s\" }]\n];\nconst DollarSign = createLucideIcon(\"dollar-sign\", __iconNode);\n\nexport { __iconNode, DollarSign as default };\n//# sourceMappingURL=dollar-sign.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M12 15V3\", key: \"m9g1x1\" }],\n  [\"path\", { d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\", key: \"ih7n3h\" }],\n  [\"path\", { d: \"m7 10 5 5 5-5\", key: \"brsn70\" }]\n];\nconst Download = createLucideIcon(\"download\", __iconNode);\n\nexport { __iconNode, Download as default };\n//# sourceMappingURL=download.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\",\n      key: \"c3ymky\"\n    }\n  ]\n];\nconst Heart = createLucideIcon(\"heart\", __iconNode);\n\nexport { __iconNode, Heart as default };\n//# sourceMappingURL=heart.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\", key: \"5wwlr5\" }],\n  [\n    \"path\",\n    {\n      d: \"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\",\n      key: \"1d0kgt\"\n    }\n  ]\n];\nconst House = createLucideIcon(\"house\", __iconNode);\n\nexport { __iconNode, House as default };\n//# sourceMappingURL=house.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M5 12h14\", key: \"1ays0h\" }],\n  [\"path\", { d: \"M12 5v14\", key: \"s699le\" }]\n];\nconst Plus = createLucideIcon(\"plus\", __iconNode);\n\nexport { __iconNode, Plus as default };\n//# sourceMappingURL=plus.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"m17 2 4 4-4 4\", key: \"nntrym\" }],\n  [\"path\", { d: \"M3 11v-1a4 4 0 0 1 4-4h14\", key: \"84bu3i\" }],\n  [\"path\", { d: \"m7 22-4-4 4-4\", key: \"1wqhfi\" }],\n  [\"path\", { d: \"M21 13v1a4 4 0 0 1-4 4H3\", key: \"1rx37r\" }]\n];\nconst Repeat = createLucideIcon(\"repeat\", __iconNode);\n\nexport { __iconNode, Repeat as default };\n//# sourceMappingURL=repeat.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"8\", cy: \"21\", r: \"1\", key: \"jimo8o\" }],\n  [\"circle\", { cx: \"19\", cy: \"21\", r: \"1\", key: \"13723u\" }],\n  [\n    \"path\",\n    {\n      d: \"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12\",\n      key: \"9zh506\"\n    }\n  ]\n];\nconst ShoppingCart = createLucideIcon(\"shopping-cart\", __iconNode);\n\nexport { __iconNode, ShoppingCart as default };\n//# sourceMappingURL=shopping-cart.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    { d: \"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344\", key: \"2acyp4\" }\n  ],\n  [\"path\", { d: \"m9 11 3 3L22 4\", key: \"1pflzl\" }]\n];\nconst SquareCheckBig = createLucideIcon(\"square-check-big\", __iconNode);\n\nexport { __iconNode, SquareCheckBig as default };\n//# sourceMappingURL=square-check-big.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"rect\", { width: \"18\", height: \"18\", x: \"3\", y: \"3\", rx: \"2\", key: \"afitv7\" }]\n];\nconst Square = createLucideIcon(\"square\", __iconNode);\n\nexport { __iconNode, Square as default };\n//# sourceMappingURL=square.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M3 6h18\", key: \"d0wm0j\" }],\n  [\"path\", { d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\", key: \"4alrt4\" }],\n  [\"path\", { d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\", key: \"v07s0e\" }],\n  [\"line\", { x1: \"10\", x2: \"10\", y1: \"11\", y2: \"17\", key: \"1uufr5\" }],\n  [\"line\", { x1: \"14\", x2: \"14\", y1: \"11\", y2: \"17\", key: \"xtxkd\" }]\n];\nconst Trash2 = createLucideIcon(\"trash-2\", __iconNode);\n\nexport { __iconNode, Trash2 as default };\n//# sourceMappingURL=trash-2.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M12 3v12\", key: \"1x0j5s\" }],\n  [\"path\", { d: \"m17 8-5-5-5 5\", key: \"7q97r8\" }],\n  [\"path\", { d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\", key: \"ih7n3h\" }]\n];\nconst Upload = createLucideIcon(\"upload\", __iconNode);\n\nexport { __iconNode, Upload as default };\n//# sourceMappingURL=upload.js.map\n", "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string) => string.replace(\n  /^([A-Z])|[\\s-_]+(\\w)/g,\n  (match, p1, p2) => p2 ? p2.toUpperCase() : p1.toLowerCase()\n);\nconst toPascalCase = (string) => {\n  const camelCase = toCamelCase(string);\n  return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\nconst hasA11yProp = (props) => {\n  for (const prop in props) {\n    if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n      return true;\n    }\n  }\n};\n\nexport { hasA11yProp, mergeClasses, toCamelCase, toKebabCase, toPascalCase };\n//# sourceMappingURL=utils.js.map\n", "import { Component, createElement, options, toChildArray, Fragment, render as render$1, hydrate as hydrate$1, createContext, createRef, cloneElement as cloneElement$1 } from 'preact';\nexport { Component, Fragment, createContext, createElement, createRef } from 'preact';\nimport { useCallback, useContext, useDebugValue, useEffect, useId, useImperativeHandle, useLayoutEffect, useMemo, useReducer, useRef, useState } from 'preact/hooks';\nexport * from 'preact/hooks';\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nfunction assign(obj, props) {\n  for (var i in props) obj[i] = props[i];\n  return /** @type {O & P} */obj;\n}\n\n/**\n * Check if two objects have a different shape\n * @param {object} a\n * @param {object} b\n * @returns {boolean}\n */\nfunction shallowDiffers(a, b) {\n  for (var i in a) if (i !== '__source' && !(i in b)) return true;\n  for (var _i in b) if (_i !== '__source' && a[_i] !== b[_i]) return true;\n  return false;\n}\n\n/**\n * Check if two values are the same value\n * @param {*} x\n * @param {*} y\n * @returns {boolean}\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y;\n}\n\n/**\n * Component class with a predefined `shouldComponentUpdate` implementation\n */\nfunction PureComponent(p, c) {\n  this.props = p;\n  this.context = c;\n}\nPureComponent.prototype = new Component();\n// Some third-party libraries check if this property is present\nPureComponent.prototype.isPureReactComponent = true;\nPureComponent.prototype.shouldComponentUpdate = function (props, state) {\n  return shallowDiffers(this.props, props) || shallowDiffers(this.state, state);\n};\n\n/**\n * Memoize a component, so that it only updates when the props actually have\n * changed. This was previously known as `React.pure`.\n * @param {import('./internal').FunctionComponent} c functional component\n * @param {(prev: object, next: object) => boolean} [comparer] Custom equality function\n * @returns {import('./internal').FunctionComponent}\n */\nfunction memo(c, comparer) {\n  function shouldUpdate(nextProps) {\n    var ref = this.props.ref;\n    var updateRef = ref == nextProps.ref;\n    if (!updateRef && ref) {\n      ref.call ? ref(null) : ref.current = null;\n    }\n    if (!comparer) {\n      return shallowDiffers(this.props, nextProps);\n    }\n    return !comparer(this.props, nextProps) || !updateRef;\n  }\n  function Memoed(props) {\n    this.shouldComponentUpdate = shouldUpdate;\n    return createElement(c, props);\n  }\n  Memoed.displayName = 'Memo(' + (c.displayName || c.name) + ')';\n  Memoed.prototype.isReactComponent = true;\n  Memoed.__f = true;\n  return Memoed;\n}\n\nvar oldDiffHook = options.__b;\noptions.__b = function (vnode) {\n  if (vnode.type && vnode.type.__f && vnode.ref) {\n    vnode.props.ref = vnode.ref;\n    vnode.ref = null;\n  }\n  if (oldDiffHook) oldDiffHook(vnode);\n};\nvar REACT_FORWARD_SYMBOL = typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.forward_ref') || 0xf47;\n\n/**\n * Pass ref down to a child. This is mainly used in libraries with HOCs that\n * wrap components. Using `forwardRef` there is an easy way to get a reference\n * of the wrapped component instead of one of the wrapper itself.\n * @param {import('./index').ForwardFn} fn\n * @returns {import('./internal').FunctionComponent}\n */\nfunction forwardRef(fn) {\n  function Forwarded(props) {\n    var clone = assign({}, props);\n    delete clone.ref;\n    return fn(clone, props.ref || null);\n  }\n\n  // mobx-react checks for this being present\n  Forwarded.$$typeof = REACT_FORWARD_SYMBOL;\n  // mobx-react heavily relies on implementation details.\n  // It expects an object here with a `render` property,\n  // and prototype.render will fail. Without this\n  // mobx-react throws.\n  Forwarded.render = Forwarded;\n  Forwarded.prototype.isReactComponent = Forwarded.__f = true;\n  Forwarded.displayName = 'ForwardRef(' + (fn.displayName || fn.name) + ')';\n  return Forwarded;\n}\n\nvar mapFn = function mapFn(children, fn) {\n  if (children == null) return null;\n  return toChildArray(toChildArray(children).map(fn));\n};\n\n// This API is completely unnecessary for Preact, so it's basically passthrough.\nvar Children = {\n  map: mapFn,\n  forEach: mapFn,\n  count: function count(children) {\n    return children ? toChildArray(children).length : 0;\n  },\n  only: function only(children) {\n    var normalized = toChildArray(children);\n    if (normalized.length !== 1) throw 'Children.only';\n    return normalized[0];\n  },\n  toArray: toChildArray\n};\n\n/** Normal hydration that attaches to a DOM tree but does not diff it. */\nvar MODE_HYDRATE = 1 << 5;\n\nvar oldCatchError = options.__e;\noptions.__e = function (error, newVNode, oldVNode, errorInfo) {\n  if (error.then) {\n    /** @type {import('./internal').Component} */\n    var component;\n    var vnode = newVNode;\n    for (; vnode = vnode.__;) {\n      if ((component = vnode.__c) && component.__c) {\n        if (newVNode.__e == null) {\n          newVNode.__e = oldVNode.__e;\n          newVNode.__k = oldVNode.__k;\n        }\n        // Don't call oldCatchError if we found a Suspense\n        return component.__c(error, newVNode);\n      }\n    }\n  }\n  oldCatchError(error, newVNode, oldVNode, errorInfo);\n};\nvar oldUnmount = options.unmount;\noptions.unmount = function (vnode) {\n  /** @type {import('./internal').Component} */\n  var component = vnode.__c;\n  if (component && component.__R) {\n    component.__R();\n  }\n\n  // if the component is still hydrating\n  // most likely it is because the component is suspended\n  // we set the vnode.type as `null` so that it is not a typeof function\n  // so the unmount will remove the vnode._dom\n  if (component && vnode.__u & MODE_HYDRATE) {\n    vnode.type = null;\n  }\n  if (oldUnmount) oldUnmount(vnode);\n};\nfunction detachedClone(vnode, detachedParent, parentDom) {\n  if (vnode) {\n    if (vnode.__c && vnode.__c.__H) {\n      vnode.__c.__H.__.forEach(function (effect) {\n        if (typeof effect.__c == 'function') effect.__c();\n      });\n      vnode.__c.__H = null;\n    }\n    vnode = assign({}, vnode);\n    if (vnode.__c != null) {\n      if (vnode.__c.__P === parentDom) {\n        vnode.__c.__P = detachedParent;\n      }\n      vnode.__c = null;\n    }\n    vnode.__k = vnode.__k && vnode.__k.map(function (child) {\n      return detachedClone(child, detachedParent, parentDom);\n    });\n  }\n  return vnode;\n}\nfunction removeOriginal(vnode, detachedParent, originalParent) {\n  if (vnode && originalParent) {\n    vnode.__v = null;\n    vnode.__k = vnode.__k && vnode.__k.map(function (child) {\n      return removeOriginal(child, detachedParent, originalParent);\n    });\n    if (vnode.__c) {\n      if (vnode.__c.__P === detachedParent) {\n        if (vnode.__e) {\n          originalParent.appendChild(vnode.__e);\n        }\n        vnode.__c.__e = true;\n        vnode.__c.__P = originalParent;\n      }\n    }\n  }\n  return vnode;\n}\n\n// having custom inheritance instead of a class here saves a lot of bytes\nfunction Suspense() {\n  // we do not call super here to golf some bytes...\n  this.__u = 0;\n  this._suspenders = null;\n  this.__b = null;\n}\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspense.prototype = new Component();\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {Promise} promise The thrown promise\n * @param {import('./internal').VNode<any, any>} suspendingVNode The suspending component\n */\nSuspense.prototype.__c = function (promise, suspendingVNode) {\n  var suspendingComponent = suspendingVNode.__c;\n\n  /** @type {import('./internal').SuspenseComponent} */\n  var c = this;\n  if (c._suspenders == null) {\n    c._suspenders = [];\n  }\n  c._suspenders.push(suspendingComponent);\n  var resolve = suspended(c.__v);\n  var resolved = false;\n  var onResolved = function onResolved() {\n    if (resolved) return;\n    resolved = true;\n    suspendingComponent.__R = null;\n    if (resolve) {\n      resolve(onSuspensionComplete);\n    } else {\n      onSuspensionComplete();\n    }\n  };\n  suspendingComponent.__R = onResolved;\n  var onSuspensionComplete = function onSuspensionComplete() {\n    if (! --c.__u) {\n      // If the suspension was during hydration we don't need to restore the\n      // suspended children into the _children array\n      if (c.state.__a) {\n        var suspendedVNode = c.state.__a;\n        c.__v.__k[0] = removeOriginal(suspendedVNode, suspendedVNode.__c.__P, suspendedVNode.__c.__O);\n      }\n      c.setState({\n        __a: c.__b = null\n      });\n      var _suspended;\n      while (_suspended = c._suspenders.pop()) {\n        _suspended.forceUpdate();\n      }\n    }\n  };\n\n  /**\n   * We do not set `suspended: true` during hydration because we want the actual markup\n   * to remain on screen and hydrate it when the suspense actually gets resolved.\n   * While in non-hydration cases the usual fallback -> component flow would occour.\n   */\n  if (!c.__u++ && !(suspendingVNode.__u & MODE_HYDRATE)) {\n    c.setState({\n      __a: c.__b = c.__v.__k[0]\n    });\n  }\n  promise.then(onResolved, onResolved);\n};\nSuspense.prototype.componentWillUnmount = function () {\n  this._suspenders = [];\n};\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {import('./internal').SuspenseComponent[\"props\"]} props\n * @param {import('./internal').SuspenseState} state\n */\nSuspense.prototype.render = function (props, state) {\n  if (this.__b) {\n    // When the Suspense's _vnode was created by a call to createVNode\n    // (i.e. due to a setState further up in the tree)\n    // it's _children prop is null, in this case we \"forget\" about the parked vnodes to detach\n    if (this.__v.__k) {\n      var detachedParent = options.document.createElement('div');\n      var detachedComponent = this.__v.__k[0].__c;\n      this.__v.__k[0] = detachedClone(this.__b, detachedParent, detachedComponent.__O = detachedComponent.__P);\n    }\n    this.__b = null;\n  }\n\n  // Wrap fallback tree in a VNode that prevents itself from being marked as aborting mid-hydration:\n  /** @type {import('./internal').VNode} */\n  var fallback = state.__a && createElement(Fragment, null, props.fallback);\n  if (fallback) fallback.__u &= ~MODE_HYDRATE;\n  return [createElement(Fragment, null, state.__a ? null : props.children), fallback];\n};\n\n/**\n * Checks and calls the parent component's _suspended method, passing in the\n * suspended vnode. This is a way for a parent (e.g. SuspenseList) to get notified\n * that one of its children/descendants suspended.\n *\n * The parent MAY return a callback. The callback will get called when the\n * suspension resolves, notifying the parent of the fact.\n * Moreover, the callback gets function `unsuspend` as a parameter. The resolved\n * child descendant will not actually get unsuspended until `unsuspend` gets called.\n * This is a way for the parent to delay unsuspending.\n *\n * If the parent does not return a callback then the resolved vnode\n * gets unsuspended immediately when it resolves.\n *\n * @param {import('./internal').VNode} vnode\n * @returns {((unsuspend: () => void) => void)?}\n */\nfunction suspended(vnode) {\n  /** @type {import('./internal').Component} */\n  var component = vnode.__.__c;\n  return component && component.__a && component.__a(vnode);\n}\nfunction lazy(loader) {\n  var prom;\n  var component;\n  var error;\n  function Lazy(props) {\n    if (!prom) {\n      prom = loader();\n      prom.then(function (exports) {\n        component = exports.default || exports;\n      }, function (e) {\n        error = e;\n      });\n    }\n    if (error) {\n      throw error;\n    }\n    if (!component) {\n      throw prom;\n    }\n    return createElement(component, props);\n  }\n  Lazy.displayName = 'Lazy';\n  Lazy.__f = true;\n  return Lazy;\n}\n\n// Indexes to linked list nodes (nodes are stored as arrays to save bytes).\nvar SUSPENDED_COUNT = 0;\nvar RESOLVED_COUNT = 1;\nvar NEXT_NODE = 2;\n\n// Having custom inheritance instead of a class here saves a lot of bytes.\nfunction SuspenseList() {\n  this._next = null;\n  this._map = null;\n}\n\n// Mark one of child's earlier suspensions as resolved.\n// Some pending callbacks may become callable due to this\n// (e.g. the last suspended descendant gets resolved when\n// revealOrder === 'together'). Process those callbacks as well.\nvar resolve = function resolve(list, child, node) {\n  if (++node[RESOLVED_COUNT] === node[SUSPENDED_COUNT]) {\n    // The number a child (or any of its descendants) has been suspended\n    // matches the number of times it's been resolved. Therefore we\n    // mark the child as completely resolved by deleting it from ._map.\n    // This is used to figure out when *all* children have been completely\n    // resolved when revealOrder is 'together'.\n    list._map.delete(child);\n  }\n\n  // If revealOrder is falsy then we can do an early exit, as the\n  // callbacks won't get queued in the node anyway.\n  // If revealOrder is 'together' then also do an early exit\n  // if all suspended descendants have not yet been resolved.\n  if (!list.props.revealOrder || list.props.revealOrder[0] === 't' && list._map.size) {\n    return;\n  }\n\n  // Walk the currently suspended children in order, calling their\n  // stored callbacks on the way. Stop if we encounter a child that\n  // has not been completely resolved yet.\n  node = list._next;\n  while (node) {\n    while (node.length > 3) {\n      node.pop()();\n    }\n    if (node[RESOLVED_COUNT] < node[SUSPENDED_COUNT]) {\n      break;\n    }\n    list._next = node = node[NEXT_NODE];\n  }\n};\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspenseList.prototype = new Component();\nSuspenseList.prototype.__a = function (child) {\n  var list = this;\n  var delegated = suspended(list.__v);\n  var node = list._map.get(child);\n  node[SUSPENDED_COUNT]++;\n  return function (unsuspend) {\n    var wrappedUnsuspend = function wrappedUnsuspend() {\n      if (!list.props.revealOrder) {\n        // Special case the undefined (falsy) revealOrder, as there\n        // is no need to coordinate a specific order or unsuspends.\n        unsuspend();\n      } else {\n        node.push(unsuspend);\n        resolve(list, child, node);\n      }\n    };\n    if (delegated) {\n      delegated(wrappedUnsuspend);\n    } else {\n      wrappedUnsuspend();\n    }\n  };\n};\nSuspenseList.prototype.render = function (props) {\n  this._next = null;\n  this._map = new Map();\n  var children = toChildArray(props.children);\n  if (props.revealOrder && props.revealOrder[0] === 'b') {\n    // If order === 'backwards' (or, well, anything starting with a 'b')\n    // then flip the child list around so that the last child will be\n    // the first in the linked list.\n    children.reverse();\n  }\n  // Build the linked list. Iterate through the children in reverse order\n  // so that `_next` points to the first linked list node to be resolved.\n  for (var i = children.length; i--;) {\n    // Create a new linked list node as an array of form:\n    // \t[suspended_count, resolved_count, next_node]\n    // where suspended_count and resolved_count are numeric counters for\n    // keeping track how many times a node has been suspended and resolved.\n    //\n    // Note that suspended_count starts from 1 instead of 0, so we can block\n    // processing callbacks until componentDidMount has been called. In a sense\n    // node is suspended at least until componentDidMount gets called!\n    //\n    // Pending callbacks are added to the end of the node:\n    // \t[suspended_count, resolved_count, next_node, callback_0, callback_1, ...]\n    this._map.set(children[i], this._next = [1, 0, this._next]);\n  }\n  return props.children;\n};\nSuspenseList.prototype.componentDidUpdate = SuspenseList.prototype.componentDidMount = function () {\n  var _this = this;\n  // Iterate through all children after mounting for two reasons:\n  // 1. As each node[SUSPENDED_COUNT] starts from 1, this iteration increases\n  //    each node[RELEASED_COUNT] by 1, therefore balancing the counters.\n  //    The nodes can now be completely consumed from the linked list.\n  // 2. Handle nodes that might have gotten resolved between render and\n  //    componentDidMount.\n  this._map.forEach(function (node, child) {\n    resolve(_this, child, node);\n  });\n};\n\n/**\n * @param {import('../../src/index').RenderableProps<{ context: any }>} props\n */\nfunction ContextProvider(props) {\n  this.getChildContext = function () {\n    return props.context;\n  };\n  return props.children;\n}\n\n/**\n * Portal component\n * @this {import('./internal').Component}\n * @param {object | null | undefined} props\n *\n * TODO: use createRoot() instead of fake root\n */\nfunction Portal(props) {\n  var _this = this;\n  var container = props._container;\n  _this.componentWillUnmount = function () {\n    render$1(null, _this._temp);\n    _this._temp = null;\n    _this._container = null;\n  };\n\n  // When we change container we should clear our old container and\n  // indicate a new mount.\n  if (_this._container && _this._container !== container) {\n    _this.componentWillUnmount();\n  }\n  if (!_this._temp) {\n    _this._container = container;\n\n    // Create a fake DOM parent node that manages a subset of `container`'s children:\n    _this._temp = {\n      nodeType: 1,\n      parentNode: container,\n      childNodes: [],\n      contains: function contains() {\n        return true;\n      },\n      appendChild: function appendChild(child) {\n        this.childNodes.push(child);\n        _this._container.appendChild(child);\n      },\n      insertBefore: function insertBefore(child, before) {\n        this.childNodes.push(child);\n        _this._container.appendChild(child);\n      },\n      removeChild: function removeChild(child) {\n        this.childNodes.splice(this.childNodes.indexOf(child) >>> 1, 1);\n        _this._container.removeChild(child);\n      }\n    };\n  }\n\n  // Render our wrapping element into temp.\n  render$1(createElement(ContextProvider, {\n    context: _this.context\n  }, props.__v), _this._temp);\n}\n\n/**\n * Create a `Portal` to continue rendering the vnode tree at a different DOM node\n * @param {import('./internal').VNode} vnode The vnode to render\n * @param {import('./internal').PreactElement} container The DOM node to continue rendering in to.\n */\nfunction createPortal(vnode, container) {\n  var el = createElement(Portal, {\n    __v: vnode,\n    _container: container\n  });\n  el.containerInfo = container;\n  return el;\n}\n\nvar REACT_ELEMENT_TYPE = typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.element') || 0xeac7;\n\n// Some libraries like `react-virtualized` explicitly check for this.\nComponent.prototype.isReactComponent = {};\nvar oldVNodeHook = options.vnode;\noptions.vnode = function (vnode) {\n  vnode.$$typeof = REACT_ELEMENT_TYPE;\n  if (oldVNodeHook) oldVNodeHook(vnode);\n};\n\n// `UNSAFE_*` lifecycle hooks\n// Preact only ever invokes the unprefixed methods.\n// Here we provide a base \"fallback\" implementation that calls any defined UNSAFE_ prefixed method.\n// - If a component defines its own `componentDidMount()` (including via defineProperty), use that.\n// - If a component defines `UNSAFE_componentDidMount()`, `componentDidMount` is the alias getter/setter.\n// - If anything assigns to an `UNSAFE_*` property, the assignment is forwarded to the unprefixed property.\n// See https://github.com/preactjs/preact/issues/1941\n['componentWillMount', 'componentWillReceiveProps', 'componentWillUpdate'].forEach(function (key) {\n  Object.defineProperty(Component.prototype, key, {\n    configurable: true,\n    get: function get() {\n      return this['UNSAFE_' + key];\n    },\n    set: function set(v) {\n      Object.defineProperty(this, key, {\n        configurable: true,\n        writable: true,\n        value: v\n      });\n    }\n  });\n});\n\n/**\n * Proxy render() since React returns a Component reference.\n * @param {import('./internal').VNode} vnode VNode tree to render\n * @param {import('./internal').PreactElement} parent DOM node to render vnode tree into\n * @param {() => void} [callback] Optional callback that will be called after rendering\n * @returns {import('./internal').Component | null} The root component reference or null\n */\nfunction render(vnode, parent, callback) {\n  // React destroys any existing DOM nodes, see #1727\n  // ...but only on the first render, see #1828\n  if (parent.__k == null) {\n    parent.textContent = '';\n  }\n  render$1(vnode, parent);\n  if (typeof callback == 'function') callback();\n  return vnode ? vnode.__c : null;\n}\nfunction hydrate(vnode, parent, callback) {\n  hydrate$1(vnode, parent);\n  if (typeof callback == 'function') callback();\n  return vnode ? vnode.__c : null;\n}\nvar oldEventHook = options.event;\noptions.event = function (e) {\n  if (oldEventHook) e = oldEventHook(e);\n  e.persist = empty;\n  e.isPropagationStopped = isPropagationStopped;\n  e.isDefaultPrevented = isDefaultPrevented;\n  return e.nativeEvent = e;\n};\nfunction empty() {}\nfunction isPropagationStopped() {\n  return this.cancelBubble;\n}\nfunction isDefaultPrevented() {\n  return this.defaultPrevented;\n}\n\n// This is a very very private internal function for React it\n// is used to sort-of do runtime dependency injection.\nvar __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {\n  ReactCurrentDispatcher: {\n    current: {\n      useCallback: useCallback,\n      useContext: useContext,\n      useDebugValue: useDebugValue,\n      useDeferredValue: useDeferredValue,\n      useEffect: useEffect,\n      useId: useId,\n      useImperativeHandle: useImperativeHandle,\n      useInsertionEffect: useInsertionEffect,\n      useLayoutEffect: useLayoutEffect,\n      useMemo: useMemo,\n      // useMutableSource, // experimental-only and replaced by uSES, likely not worth supporting\n      useReducer: useReducer,\n      useRef: useRef,\n      useState: useState,\n      useSyncExternalStore: useSyncExternalStore,\n      useTransition: useTransition\n    }\n  }\n};\n\nvar version = '18.3.1'; // trick libraries to think we are react\n\n/**\n * Legacy version of createElement.\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor\n */\nfunction createFactory(type) {\n  return createElement.bind(null, type);\n}\n\n/**\n * Check if the passed element is a valid (p)react node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isValidElement(element) {\n  return !!element && element.$$typeof === REACT_ELEMENT_TYPE;\n}\n\n/**\n * Check if the passed element is a Fragment node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isFragment(element) {\n  return isValidElement(element) && element.type === Fragment;\n}\n\n/**\n * Check if the passed element is a Memo node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isMemo(element) {\n  return !!element && !!element.displayName && (typeof element.displayName === 'string' || element.displayName instanceof String) && element.displayName.startsWith('Memo(');\n}\n\n/**\n * Wrap `cloneElement` to abort if the passed element is not a valid element and apply\n * all vnode normalizations.\n * @param {import('./internal').VNode} element The vnode to clone\n * @param {object} props Props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Optional component children\n */\nfunction cloneElement(element) {\n  if (!isValidElement(element)) return element;\n  return cloneElement$1.apply(null, arguments);\n}\n\n/**\n * Remove a component tree from the DOM, including state and event handlers.\n * @param {import('./internal').PreactElement} container\n * @returns {boolean}\n */\nfunction unmountComponentAtNode(container) {\n  if (container.__k) {\n    render$1(null, container);\n    return true;\n  }\n  return false;\n}\n\n/**\n * Get the matching DOM node for a component\n * @param {import('./internal').Component} component\n * @returns {import('./internal').PreactElement | null}\n */\nfunction findDOMNode(component) {\n  return component && (component.base || component.nodeType === 1 && component) || null;\n}\n\n/**\n * Deprecated way to control batched rendering inside the reconciler, but we\n * already schedule in batches inside our rendering code\n * @template Arg\n * @param {(arg: Arg) => void} callback function that triggers the updated\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n */\n// eslint-disable-next-line camelcase\nvar unstable_batchedUpdates = function unstable_batchedUpdates(callback, arg) {\n  return callback(arg);\n};\n\n/**\n * In React, `flushSync` flushes the entire tree and forces a rerender. It's\n * implmented here as a no-op.\n * @template Arg\n * @template Result\n * @param {(arg: Arg) => Result} callback function that runs before the flush\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n * @returns\n */\nvar flushSync = function flushSync(callback, arg) {\n  return callback(arg);\n};\n\n/**\n * Strict Mode is not implemented in Preact, so we provide a stand-in for it\n * that just renders its children without imposing any restrictions.\n */\nvar StrictMode = Fragment;\nfunction startTransition(cb) {\n  cb();\n}\nfunction useDeferredValue(val) {\n  return val;\n}\nfunction useTransition() {\n  return [false, startTransition];\n}\n\n// TODO: in theory this should be done after a VNode is diffed as we want to insert\n// styles/... before it attaches\nvar useInsertionEffect = useLayoutEffect;\n\n// compat to react-is\nvar isElement = isValidElement;\n\n/**\n * This is taken from https://github.com/facebook/react/blob/main/packages/use-sync-external-store/src/useSyncExternalStoreShimClient.js#L84\n * on a high level this cuts out the warnings, ... and attempts a smaller implementation\n * @typedef {{ _value: any; _getSnapshot: () => any }} Store\n */\nfunction useSyncExternalStore(subscribe, getSnapshot) {\n  var value = getSnapshot();\n\n  /**\n   * @typedef {{ _instance: Store }} StoreRef\n   * @type {[StoreRef, (store: StoreRef) => void]}\n   */\n  var _useState = useState({\n      _instance: {\n        __: value,\n        _getSnapshot: getSnapshot\n      }\n    }),\n    _instance = _useState[0]._instance,\n    forceUpdate = _useState[1];\n  useLayoutEffect(function () {\n    _instance.__ = value;\n    _instance._getSnapshot = getSnapshot;\n    if (didSnapshotChange(_instance)) {\n      forceUpdate({\n        _instance: _instance\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    if (didSnapshotChange(_instance)) {\n      forceUpdate({\n        _instance: _instance\n      });\n    }\n    return subscribe(function () {\n      if (didSnapshotChange(_instance)) {\n        forceUpdate({\n          _instance: _instance\n        });\n      }\n    });\n  }, [subscribe]);\n  return value;\n}\n\n/** @type {(inst: Store) => boolean} */\nfunction didSnapshotChange(inst) {\n  var latestGetSnapshot = inst._getSnapshot;\n  var prevValue = inst.__;\n  try {\n    var nextValue = latestGetSnapshot();\n    return !is(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\n// React copies the named exports to the default one.\nvar index = {\n  useState: useState,\n  useId: useId,\n  useReducer: useReducer,\n  useEffect: useEffect,\n  useLayoutEffect: useLayoutEffect,\n  useInsertionEffect: useInsertionEffect,\n  useTransition: useTransition,\n  useDeferredValue: useDeferredValue,\n  useSyncExternalStore: useSyncExternalStore,\n  startTransition: startTransition,\n  useRef: useRef,\n  useImperativeHandle: useImperativeHandle,\n  useMemo: useMemo,\n  useCallback: useCallback,\n  useContext: useContext,\n  useDebugValue: useDebugValue,\n  version: version,\n  Children: Children,\n  render: render,\n  hydrate: hydrate,\n  unmountComponentAtNode: unmountComponentAtNode,\n  createPortal: createPortal,\n  createElement: createElement,\n  createContext: createContext,\n  createFactory: createFactory,\n  cloneElement: cloneElement,\n  createRef: createRef,\n  Fragment: Fragment,\n  isValidElement: isValidElement,\n  isElement: isElement,\n  isFragment: isFragment,\n  isMemo: isMemo,\n  findDOMNode: findDOMNode,\n  Component: Component,\n  PureComponent: PureComponent,\n  memo: memo,\n  forwardRef: forwardRef,\n  flushSync: flushSync,\n  unstable_batchedUpdates: unstable_batchedUpdates,\n  StrictMode: StrictMode,\n  Suspense: Suspense,\n  SuspenseList: SuspenseList,\n  lazy: lazy,\n  __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n};\n\nexport { Children, PureComponent, StrictMode, Suspense, SuspenseList, __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED, cloneElement, createFactory, createPortal, index as default, findDOMNode, flushSync, forwardRef, hydrate, isElement, isFragment, isMemo, isValidElement, lazy, memo, render, startTransition, unmountComponentAtNode, unstable_batchedUpdates, useDeferredValue, useInsertionEffect, useSyncExternalStore, useTransition, version };\n//# sourceMappingURL=compat.module.js.map\n", "/** Normal hydration that attaches to a DOM tree but does not diff it. */\nvar MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nvar MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nvar INSERT_VNODE = 1 << 16;\n/** Indicates a VNode has been matched with another VNode in the diff */\nvar MATCHED = 1 << 17;\n\n/** Reset all mode flags */\nvar RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\nvar EMPTY_OBJ = /** @type {any} */{};\nvar EMPTY_ARR = [];\nvar IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n\nvar isArray = Array.isArray;\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nfunction assign(obj, props) {\n  // @ts-expect-error We change the type of `obj` to be `O & P`\n  for (var i in props) obj[i] = props[i];\n  return /** @type {O & P} */obj;\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {preact.ContainerNode} node The node to remove\n */\nfunction removeNode(node) {\n  if (node && node.parentNode) node.parentNode.removeChild(node);\n}\nvar slice = EMPTY_ARR.slice;\n\n/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {VNode} vnode The vnode that threw the error that was caught (except\n * for unmounting when this parameter is the highest parent that was being\n * unmounted)\n * @param {VNode} [oldVNode]\n * @param {ErrorInfo} [errorInfo]\n */\nfunction _catchError(error, vnode, oldVNode, errorInfo) {\n  /** @type {Component} */\n  var component, /** @type {ComponentType} */\n    ctor, /** @type {boolean} */\n    handled;\n  for (; vnode = vnode.__;) {\n    if ((component = vnode.__c) && !component.__) {\n      try {\n        ctor = component.constructor;\n        if (ctor && ctor.getDerivedStateFromError != null) {\n          component.setState(ctor.getDerivedStateFromError(error));\n          handled = component.__d;\n        }\n        if (component.componentDidCatch != null) {\n          component.componentDidCatch(error, errorInfo || {});\n          handled = component.__d;\n        }\n\n        // This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n        if (handled) {\n          return component.__E = component;\n        }\n      } catch (e) {\n        error = e;\n      }\n    }\n  }\n  throw error;\n}\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {Options}\n */\nvar options = {\n  __e: _catchError\n};\n\nvar vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {VNode[\"type\"]} type The node name or Component constructor for this\n * virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the\n * virtual node\n * @returns {VNode}\n */\nfunction createElement(type, props, children) {\n  var normalizedProps = {},\n    key,\n    ref,\n    i;\n  for (i in props) {\n    if (i == 'key') key = props[i];else if (i == 'ref') ref = props[i];else normalizedProps[i] = props[i];\n  }\n  if (arguments.length > 2) {\n    normalizedProps.children = arguments.length > 3 ? slice.call(arguments, 2) : children;\n  }\n\n  // If a Component VNode, check for and apply defaultProps\n  // Note: type may be undefined in development, must never error here.\n  if (typeof type == 'function' && type.defaultProps != null) {\n    for (i in type.defaultProps) {\n      if (normalizedProps[i] === undefined) {\n        normalizedProps[i] = type.defaultProps[i];\n      }\n    }\n  }\n  return createVNode(type, normalizedProps, key, ref, null);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {VNode}\n */\nfunction createVNode(type, props, key, ref, original) {\n  // V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n  // Do not inline into createElement and coerceToVNode!\n  /** @type {VNode} */\n  var vnode = {\n    type: type,\n    props: props,\n    key: key,\n    ref: ref,\n    __k: null,\n    __: null,\n    __b: 0,\n    __e: null,\n    // _nextDom must be initialized to undefined b/c it will eventually\n    // be set to dom.nextSibling which can return `null` and it is important\n    // to be able to distinguish between an uninitialized _nextDom and\n    // a _nextDom that has been set to `null`\n    __d: undefined,\n    __c: null,\n    constructor: undefined,\n    __v: original == null ? ++vnodeId : original,\n    __i: -1,\n    __u: 0\n  };\n\n  // Only invoke the vnode hook if this was *not* a direct copy:\n  if (original == null && options.vnode != null) options.vnode(vnode);\n  return vnode;\n}\nfunction createRef() {\n  return {\n    current: null\n  };\n}\nfunction Fragment(props) {\n  return props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is VNode}\n */\nvar isValidElement = function isValidElement(vnode) {\n  return vnode != null && vnode.constructor == undefined;\n};\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nfunction BaseComponent(props, context) {\n  this.props = props;\n  this.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nBaseComponent.prototype.setState = function (update, callback) {\n  // only clone state when copying to nextState the first time.\n  var s;\n  if (this.__s != null && this.__s !== this.state) {\n    s = this.__s;\n  } else {\n    s = this.__s = assign({}, this.state);\n  }\n  if (typeof update == 'function') {\n    // Some libraries like `immer` mark the current state as readonly,\n    // preventing us from mutating it, so we need to clone it. See #2716\n    update = update(assign({}, s), this.props);\n  }\n  if (update) {\n    assign(s, update);\n  }\n\n  // Skip update if updater function returned null\n  if (update == null) return;\n  if (this.__v) {\n    if (callback) {\n      this._sb.push(callback);\n    }\n    enqueueRender(this);\n  }\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nBaseComponent.prototype.forceUpdate = function (callback) {\n  if (this.__v) {\n    // Set render mode so that we can differentiate where the render request\n    // is coming from. We need this because forceUpdate should never call\n    // shouldComponentUpdate\n    this.__e = true;\n    if (callback) this.__h.push(callback);\n    enqueueRender(this);\n  }\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {ComponentChildren | void}\n */\nBaseComponent.prototype.render = Fragment;\n\n/**\n * @param {VNode} vnode\n * @param {number | null} [childIndex]\n */\nfunction getDomSibling(vnode, childIndex) {\n  if (childIndex == null) {\n    // Use childIndex==null as a signal to resume the search from the vnode's sibling\n    return vnode.__ ? getDomSibling(vnode.__, vnode.__i + 1) : null;\n  }\n  var sibling;\n  for (; childIndex < vnode.__k.length; childIndex++) {\n    sibling = vnode.__k[childIndex];\n    if (sibling != null && sibling.__e != null) {\n      // Since updateParentDomPointers keeps _dom pointer correct,\n      // we can rely on _dom to tell us if this subtree contains a\n      // rendered DOM node, and what the first rendered DOM node is\n      return sibling.__e;\n    }\n  }\n\n  // If we get here, we have not found a DOM node in this vnode's children.\n  // We must resume from this vnode's sibling (in it's parent _children array)\n  // Only climb up and search the parent if we aren't searching through a DOM\n  // VNode (meaning we reached the DOM parent of the original vnode that began\n  // the search)\n  return typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {Component} component The component to rerender\n */\nfunction renderComponent(component) {\n  var oldVNode = component.__v,\n    oldDom = oldVNode.__e,\n    commitQueue = [],\n    refQueue = [];\n  if (component.__P) {\n    var newVNode = assign({}, oldVNode);\n    newVNode.__v = oldVNode.__v + 1;\n    if (options.vnode) options.vnode(newVNode);\n    diff(component.__P, newVNode, oldVNode, component.__n, component.__P.namespaceURI, oldVNode.__u & MODE_HYDRATE ? [oldDom] : null, commitQueue, oldDom == null ? getDomSibling(oldVNode) : oldDom, !!(oldVNode.__u & MODE_HYDRATE), refQueue);\n    newVNode.__v = oldVNode.__v;\n    newVNode.__.__k[newVNode.__i] = newVNode;\n    commitRoot(commitQueue, newVNode, refQueue);\n    if (newVNode.__e != oldDom) {\n      updateParentDomPointers(newVNode);\n    }\n  }\n}\n\n/**\n * @param {VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n  if ((vnode = vnode.__) != null && vnode.__c != null) {\n    vnode.__e = vnode.__c.base = null;\n    for (var i = 0; i < vnode.__k.length; i++) {\n      var child = vnode.__k[i];\n      if (child != null && child.__e != null) {\n        vnode.__e = vnode.__c.base = child.__e;\n        break;\n      }\n    }\n    return updateParentDomPointers(vnode);\n  }\n}\n\n/**\n * The render queue\n * @type {Array<Component>}\n */\nvar rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nvar prevDebounce;\nvar defer = typeof Promise == 'function' ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout;\n\n/**\n * Enqueue a rerender of a component\n * @param {Component} c The component to rerender\n */\nfunction enqueueRender(c) {\n  if (!c.__d && (c.__d = true) && rerenderQueue.push(c) && !process.__r++ || prevDebounce !== options.debounceRendering) {\n    prevDebounce = options.debounceRendering;\n    (prevDebounce || defer)(process);\n  }\n}\n\n/**\n * @param {Component} a\n * @param {Component} b\n */\nvar depthSort = function depthSort(a, b) {\n  return a.__v.__b - b.__v.__b;\n};\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n  var c;\n  rerenderQueue.sort(depthSort);\n  // Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n  // process() calls from getting scheduled while `queue` is still being consumed.\n  while (c = rerenderQueue.shift()) {\n    if (c.__d) {\n      var renderQueueLength = rerenderQueue.length;\n      renderComponent(c);\n      if (rerenderQueue.length > renderQueueLength) {\n        // When i.e. rerendering a provider additional new items can be injected, we want to\n        // keep the order from top to bottom with those new items so we can handle them in a\n        // single pass\n        rerenderQueue.sort(depthSort);\n      }\n    }\n  }\n  process.__r = 0;\n}\nprocess.__r = 0;\n\n/**\n * Diff the children of a virtual node\n * @param {PreactElement} parentDom The DOM element whose children are being\n * diffed\n * @param {ComponentChildren[]} renderResult\n * @param {VNode} newParentVNode The new virtual node whose children should be\n * diff'ed against oldParentVNode\n * @param {VNode} oldParentVNode The old virtual node whose children should be\n * diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nfunction diffChildren(parentDom, renderResult, newParentVNode, oldParentVNode, globalContext, namespace, excessDomChildren, commitQueue, oldDom, isHydrating, refQueue) {\n  var i, /** @type {VNode} */\n    oldVNode, /** @type {VNode} */\n    childVNode, /** @type {PreactElement} */\n    newDom, /** @type {PreactElement} */\n    firstChildDom;\n\n  // This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n  // as EMPTY_OBJ._children should be `undefined`.\n  /** @type {VNode[]} */\n  var oldChildren = oldParentVNode && oldParentVNode.__k || EMPTY_ARR;\n  var newChildrenLength = renderResult.length;\n  newParentVNode.__d = oldDom;\n  constructNewChildrenArray(newParentVNode, renderResult, oldChildren);\n  oldDom = newParentVNode.__d;\n  for (i = 0; i < newChildrenLength; i++) {\n    childVNode = newParentVNode.__k[i];\n    if (childVNode == null) continue;\n\n    // At this point, constructNewChildrenArray has assigned _index to be the\n    // matchingIndex for this VNode's oldVNode (or -1 if there is no oldVNode).\n    if (childVNode.__i === -1) {\n      oldVNode = EMPTY_OBJ;\n    } else {\n      oldVNode = oldChildren[childVNode.__i] || EMPTY_OBJ;\n    }\n\n    // Update childVNode._index to its final index\n    childVNode.__i = i;\n\n    // Morph the old element into the new one, but don't append it to the dom yet\n    diff(parentDom, childVNode, oldVNode, globalContext, namespace, excessDomChildren, commitQueue, oldDom, isHydrating, refQueue);\n\n    // Adjust DOM nodes\n    newDom = childVNode.__e;\n    if (childVNode.ref && oldVNode.ref != childVNode.ref) {\n      if (oldVNode.ref) {\n        applyRef(oldVNode.ref, null, childVNode);\n      }\n      refQueue.push(childVNode.ref, childVNode.__c || newDom, childVNode);\n    }\n    if (firstChildDom == null && newDom != null) {\n      firstChildDom = newDom;\n    }\n    if (childVNode.__u & INSERT_VNODE || oldVNode.__k === childVNode.__k) {\n      oldDom = insert(childVNode, oldDom, parentDom);\n    } else if (typeof childVNode.type == 'function' && childVNode.__d !== undefined) {\n      // Since Fragments or components that return Fragment like VNodes can\n      // contain multiple DOM nodes as the same level, continue the diff from\n      // the sibling of last DOM child of this child VNode\n      oldDom = childVNode.__d;\n    } else if (newDom) {\n      oldDom = newDom.nextSibling;\n    }\n\n    // Eagerly cleanup _nextDom. We don't need to persist the value because it\n    // is only used by `diffChildren` to determine where to resume the diff\n    // after diffing Components and Fragments. Once we store it the nextDOM\n    // local var, we can clean up the property. Also prevents us hanging on to\n    // DOM nodes that may have been unmounted.\n    childVNode.__d = undefined;\n\n    // Unset diffing flags\n    childVNode.__u &= ~(INSERT_VNODE | MATCHED);\n  }\n\n  // TODO: With new child diffing algo, consider alt ways to diff Fragments.\n  // Such as dropping oldDom and moving fragments in place\n  //\n  // Because the newParentVNode is Fragment-like, we need to set it's\n  // _nextDom property to the nextSibling of its last child DOM node.\n  //\n  // `oldDom` contains the correct value here because if the last child\n  // is a Fragment-like, then oldDom has already been set to that child's _nextDom.\n  // If the last child is a DOM VNode, then oldDom will be set to that DOM\n  // node's nextSibling.\n  newParentVNode.__d = oldDom;\n  newParentVNode.__e = firstChildDom;\n}\n\n/**\n * @param {VNode} newParentVNode\n * @param {ComponentChildren[]} renderResult\n * @param {VNode[]} oldChildren\n */\nfunction constructNewChildrenArray(newParentVNode, renderResult, oldChildren) {\n  /** @type {number} */\n  var i;\n  /** @type {VNode} */\n  var childVNode;\n  /** @type {VNode} */\n  var oldVNode;\n  var newChildrenLength = renderResult.length;\n  var oldChildrenLength = oldChildren.length,\n    remainingOldChildren = oldChildrenLength;\n  var skew = 0;\n  newParentVNode.__k = [];\n  for (i = 0; i < newChildrenLength; i++) {\n    // @ts-expect-error We are reusing the childVNode variable to hold both the\n    // pre and post normalized childVNode\n    childVNode = renderResult[i];\n    if (childVNode == null || typeof childVNode == 'boolean' || typeof childVNode == 'function') {\n      childVNode = newParentVNode.__k[i] = null;\n      continue;\n    }\n    // If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n    // or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n    // it's own DOM & etc. pointers\n    else if (typeof childVNode == 'string' || typeof childVNode == 'number' ||\n    // eslint-disable-next-line valid-typeof\n    typeof childVNode == 'bigint' || childVNode.constructor == String) {\n      childVNode = newParentVNode.__k[i] = createVNode(null, childVNode, null, null, null);\n    } else if (isArray(childVNode)) {\n      childVNode = newParentVNode.__k[i] = createVNode(Fragment, {\n        children: childVNode\n      }, null, null, null);\n    } else if (childVNode.constructor === undefined && childVNode.__b > 0) {\n      // VNode is already in use, clone it. This can happen in the following\n      // scenario:\n      //   const reuse = <div />\n      //   <div>{reuse}<span />{reuse}</div>\n      childVNode = newParentVNode.__k[i] = createVNode(childVNode.type, childVNode.props, childVNode.key, childVNode.ref ? childVNode.ref : null, childVNode.__v);\n    } else {\n      childVNode = newParentVNode.__k[i] = childVNode;\n    }\n    var skewedIndex = i + skew;\n    childVNode.__ = newParentVNode;\n    childVNode.__b = newParentVNode.__b + 1;\n\n    // Temporarily store the matchingIndex on the _index property so we can pull\n    // out the oldVNode in diffChildren. We'll override this to the VNode's\n    // final index after using this property to get the oldVNode\n    var matchingIndex = childVNode.__i = findMatchingIndex(childVNode, oldChildren, skewedIndex, remainingOldChildren);\n    oldVNode = null;\n    if (matchingIndex !== -1) {\n      oldVNode = oldChildren[matchingIndex];\n      remainingOldChildren--;\n      if (oldVNode) {\n        oldVNode.__u |= MATCHED;\n      }\n    }\n\n    // Here, we define isMounting for the purposes of the skew diffing\n    // algorithm. Nodes that are unsuspending are considered mounting and we detect\n    // this by checking if oldVNode._original === null\n    var isMounting = oldVNode == null || oldVNode.__v === null;\n    if (isMounting) {\n      if (matchingIndex == -1) {\n        skew--;\n      }\n\n      // If we are mounting a DOM VNode, mark it for insertion\n      if (typeof childVNode.type != 'function') {\n        childVNode.__u |= INSERT_VNODE;\n      }\n    } else if (matchingIndex !== skewedIndex) {\n      // When we move elements around i.e. [0, 1, 2] --> [1, 0, 2]\n      // --> we diff 1, we find it at position 1 while our skewed index is 0 and our skew is 0\n      //     we set the skew to 1 as we found an offset.\n      // --> we diff 0, we find it at position 0 while our skewed index is at 2 and our skew is 1\n      //     this makes us increase the skew again.\n      // --> we diff 2, we find it at position 2 while our skewed index is at 4 and our skew is 2\n      //\n      // this becomes an optimization question where currently we see a 1 element offset as an insertion\n      // or deletion i.e. we optimize for [0, 1, 2] --> [9, 0, 1, 2]\n      // while a more than 1 offset we see as a swap.\n      // We could probably build heuristics for having an optimized course of action here as well, but\n      // might go at the cost of some bytes.\n      //\n      // If we wanted to optimize for i.e. only swaps we'd just do the last two code-branches and have\n      // only the first item be a re-scouting and all the others fall in their skewed counter-part.\n      // We could also further optimize for swaps\n      if (matchingIndex == skewedIndex - 1) {\n        skew--;\n      } else if (matchingIndex == skewedIndex + 1) {\n        skew++;\n      } else {\n        if (matchingIndex > skewedIndex) {\n          skew--;\n        } else {\n          skew++;\n        }\n\n        // Move this VNode's DOM if the original index (matchingIndex) doesn't\n        // match the new skew index (i + new skew)\n        // In the former two branches we know that it matches after skewing\n        childVNode.__u |= INSERT_VNODE;\n      }\n    }\n  }\n\n  // Remove remaining oldChildren if there are any. Loop forwards so that as we\n  // unmount DOM from the beginning of the oldChildren, we can adjust oldDom to\n  // point to the next child, which needs to be the first DOM node that won't be\n  // unmounted.\n  if (remainingOldChildren) {\n    for (i = 0; i < oldChildrenLength; i++) {\n      oldVNode = oldChildren[i];\n      if (oldVNode != null && (oldVNode.__u & MATCHED) === 0) {\n        if (oldVNode.__e == newParentVNode.__d) {\n          newParentVNode.__d = getDomSibling(oldVNode);\n        }\n        unmount(oldVNode, oldVNode);\n      }\n    }\n  }\n}\n\n/**\n * @param {VNode} parentVNode\n * @param {PreactElement} oldDom\n * @param {PreactElement} parentDom\n * @returns {PreactElement}\n */\nfunction insert(parentVNode, oldDom, parentDom) {\n  // Note: VNodes in nested suspended trees may be missing _children.\n\n  if (typeof parentVNode.type == 'function') {\n    var children = parentVNode.__k;\n    for (var i = 0; children && i < children.length; i++) {\n      if (children[i]) {\n        // If we enter this code path on sCU bailout, where we copy\n        // oldVNode._children to newVNode._children, we need to update the old\n        // children's _parent pointer to point to the newVNode (parentVNode\n        // here).\n        children[i].__ = parentVNode;\n        oldDom = insert(children[i], oldDom, parentDom);\n      }\n    }\n    return oldDom;\n  } else if (parentVNode.__e != oldDom) {\n    if (oldDom && parentVNode.type && !parentDom.contains(oldDom)) {\n      oldDom = getDomSibling(parentVNode);\n    }\n    parentDom.insertBefore(parentVNode.__e, oldDom || null);\n    oldDom = parentVNode.__e;\n  }\n  do {\n    oldDom = oldDom && oldDom.nextSibling;\n  } while (oldDom != null && oldDom.nodeType === 8);\n  return oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {ComponentChildren} children The unflattened children of a virtual\n * node\n * @returns {VNode[]}\n */\nfunction toChildArray(children, out) {\n  out = out || [];\n  if (children == null || typeof children == 'boolean') ; else if (isArray(children)) {\n    children.some(function (child) {\n      toChildArray(child, out);\n    });\n  } else {\n    out.push(children);\n  }\n  return out;\n}\n\n/**\n * @param {VNode} childVNode\n * @param {VNode[]} oldChildren\n * @param {number} skewedIndex\n * @param {number} remainingOldChildren\n * @returns {number}\n */\nfunction findMatchingIndex(childVNode, oldChildren, skewedIndex, remainingOldChildren) {\n  var key = childVNode.key;\n  var type = childVNode.type;\n  var x = skewedIndex - 1;\n  var y = skewedIndex + 1;\n  var oldVNode = oldChildren[skewedIndex];\n\n  // We only need to perform a search if there are more children\n  // (remainingOldChildren) to search. However, if the oldVNode we just looked\n  // at skewedIndex was not already used in this diff, then there must be at\n  // least 1 other (so greater than 1) remainingOldChildren to attempt to match\n  // against. So the following condition checks that ensuring\n  // remainingOldChildren > 1 if the oldVNode is not already used/matched. Else\n  // if the oldVNode was null or matched, then there could needs to be at least\n  // 1 (aka `remainingOldChildren > 0`) children to find and compare against.\n  var shouldSearch = remainingOldChildren > (oldVNode != null && (oldVNode.__u & MATCHED) === 0 ? 1 : 0);\n  if (oldVNode === null || oldVNode && key == oldVNode.key && type === oldVNode.type && (oldVNode.__u & MATCHED) === 0) {\n    return skewedIndex;\n  } else if (shouldSearch) {\n    while (x >= 0 || y < oldChildren.length) {\n      if (x >= 0) {\n        oldVNode = oldChildren[x];\n        if (oldVNode && (oldVNode.__u & MATCHED) === 0 && key == oldVNode.key && type === oldVNode.type) {\n          return x;\n        }\n        x--;\n      }\n      if (y < oldChildren.length) {\n        oldVNode = oldChildren[y];\n        if (oldVNode && (oldVNode.__u & MATCHED) === 0 && key == oldVNode.key && type === oldVNode.type) {\n          return y;\n        }\n        y++;\n      }\n    }\n  }\n  return -1;\n}\n\nfunction setStyle(style, key, value) {\n  if (key[0] === '-') {\n    style.setProperty(key, value == null ? '' : value);\n  } else if (value == null) {\n    style[key] = '';\n  } else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n    style[key] = value;\n  } else {\n    style[key] = value + 'px';\n  }\n}\n\n// A logical clock to solve issues like https://github.com/preactjs/preact/issues/3927.\n// When the DOM performs an event it leaves micro-ticks in between bubbling up which means that\n// an event can trigger on a newly reated DOM-node while the event bubbles up.\n//\n// Originally inspired by Vue\n// (https://github.com/vuejs/core/blob/caeb8a68811a1b0f79/packages/runtime-dom/src/modules/events.ts#L90-L101),\n// but modified to use a logical clock instead of Date.now() in case event handlers get attached\n// and events get dispatched during the same millisecond.\n//\n// The clock is incremented after each new event dispatch. This allows 1 000 000 new events\n// per second for over 280 years before the value reaches Number.MAX_SAFE_INTEGER (2**53 - 1).\nvar eventClock = 0;\n\n/**\n * Set a property value on a DOM node\n * @param {PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {string} namespace Whether or not this DOM node is an SVG node or not\n */\nfunction setProperty(dom, name, value, oldValue, namespace) {\n  var useCapture;\n  o: if (name === 'style') {\n    if (typeof value == 'string') {\n      dom.style.cssText = value;\n    } else {\n      if (typeof oldValue == 'string') {\n        dom.style.cssText = oldValue = '';\n      }\n      if (oldValue) {\n        for (name in oldValue) {\n          if (!(value && name in value)) {\n            setStyle(dom.style, name, '');\n          }\n        }\n      }\n      if (value) {\n        for (name in value) {\n          if (!oldValue || value[name] !== oldValue[name]) {\n            setStyle(dom.style, name, value[name]);\n          }\n        }\n      }\n    }\n  }\n  // Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n  else if (name[0] === 'o' && name[1] === 'n') {\n    useCapture = name !== (name = name.replace(/(PointerCapture)$|Capture$/i, '$1'));\n\n    // Infer correct casing for DOM built-in events:\n    if (name.toLowerCase() in dom || name === 'onFocusOut' || name === 'onFocusIn') name = name.toLowerCase().slice(2);else name = name.slice(2);\n    if (!dom.l) dom.l = {};\n    dom.l[name + useCapture] = value;\n    if (value) {\n      if (!oldValue) {\n        value._attached = eventClock;\n        dom.addEventListener(name, useCapture ? eventProxyCapture : eventProxy, useCapture);\n      } else {\n        value._attached = oldValue._attached;\n      }\n    } else {\n      dom.removeEventListener(name, useCapture ? eventProxyCapture : eventProxy, useCapture);\n    }\n  } else {\n    if (namespace == 'http://www.w3.org/2000/svg') {\n      // Normalize incorrect prop usage for SVG:\n      // - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n      // - className --> class\n      name = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n    } else if (name != 'width' && name != 'height' && name != 'href' && name != 'list' && name != 'form' &&\n    // Default value in browsers is `-1` and an empty string is\n    // cast to `0` instead\n    name != 'tabIndex' && name != 'download' && name != 'rowSpan' && name != 'colSpan' && name != 'role' && name != 'popover' && name in dom) {\n      try {\n        dom[name] = value == null ? '' : value;\n        // labelled break is 1b smaller here than a return statement (sorry)\n        break o;\n      } catch (e) {}\n    }\n\n    // aria- and data- attributes have no boolean representation.\n    // A `false` value is different from the attribute not being\n    // present, so we can't remove it. For non-boolean aria\n    // attributes we could treat false as a removal, but the\n    // amount of exceptions would cost too many bytes. On top of\n    // that other frameworks generally stringify `false`.\n\n    if (typeof value == 'function') ; else if (value != null && (value !== false || name[4] === '-')) {\n      dom.setAttribute(name, name == 'popover' && value == true ? '' : value);\n    } else {\n      dom.removeAttribute(name);\n    }\n  }\n}\n\n/**\n * Create an event proxy function.\n * @param {boolean} useCapture Is the event handler for the capture phase.\n * @private\n */\nfunction createEventProxy(useCapture) {\n  /**\n   * Proxy an event to hooked event handlers\n   * @param {PreactEvent} e The event object from the browser\n   * @private\n   */\n  return function (e) {\n    if (this.l) {\n      var eventHandler = this.l[e.type + useCapture];\n      if (e._dispatched == null) {\n        e._dispatched = eventClock++;\n\n        // When `e._dispatched` is smaller than the time when the targeted event\n        // handler was attached we know we have bubbled up to an element that was added\n        // during patching the DOM.\n      } else if (e._dispatched < eventHandler._attached) {\n        return;\n      }\n      return eventHandler(options.event ? options.event(e) : e);\n    }\n  };\n}\nvar eventProxy = createEventProxy(false);\nvar eventProxyCapture = createEventProxy(true);\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {PreactElement} parentDom The parent of the DOM element\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nfunction diff(parentDom, newVNode, oldVNode, globalContext, namespace, excessDomChildren, commitQueue, oldDom, isHydrating, refQueue) {\n  /** @type {any} */\n  var tmp,\n    newType = newVNode.type;\n\n  // When passing through createElement it assigns the object\n  // constructor as undefined. This to prevent JSON-injection.\n  if (newVNode.constructor !== undefined) return null;\n\n  // If the previous diff bailed out, resume creating/hydrating.\n  if (oldVNode.__u & MODE_SUSPENDED) {\n    isHydrating = !!(oldVNode.__u & MODE_HYDRATE);\n    oldDom = newVNode.__e = oldVNode.__e;\n    excessDomChildren = [oldDom];\n  }\n  if (tmp = options.__b) tmp(newVNode);\n  outer: if (typeof newType == 'function') {\n    try {\n      var c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n      var newProps = newVNode.props;\n      var isClassComponent = 'prototype' in newType && newType.prototype.render;\n\n      // Necessary for createContext api. Setting this property will pass\n      // the context value as `this.context` just for this component.\n      tmp = newType.contextType;\n      var provider = tmp && globalContext[tmp.__c];\n      var componentContext = tmp ? provider ? provider.props.value : tmp.__ : globalContext;\n\n      // Get component and set it to `c`\n      if (oldVNode.__c) {\n        c = newVNode.__c = oldVNode.__c;\n        clearProcessingException = c.__ = c.__E;\n      } else {\n        // Instantiate the new component\n        if (isClassComponent) {\n          // @ts-expect-error The check above verifies that newType is suppose to be constructed\n          newVNode.__c = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n        } else {\n          // @ts-expect-error Trust me, Component implements the interface we want\n          newVNode.__c = c = new BaseComponent(newProps, componentContext);\n          c.constructor = newType;\n          c.render = doRender;\n        }\n        if (provider) provider.sub(c);\n        c.props = newProps;\n        if (!c.state) c.state = {};\n        c.context = componentContext;\n        c.__n = globalContext;\n        isNew = c.__d = true;\n        c.__h = [];\n        c._sb = [];\n      }\n\n      // Invoke getDerivedStateFromProps\n      if (isClassComponent && c.__s == null) {\n        c.__s = c.state;\n      }\n      if (isClassComponent && newType.getDerivedStateFromProps != null) {\n        if (c.__s == c.state) {\n          c.__s = assign({}, c.__s);\n        }\n        assign(c.__s, newType.getDerivedStateFromProps(newProps, c.__s));\n      }\n      oldProps = c.props;\n      oldState = c.state;\n      c.__v = newVNode;\n\n      // Invoke pre-render lifecycle methods\n      if (isNew) {\n        if (isClassComponent && newType.getDerivedStateFromProps == null && c.componentWillMount != null) {\n          c.componentWillMount();\n        }\n        if (isClassComponent && c.componentDidMount != null) {\n          c.__h.push(c.componentDidMount);\n        }\n      } else {\n        if (isClassComponent && newType.getDerivedStateFromProps == null && newProps !== oldProps && c.componentWillReceiveProps != null) {\n          c.componentWillReceiveProps(newProps, componentContext);\n        }\n        if (!c.__e && (c.shouldComponentUpdate != null && c.shouldComponentUpdate(newProps, c.__s, componentContext) === false || newVNode.__v === oldVNode.__v)) {\n          // More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n          if (newVNode.__v !== oldVNode.__v) {\n            // When we are dealing with a bail because of sCU we have to update\n            // the props, state and dirty-state.\n            // when we are dealing with strict-equality we don't as the child could still\n            // be dirtied see #3883\n            c.props = newProps;\n            c.state = c.__s;\n            c.__d = false;\n          }\n          newVNode.__e = oldVNode.__e;\n          newVNode.__k = oldVNode.__k;\n          newVNode.__k.some(function (vnode) {\n            if (vnode) vnode.__ = newVNode;\n          });\n          for (var i = 0; i < c._sb.length; i++) {\n            c.__h.push(c._sb[i]);\n          }\n          c._sb = [];\n          if (c.__h.length) {\n            commitQueue.push(c);\n          }\n          break outer;\n        }\n        if (c.componentWillUpdate != null) {\n          c.componentWillUpdate(newProps, c.__s, componentContext);\n        }\n        if (isClassComponent && c.componentDidUpdate != null) {\n          c.__h.push(function () {\n            c.componentDidUpdate(oldProps, oldState, snapshot);\n          });\n        }\n      }\n      c.context = componentContext;\n      c.props = newProps;\n      c.__P = parentDom;\n      c.__e = false;\n      var renderHook = options.__r,\n        count = 0;\n      if (isClassComponent) {\n        c.state = c.__s;\n        c.__d = false;\n        if (renderHook) renderHook(newVNode);\n        tmp = c.render(c.props, c.state, c.context);\n        for (var _i = 0; _i < c._sb.length; _i++) {\n          c.__h.push(c._sb[_i]);\n        }\n        c._sb = [];\n      } else {\n        do {\n          c.__d = false;\n          if (renderHook) renderHook(newVNode);\n          tmp = c.render(c.props, c.state, c.context);\n\n          // Handle setState called in render, see #2553\n          c.state = c.__s;\n        } while (c.__d && ++count < 25);\n      }\n\n      // Handle setState called in render, see #2553\n      c.state = c.__s;\n      if (c.getChildContext != null) {\n        globalContext = assign(assign({}, globalContext), c.getChildContext());\n      }\n      if (isClassComponent && !isNew && c.getSnapshotBeforeUpdate != null) {\n        snapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n      }\n      var isTopLevelFragment = tmp != null && tmp.type === Fragment && tmp.key == null;\n      var renderResult = isTopLevelFragment ? tmp.props.children : tmp;\n      diffChildren(parentDom, isArray(renderResult) ? renderResult : [renderResult], newVNode, oldVNode, globalContext, namespace, excessDomChildren, commitQueue, oldDom, isHydrating, refQueue);\n      c.base = newVNode.__e;\n\n      // We successfully rendered this VNode, unset any stored hydration/bailout state:\n      newVNode.__u &= RESET_MODE;\n      if (c.__h.length) {\n        commitQueue.push(c);\n      }\n      if (clearProcessingException) {\n        c.__E = c.__ = null;\n      }\n    } catch (e) {\n      newVNode.__v = null;\n      // if hydrating or creating initial tree, bailout preserves DOM:\n      if (isHydrating || excessDomChildren != null) {\n        newVNode.__u |= isHydrating ? MODE_HYDRATE | MODE_SUSPENDED : MODE_HYDRATE;\n        while (oldDom && oldDom.nodeType === 8 && oldDom.nextSibling) {\n          oldDom = oldDom.nextSibling;\n        }\n        excessDomChildren[excessDomChildren.indexOf(oldDom)] = null;\n        newVNode.__e = oldDom;\n      } else {\n        newVNode.__e = oldVNode.__e;\n        newVNode.__k = oldVNode.__k;\n      }\n      options.__e(e, newVNode, oldVNode);\n    }\n  } else if (excessDomChildren == null && newVNode.__v === oldVNode.__v) {\n    newVNode.__k = oldVNode.__k;\n    newVNode.__e = oldVNode.__e;\n  } else {\n    newVNode.__e = diffElementNodes(oldVNode.__e, newVNode, oldVNode, globalContext, namespace, excessDomChildren, commitQueue, isHydrating, refQueue);\n  }\n  if (tmp = options.diffed) tmp(newVNode);\n}\n\n/**\n * @param {Array<Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {VNode} root\n */\nfunction commitRoot(commitQueue, root, refQueue) {\n  root.__d = undefined;\n  for (var i = 0; i < refQueue.length; i++) {\n    applyRef(refQueue[i], refQueue[++i], refQueue[++i]);\n  }\n  if (options.__c) options.__c(root, commitQueue);\n  commitQueue.some(function (c) {\n    try {\n      // @ts-expect-error Reuse the commitQueue variable here so the type changes\n      commitQueue = c.__h;\n      c.__h = [];\n      commitQueue.some(function (cb) {\n        // @ts-expect-error See above comment on commitQueue\n        cb.call(c);\n      });\n    } catch (e) {\n      options.__e(e, c.__v);\n    }\n  });\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {PreactElement} dom The DOM element representing the virtual nodes\n * being diffed\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n * @returns {PreactElement}\n */\nfunction diffElementNodes(dom, newVNode, oldVNode, globalContext, namespace, excessDomChildren, commitQueue, isHydrating, refQueue) {\n  var oldProps = oldVNode.props;\n  var newProps = newVNode.props;\n  var nodeType = /** @type {string} */newVNode.type;\n  /** @type {any} */\n  var i;\n  /** @type {{ __html?: string }} */\n  var newHtml;\n  /** @type {{ __html?: string }} */\n  var oldHtml;\n  /** @type {ComponentChildren} */\n  var newChildren;\n  var value;\n  var inputValue;\n  var checked;\n\n  // Tracks entering and exiting namespaces when descending through the tree.\n  if (nodeType === 'svg') namespace = 'http://www.w3.org/2000/svg';else if (nodeType === 'math') namespace = 'http://www.w3.org/1998/Math/MathML';else if (!namespace) namespace = 'http://www.w3.org/1999/xhtml';\n  if (excessDomChildren != null) {\n    for (i = 0; i < excessDomChildren.length; i++) {\n      value = excessDomChildren[i];\n\n      // if newVNode matches an element in excessDomChildren or the `dom`\n      // argument matches an element in excessDomChildren, remove it from\n      // excessDomChildren so it isn't later removed in diffChildren\n      if (value && 'setAttribute' in value === !!nodeType && (nodeType ? value.localName === nodeType : value.nodeType === 3)) {\n        dom = value;\n        excessDomChildren[i] = null;\n        break;\n      }\n    }\n  }\n  if (dom == null) {\n    if (nodeType === null) {\n      return options.document.createTextNode(newProps);\n    }\n    dom = options.document.createElementNS(namespace, nodeType, newProps.is && newProps);\n\n    // we are creating a new node, so we can assume this is a new subtree (in\n    // case we are hydrating), this deopts the hydrate\n    if (isHydrating) {\n      if (options.__m) options.__m(newVNode, excessDomChildren);\n      isHydrating = false;\n    }\n    // we created a new parent, so none of the previously attached children can be reused:\n    excessDomChildren = null;\n  }\n  if (nodeType === null) {\n    // During hydration, we still have to split merged text from SSR'd HTML.\n    if (oldProps !== newProps && (!isHydrating || dom.data !== newProps)) {\n      dom.data = newProps;\n    }\n  } else {\n    // If excessDomChildren was not null, repopulate it with the current element's children:\n    excessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n    oldProps = oldVNode.props || EMPTY_OBJ;\n\n    // If we are in a situation where we are not hydrating but are using\n    // existing DOM (e.g. replaceNode) we should read the existing DOM\n    // attributes to diff them\n    if (!isHydrating && excessDomChildren != null) {\n      oldProps = {};\n      for (i = 0; i < dom.attributes.length; i++) {\n        value = dom.attributes[i];\n        oldProps[value.name] = value.value;\n      }\n    }\n    for (i in oldProps) {\n      value = oldProps[i];\n      if (i == 'children') ; else if (i == 'dangerouslySetInnerHTML') {\n        oldHtml = value;\n      } else if (!(i in newProps)) {\n        if (i == 'value' && 'defaultValue' in newProps || i == 'checked' && 'defaultChecked' in newProps) {\n          continue;\n        }\n        setProperty(dom, i, null, value, namespace);\n      }\n    }\n\n    // During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n    // @TODO we should warn in debug mode when props don't match here.\n    for (i in newProps) {\n      value = newProps[i];\n      if (i == 'children') {\n        newChildren = value;\n      } else if (i == 'dangerouslySetInnerHTML') {\n        newHtml = value;\n      } else if (i == 'value') {\n        inputValue = value;\n      } else if (i == 'checked') {\n        checked = value;\n      } else if ((!isHydrating || typeof value == 'function') && oldProps[i] !== value) {\n        setProperty(dom, i, value, oldProps[i], namespace);\n      }\n    }\n\n    // If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n    if (newHtml) {\n      // Avoid re-applying the same '__html' if it did not changed between re-render\n      if (!isHydrating && (!oldHtml || newHtml.__html !== oldHtml.__html && newHtml.__html !== dom.innerHTML)) {\n        dom.innerHTML = newHtml.__html;\n      }\n      newVNode.__k = [];\n    } else {\n      if (oldHtml) dom.innerHTML = '';\n      diffChildren(dom, isArray(newChildren) ? newChildren : [newChildren], newVNode, oldVNode, globalContext, nodeType === 'foreignObject' ? 'http://www.w3.org/1999/xhtml' : namespace, excessDomChildren, commitQueue, excessDomChildren ? excessDomChildren[0] : oldVNode.__k && getDomSibling(oldVNode, 0), isHydrating, refQueue);\n\n      // Remove children that are not part of any vnode.\n      if (excessDomChildren != null) {\n        for (i = excessDomChildren.length; i--;) {\n          removeNode(excessDomChildren[i]);\n        }\n      }\n    }\n\n    // As above, don't diff props during hydration\n    if (!isHydrating) {\n      i = 'value';\n      if (nodeType === 'progress' && inputValue == null) {\n        dom.removeAttribute('value');\n      } else if (inputValue !== undefined && (\n      // #2756 For the <progress>-element the initial value is 0,\n      // despite the attribute not being present. When the attribute\n      // is missing the progress bar is treated as indeterminate.\n      // To fix that we'll always update it when it is 0 for progress elements\n      inputValue !== dom[i] || nodeType === 'progress' && !inputValue ||\n      // This is only for IE 11 to fix <select> value not being updated.\n      // To avoid a stale select value we need to set the option.value\n      // again, which triggers IE11 to re-evaluate the select value\n      nodeType === 'option' && inputValue !== oldProps[i])) {\n        setProperty(dom, i, inputValue, oldProps[i], namespace);\n      }\n      i = 'checked';\n      if (checked !== undefined && checked !== dom[i]) {\n        setProperty(dom, i, checked, oldProps[i], namespace);\n      }\n    }\n  }\n  return dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {Ref<any> & { _unmount?: unknown }} ref\n * @param {any} value\n * @param {VNode} vnode\n */\nfunction applyRef(ref, value, vnode) {\n  try {\n    if (typeof ref == 'function') {\n      var hasRefUnmount = typeof ref.__u == 'function';\n      if (hasRefUnmount) {\n        // @ts-ignore TS doesn't like moving narrowing checks into variables\n        ref.__u();\n      }\n      if (!hasRefUnmount || value != null) {\n        // Store the cleanup function on the function\n        // instance object itself to avoid shape\n        // transitioning vnode\n        ref.__u = ref(value);\n      }\n    } else ref.current = value;\n  } catch (e) {\n    options.__e(e, vnode);\n  }\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {VNode} vnode The virtual node to unmount\n * @param {VNode} parentVNode The parent of the VNode that initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nfunction unmount(vnode, parentVNode, skipRemove) {\n  var r;\n  if (options.unmount) options.unmount(vnode);\n  if (r = vnode.ref) {\n    if (!r.current || r.current === vnode.__e) {\n      applyRef(r, null, parentVNode);\n    }\n  }\n  if ((r = vnode.__c) != null) {\n    if (r.componentWillUnmount) {\n      try {\n        r.componentWillUnmount();\n      } catch (e) {\n        options.__e(e, parentVNode);\n      }\n    }\n    r.base = r.__P = null;\n  }\n  if (r = vnode.__k) {\n    for (var i = 0; i < r.length; i++) {\n      if (r[i]) {\n        unmount(r[i], parentVNode, skipRemove || typeof vnode.type != 'function');\n      }\n    }\n  }\n  if (!skipRemove) {\n    removeNode(vnode.__e);\n  }\n\n  // Must be set to `undefined` to properly clean up `_nextDom`\n  // for which `null` is a valid value. See comment in `create-element.js`\n  vnode.__c = vnode.__ = vnode.__e = vnode.__d = undefined;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n  return this.constructor(props, context);\n}\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {ComponentChild} vnode The virtual node to render\n * @param {PreactElement} parentDom The DOM element to render into\n * @param {PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nfunction render(vnode, parentDom, replaceNode) {\n  if (options.__) options.__(vnode, parentDom);\n\n  // We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n  // hydration mode or not by passing the `hydrate` function instead of a DOM\n  // element..\n  var isHydrating = typeof replaceNode == 'function';\n\n  // To be able to support calling `render()` multiple times on the same\n  // DOM node, we need to obtain a reference to the previous tree. We do\n  // this by assigning a new `_children` property to DOM nodes which points\n  // to the last rendered tree. By default this property is not present, which\n  // means that we are mounting a new tree for the first time.\n  var oldVNode = isHydrating ? null : replaceNode && replaceNode.__k || parentDom.__k;\n  vnode = (!isHydrating && replaceNode || parentDom).__k = createElement(Fragment, null, [vnode]);\n\n  // List of effects that need to be called after diffing.\n  var commitQueue = [],\n    refQueue = [];\n  diff(parentDom,\n  // Determine the new vnode tree and store it on the DOM element on\n  // our custom `_children` property.\n  vnode, oldVNode || EMPTY_OBJ, EMPTY_OBJ, parentDom.namespaceURI, !isHydrating && replaceNode ? [replaceNode] : oldVNode ? null : parentDom.firstChild ? slice.call(parentDom.childNodes) : null, commitQueue, !isHydrating && replaceNode ? replaceNode : oldVNode ? oldVNode.__e : parentDom.firstChild, isHydrating, refQueue);\n\n  // Flush all queued effects\n  commitRoot(commitQueue, vnode, refQueue);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {ComponentChild} vnode The virtual node to render\n * @param {PreactElement} parentDom The DOM element to update\n */\nfunction hydrate(vnode, parentDom) {\n  render(vnode, parentDom, hydrate);\n}\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its\n * children.\n * @param {VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<ComponentChildren>} rest Any additional arguments will be used\n * as replacement children.\n * @returns {VNode}\n */\nfunction cloneElement(vnode, props, children) {\n  var normalizedProps = assign({}, vnode.props),\n    key,\n    ref,\n    i;\n  var defaultProps;\n  if (vnode.type && vnode.type.defaultProps) {\n    defaultProps = vnode.type.defaultProps;\n  }\n  for (i in props) {\n    if (i == 'key') key = props[i];else if (i == 'ref') ref = props[i];else if (props[i] === undefined && defaultProps !== undefined) {\n      normalizedProps[i] = defaultProps[i];\n    } else {\n      normalizedProps[i] = props[i];\n    }\n  }\n  if (arguments.length > 2) {\n    normalizedProps.children = arguments.length > 3 ? slice.call(arguments, 2) : children;\n  }\n  return createVNode(vnode.type, normalizedProps, key || vnode.key, ref || vnode.ref, null);\n}\n\nvar i = 0;\nfunction createContext(defaultValue, contextId) {\n  contextId = '__cC' + i++;\n  var context = {\n    __c: contextId,\n    __: defaultValue,\n    /** @type {FunctionComponent} */Consumer: function Consumer(props, contextValue) {\n      // return props.children(\n      // \tcontext[contextId] ? context[contextId].props.value : defaultValue\n      // );\n      return props.children(contextValue);\n    },\n    /** @type {FunctionComponent} */Provider: function Provider(props) {\n      if (!this.getChildContext) {\n        /** @type {Component[] | null} */\n        var subs = [];\n        var ctx = {};\n        ctx[contextId] = this;\n        this.getChildContext = function () {\n          return ctx;\n        };\n        this.componentWillUnmount = function () {\n          subs = null;\n        };\n        this.shouldComponentUpdate = function (_props) {\n          if (this.props.value !== _props.value) {\n            subs.some(function (c) {\n              c.__e = true;\n              enqueueRender(c);\n            });\n          }\n        };\n        this.sub = function (c) {\n          subs.push(c);\n          var old = c.componentWillUnmount;\n          c.componentWillUnmount = function () {\n            if (subs) {\n              subs.splice(subs.indexOf(c), 1);\n            }\n            if (old) old.call(c);\n          };\n        };\n      }\n      return props.children;\n    }\n  };\n\n  // Devtools needs access to the context object when it\n  // encounters a Provider. This is necessary to support\n  // setting `displayName` on the context object instead\n  // of on the component itself. See:\n  // https://reactjs.org/docs/context.html#contextdisplayname\n\n  return context.Provider.__ = context.Consumer.contextType = context;\n}\n\nexport { BaseComponent as Component, Fragment, cloneElement, createContext, createElement, createRef, createElement as h, hydrate, isValidElement, options, render, toChildArray };\n//# sourceMappingURL=preact.module.js.map\n", "import { options as options$1 } from 'preact';\n\n/** @type {number} */\nvar currentIndex;\n\n/** @type {import('./internal').Component} */\nvar currentComponent;\n\n/** @type {import('./internal').Component} */\nvar previousComponent;\n\n/** @type {number} */\nvar currentHook = 0;\n\n/** @type {Array<import('./internal').Component>} */\nvar afterPaintEffects = [];\n\n// Cast to use internal Options type\nvar options = /** @type {import('./internal').Options} */options$1;\nvar oldBeforeDiff = options.__b;\nvar oldBeforeRender = options.__r;\nvar oldAfterDiff = options.diffed;\nvar oldCommit = options.__c;\nvar oldBeforeUnmount = options.unmount;\nvar oldRoot = options.__;\nvar RAF_TIMEOUT = 100;\nvar prevRaf;\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.__b = function (vnode) {\n  currentComponent = null;\n  if (oldBeforeDiff) oldBeforeDiff(vnode);\n};\noptions.__ = function (vnode, parentDom) {\n  if (vnode && parentDom.__k && parentDom.__k.__m) {\n    vnode.__m = parentDom.__k.__m;\n  }\n  if (oldRoot) oldRoot(vnode, parentDom);\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.__r = function (vnode) {\n  if (oldBeforeRender) oldBeforeRender(vnode);\n  currentComponent = vnode.__c;\n  currentIndex = 0;\n  var hooks = currentComponent.__H;\n  if (hooks) {\n    if (previousComponent === currentComponent) {\n      hooks.__h = [];\n      currentComponent.__h = [];\n      hooks.__.forEach(function (hookItem) {\n        if (hookItem.__N) {\n          hookItem.__ = hookItem.__N;\n        }\n        hookItem._pendingArgs = hookItem.__N = undefined;\n      });\n    } else {\n      hooks.__h.forEach(invokeCleanup);\n      hooks.__h.forEach(invokeEffect);\n      hooks.__h = [];\n      currentIndex = 0;\n    }\n  }\n  previousComponent = currentComponent;\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.diffed = function (vnode) {\n  if (oldAfterDiff) oldAfterDiff(vnode);\n  var c = vnode.__c;\n  if (c && c.__H) {\n    if (c.__H.__h.length) afterPaint(afterPaintEffects.push(c));\n    c.__H.__.forEach(function (hookItem) {\n      if (hookItem._pendingArgs) {\n        hookItem.__H = hookItem._pendingArgs;\n      }\n      hookItem._pendingArgs = undefined;\n    });\n  }\n  previousComponent = currentComponent = null;\n};\n\n// TODO: Improve typing of commitQueue parameter\n/** @type {(vnode: import('./internal').VNode, commitQueue: any) => void} */\noptions.__c = function (vnode, commitQueue) {\n  commitQueue.some(function (component) {\n    try {\n      component.__h.forEach(invokeCleanup);\n      component.__h = component.__h.filter(function (cb) {\n        return cb.__ ? invokeEffect(cb) : true;\n      });\n    } catch (e) {\n      commitQueue.some(function (c) {\n        if (c.__h) c.__h = [];\n      });\n      commitQueue = [];\n      options.__e(e, component.__v);\n    }\n  });\n  if (oldCommit) oldCommit(vnode, commitQueue);\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.unmount = function (vnode) {\n  if (oldBeforeUnmount) oldBeforeUnmount(vnode);\n  var c = vnode.__c;\n  if (c && c.__H) {\n    var hasErrored;\n    c.__H.__.forEach(function (s) {\n      try {\n        invokeCleanup(s);\n      } catch (e) {\n        hasErrored = e;\n      }\n    });\n    c.__H = undefined;\n    if (hasErrored) options.__e(hasErrored, c.__v);\n  }\n};\n\n/**\n * Get a hook's state from the currentComponent\n * @param {number} index The index of the hook to get\n * @param {number} type The index of the hook to get\n * @returns {any}\n */\nfunction getHookState(index, type) {\n  if (options.__h) {\n    options.__h(currentComponent, index, currentHook || type);\n  }\n  currentHook = 0;\n\n  // Largely inspired by:\n  // * https://github.com/michael-klein/funcy.js/blob/f6be73468e6ec46b0ff5aa3cc4c9baf72a29025a/src/hooks/core_hooks.mjs\n  // * https://github.com/michael-klein/funcy.js/blob/650beaa58c43c33a74820a3c98b3c7079cf2e333/src/renderer.mjs\n  // Other implementations to look at:\n  // * https://codesandbox.io/s/mnox05qp8\n  var hooks = currentComponent.__H || (currentComponent.__H = {\n    __: [],\n    __h: []\n  });\n  if (index >= hooks.__.length) {\n    hooks.__.push({});\n  }\n  return hooks.__[index];\n}\n\n/**\n * @template {unknown} S\n * @param {import('./index').Dispatch<import('./index').StateUpdater<S>>} [initialState]\n * @returns {[S, (state: S) => void]}\n */\nfunction useState(initialState) {\n  currentHook = 1;\n  return useReducer(invokeOrReturn, initialState);\n}\n\n/**\n * @template {unknown} S\n * @template {unknown} A\n * @param {import('./index').Reducer<S, A>} reducer\n * @param {import('./index').Dispatch<import('./index').StateUpdater<S>>} initialState\n * @param {(initialState: any) => void} [init]\n * @returns {[ S, (state: S) => void ]}\n */\nfunction useReducer(reducer, initialState, init) {\n  /** @type {import('./internal').ReducerHookState} */\n  var hookState = getHookState(currentIndex++, 2);\n  hookState._reducer = reducer;\n  if (!hookState.__c) {\n    hookState.__ = [!init ? invokeOrReturn(undefined, initialState) : init(initialState), function (action) {\n      var currentValue = hookState.__N ? hookState.__N[0] : hookState.__[0];\n      var nextValue = hookState._reducer(currentValue, action);\n      if (currentValue !== nextValue) {\n        hookState.__N = [nextValue, hookState.__[1]];\n        hookState.__c.setState({});\n      }\n    }];\n    hookState.__c = currentComponent;\n    if (!currentComponent._hasScuFromHooks) {\n      // This SCU has the purpose of bailing out after repeated updates\n      // to stateful hooks.\n      // we store the next value in _nextValue[0] and keep doing that for all\n      // state setters, if we have next states and\n      // all next states within a component end up being equal to their original state\n      // we are safe to bail out for this specific component.\n      /**\n       *\n       * @type {import('./internal').Component[\"shouldComponentUpdate\"]}\n       */\n      // @ts-ignore - We don't use TS to downtranspile\n      // eslint-disable-next-line no-inner-declarations\n      var updateHookState = function updateHookState(p, s, c) {\n        if (!hookState.__c.__H) return true;\n\n        /** @type {(x: import('./internal').HookState) => x is import('./internal').ReducerHookState} */\n        var isStateHook = function isStateHook(x) {\n          return !!x.__c;\n        };\n        var stateHooks = hookState.__c.__H.__.filter(isStateHook);\n        var allHooksEmpty = stateHooks.every(function (x) {\n          return !x.__N;\n        });\n        // When we have no updated hooks in the component we invoke the previous SCU or\n        // traverse the VDOM tree further.\n        if (allHooksEmpty) {\n          return prevScu ? prevScu.call(this, p, s, c) : true;\n        }\n\n        // We check whether we have components with a nextValue set that\n        // have values that aren't equal to one another this pushes\n        // us to update further down the tree\n        var shouldUpdate = false;\n        stateHooks.forEach(function (hookItem) {\n          if (hookItem.__N) {\n            var currentValue = hookItem.__[0];\n            hookItem.__ = hookItem.__N;\n            hookItem.__N = undefined;\n            if (currentValue !== hookItem.__[0]) shouldUpdate = true;\n          }\n        });\n        return shouldUpdate || hookState.__c.props !== p ? prevScu ? prevScu.call(this, p, s, c) : true : false;\n      };\n      currentComponent._hasScuFromHooks = true;\n      var prevScu = currentComponent.shouldComponentUpdate;\n      var prevCWU = currentComponent.componentWillUpdate;\n\n      // If we're dealing with a forced update `shouldComponentUpdate` will\n      // not be called. But we use that to update the hook values, so we\n      // need to call it.\n      currentComponent.componentWillUpdate = function (p, s, c) {\n        if (this.__e) {\n          var tmp = prevScu;\n          // Clear to avoid other sCU hooks from being called\n          prevScu = undefined;\n          updateHookState(p, s, c);\n          prevScu = tmp;\n        }\n        if (prevCWU) prevCWU.call(this, p, s, c);\n      };\n      currentComponent.shouldComponentUpdate = updateHookState;\n    }\n  }\n  return hookState.__N || hookState.__;\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {unknown[]} args\n * @returns {void}\n */\nfunction useEffect(callback, args) {\n  /** @type {import('./internal').EffectHookState} */\n  var state = getHookState(currentIndex++, 3);\n  if (!options.__s && argsChanged(state.__H, args)) {\n    state.__ = callback;\n    state._pendingArgs = args;\n    currentComponent.__H.__h.push(state);\n  }\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {unknown[]} args\n * @returns {void}\n */\nfunction useLayoutEffect(callback, args) {\n  /** @type {import('./internal').EffectHookState} */\n  var state = getHookState(currentIndex++, 4);\n  if (!options.__s && argsChanged(state.__H, args)) {\n    state.__ = callback;\n    state._pendingArgs = args;\n    currentComponent.__h.push(state);\n  }\n}\n\n/** @type {(initialValue: unknown) => unknown} */\nfunction useRef(initialValue) {\n  currentHook = 5;\n  return useMemo(function () {\n    return {\n      current: initialValue\n    };\n  }, []);\n}\n\n/**\n * @param {object} ref\n * @param {() => object} createHandle\n * @param {unknown[]} args\n * @returns {void}\n */\nfunction useImperativeHandle(ref, createHandle, args) {\n  currentHook = 6;\n  useLayoutEffect(function () {\n    if (typeof ref == 'function') {\n      ref(createHandle());\n      return function () {\n        return ref(null);\n      };\n    } else if (ref) {\n      ref.current = createHandle();\n      return function () {\n        return ref.current = null;\n      };\n    }\n  }, args == null ? args : args.concat(ref));\n}\n\n/**\n * @template {unknown} T\n * @param {() => T} factory\n * @param {unknown[]} args\n * @returns {T}\n */\nfunction useMemo(factory, args) {\n  /** @type {import('./internal').MemoHookState<T>} */\n  var state = getHookState(currentIndex++, 7);\n  if (argsChanged(state.__H, args)) {\n    state.__ = factory();\n    state.__H = args;\n    state.__h = factory;\n  }\n  return state.__;\n}\n\n/**\n * @param {() => void} callback\n * @param {unknown[]} args\n * @returns {() => void}\n */\nfunction useCallback(callback, args) {\n  currentHook = 8;\n  return useMemo(function () {\n    return callback;\n  }, args);\n}\n\n/**\n * @param {import('./internal').PreactContext} context\n */\nfunction useContext(context) {\n  var provider = currentComponent.context[context.__c];\n  // We could skip this call here, but than we'd not call\n  // `options._hook`. We need to do that in order to make\n  // the devtools aware of this hook.\n  /** @type {import('./internal').ContextHookState} */\n  var state = getHookState(currentIndex++, 9);\n  // The devtools needs access to the context object to\n  // be able to pull of the default value when no provider\n  // is present in the tree.\n  state.c = context;\n  if (!provider) return context.__;\n  // This is probably not safe to convert to \"!\"\n  if (state.__ == null) {\n    state.__ = true;\n    provider.sub(currentComponent);\n  }\n  return provider.props.value;\n}\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, cb?: (value: T) => string | number) => void}\n */\nfunction useDebugValue(value, formatter) {\n  if (options.useDebugValue) {\n    options.useDebugValue(formatter ? formatter(value) : ( /** @type {any}*/value));\n  }\n}\n\n/**\n * @param {(error: unknown, errorInfo: import('preact').ErrorInfo) => void} cb\n * @returns {[unknown, () => void]}\n */\nfunction useErrorBoundary(cb) {\n  /** @type {import('./internal').ErrorBoundaryHookState} */\n  var state = getHookState(currentIndex++, 10);\n  var errState = useState();\n  state.__ = cb;\n  if (!currentComponent.componentDidCatch) {\n    currentComponent.componentDidCatch = function (err, errorInfo) {\n      if (state.__) state.__(err, errorInfo);\n      errState[1](err);\n    };\n  }\n  return [errState[0], function () {\n    errState[1](undefined);\n  }];\n}\n\n/** @type {() => string} */\nfunction useId() {\n  /** @type {import('./internal').IdHookState} */\n  var state = getHookState(currentIndex++, 11);\n  if (!state.__) {\n    // Grab either the root node or the nearest async boundary node.\n    /** @type {import('./internal.d').VNode} */\n    var root = currentComponent.__v;\n    while (root !== null && !root.__m && root.__ !== null) {\n      root = root.__;\n    }\n    var mask = root.__m || (root.__m = [0, 0]);\n    state.__ = 'P' + mask[0] + '-' + mask[1]++;\n  }\n  return state.__;\n}\n\n/**\n * After paint effects consumer.\n */\nfunction flushAfterPaintEffects() {\n  var component;\n  while (component = afterPaintEffects.shift()) {\n    if (!component.__P || !component.__H) continue;\n    try {\n      component.__H.__h.forEach(invokeCleanup);\n      component.__H.__h.forEach(invokeEffect);\n      component.__H.__h = [];\n    } catch (e) {\n      component.__H.__h = [];\n      options.__e(e, component.__v);\n    }\n  }\n}\nvar HAS_RAF = typeof requestAnimationFrame == 'function';\n\n/**\n * Schedule a callback to be invoked after the browser has a chance to paint a new frame.\n * Do this by combining requestAnimationFrame (rAF) + setTimeout to invoke a callback after\n * the next browser frame.\n *\n * Also, schedule a timeout in parallel to the the rAF to ensure the callback is invoked\n * even if RAF doesn't fire (for example if the browser tab is not visible)\n *\n * @param {() => void} callback\n */\nfunction afterNextFrame(callback) {\n  var done = function done() {\n    clearTimeout(timeout);\n    if (HAS_RAF) cancelAnimationFrame(raf);\n    setTimeout(callback);\n  };\n  var timeout = setTimeout(done, RAF_TIMEOUT);\n  var raf;\n  if (HAS_RAF) {\n    raf = requestAnimationFrame(done);\n  }\n}\n\n// Note: if someone used options.debounceRendering = requestAnimationFrame,\n// then effects will ALWAYS run on the NEXT frame instead of the current one, incurring a ~16ms delay.\n// Perhaps this is not such a big deal.\n/**\n * Schedule afterPaintEffects flush after the browser paints\n * @param {number} newQueueLength\n * @returns {void}\n */\nfunction afterPaint(newQueueLength) {\n  if (newQueueLength === 1 || prevRaf !== options.requestAnimationFrame) {\n    prevRaf = options.requestAnimationFrame;\n    (prevRaf || afterNextFrame)(flushAfterPaintEffects);\n  }\n}\n\n/**\n * @param {import('./internal').HookState} hook\n * @returns {void}\n */\nfunction invokeCleanup(hook) {\n  // A hook cleanup can introduce a call to render which creates a new root, this will call options.vnode\n  // and move the currentComponent away.\n  var comp = currentComponent;\n  var cleanup = hook.__c;\n  if (typeof cleanup == 'function') {\n    hook.__c = undefined;\n    cleanup();\n  }\n  currentComponent = comp;\n}\n\n/**\n * Invoke a Hook's effect\n * @param {import('./internal').EffectHookState} hook\n * @returns {void}\n */\nfunction invokeEffect(hook) {\n  // A hook call can introduce a call to render which creates a new root, this will call options.vnode\n  // and move the currentComponent away.\n  var comp = currentComponent;\n  hook.__c = hook.__();\n  currentComponent = comp;\n}\n\n/**\n * @param {unknown[]} oldArgs\n * @param {unknown[]} newArgs\n * @returns {boolean}\n */\nfunction argsChanged(oldArgs, newArgs) {\n  return !oldArgs || oldArgs.length !== newArgs.length || newArgs.some(function (arg, index) {\n    return arg !== oldArgs[index];\n  });\n}\n\n/**\n * @template Arg\n * @param {Arg} arg\n * @param {(arg: Arg) => any} f\n * @returns {any}\n */\nfunction invokeOrReturn(arg, f) {\n  return typeof f == 'function' ? f(arg) : f;\n}\n\nexport { useCallback, useContext, useDebugValue, useEffect, useErrorBoundary, useId, useImperativeHandle, useLayoutEffect, useMemo, useReducer, useRef, useState };\n//# sourceMappingURL=hooks.module.js.map\n", "import { useEffect, useState, useCallback, useMemo } from '@lynx-js/react'\nimport {\n  Plus,\n  Trash2,\n  Download,\n  Upload,\n  CheckSquare,\n  Square,\n  Briefcase,\n  Home,\n  ShoppingCart,\n  Heart,\n  BookOpen,\n  DollarSign,\n  Repeat\n} from 'lucide-react'\nimport './App.css'\n\ninterface Subtask {\n  id: string;\n  title: string;\n  completed: boolean;\n  createdAt: Date;\n}\n\ninterface Task {\n  id: string;\n  title: string;\n  description?: string;\n  completed: boolean;\n  dueDate?: Date;\n  priority: 'low' | 'medium' | 'high';\n  category?: string;\n  tags: string[];\n  subtasks: Subtask[];\n  isRecurring?: boolean;\n  recurringType?: 'daily' | 'weekly' | 'monthly';\n  createdAt: Date;\n}\n\nexport function App(props: {\n  onMounted?: () => void\n}) {\n  const [tasks, setTasks] = useState<Task[]>(() => {\n    try {\n      const savedTasks = localStorage.getItem('todoTasks');\n      if (savedTasks) {\n        const parsedTasks = JSON.parse(savedTasks);\n        // Convert date strings back to Date objects\n        return parsedTasks.map((task: any) => ({\n          ...task,\n          createdAt: new Date(task.createdAt),\n          dueDate: task.dueDate ? new Date(task.dueDate) : undefined,\n          subtasks: task.subtasks?.map((subtask: any) => ({\n            ...subtask,\n            createdAt: new Date(subtask.createdAt)\n          })) || []\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading tasks from localStorage:', error);\n    }\n    return [];\n  });\n  const [newTaskTitle, setNewTaskTitle] = useState('');\n  const [selectedPriority, setSelectedPriority] = useState<'low' | 'medium' | 'high'>('medium');\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [isRecurring, setIsRecurring] = useState<boolean>(false);\n  const [recurringType, setRecurringType] = useState<'daily' | 'weekly' | 'monthly'>('daily');\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterPriority, setFilterPriority] = useState<string>('');\n  const [filterTag, setFilterTag] = useState<string>('');\n  const [searchQuery, setSearchQuery] = useState<string>('');\n  const [sortBy, setSortBy] = useState<'created' | 'priority' | 'category'>('created');\n\n  const categories = [\n    { id: 'work', name: 'Work', icon: Briefcase, color: '#3B82F6' },\n    { id: 'personal', name: 'Personal', icon: Home, color: '#10B981' },\n    { id: 'shopping', name: 'Shopping', icon: ShoppingCart, color: '#F59E0B' },\n    { id: 'health', name: 'Health', icon: Heart, color: '#EF4444' },\n    { id: 'learning', name: 'Learning', icon: BookOpen, color: '#8B5CF6' },\n    { id: 'finance', name: 'Finance', icon: DollarSign, color: '#06B6D4' }\n  ];\n\n  const availableTags = [\n    { id: 'urgent', name: 'Urgent', color: '#DC2626' },\n    { id: 'important', name: 'Important', color: '#7C3AED' },\n    { id: 'quick', name: 'Quick Task', color: '#059669' },\n    { id: 'meeting', name: 'Meeting', color: '#2563EB' },\n    { id: 'deadline', name: 'Deadline', color: '#EA580C' },\n    { id: 'research', name: 'Research', color: '#0891B2' },\n    { id: 'creative', name: 'Creative', color: '#C026D3' },\n    { id: 'routine', name: 'Routine', color: '#65A30D' }\n  ];\n\n  useEffect(() => {\n    console.info('Hello, Todo App with ReactLynx!')\n    props.onMounted?.()\n  }, [])\n\n  // Save tasks to localStorage whenever tasks change\n  useEffect(() => {\n    try {\n      localStorage.setItem('todoTasks', JSON.stringify(tasks));\n    } catch (error) {\n      console.error('Error saving tasks to localStorage:', error);\n    }\n  }, [tasks]);\n\n  const addTask = useCallback(() => {\n    if (newTaskTitle.trim()) {\n      const newTask: Task = {\n        id: Date.now().toString(),\n        title: newTaskTitle.trim(),\n        completed: false,\n        priority: selectedPriority,\n        category: selectedCategory || undefined,\n        tags: selectedTags,\n        subtasks: [],\n        isRecurring: isRecurring,\n        recurringType: isRecurring ? recurringType : undefined,\n        createdAt: new Date()\n      };\n      setTasks(prev => [...prev, newTask]);\n      setNewTaskTitle('');\n      setSelectedPriority('medium');\n      setSelectedCategory('');\n      setSelectedTags([]);\n      setIsRecurring(false);\n      setRecurringType('daily');\n    }\n  }, [newTaskTitle, selectedPriority, selectedCategory, selectedTags]);\n\n  const toggleTask = useCallback((id: string) => {\n    setTasks(prev => prev.map(task => {\n      if (task.id === id) {\n        const updatedTask = { ...task, completed: !task.completed };\n\n        // If completing a recurring task, create a new instance\n        if (!task.completed && task.isRecurring && task.recurringType) {\n          const nextDate = new Date();\n          switch (task.recurringType) {\n            case 'daily':\n              nextDate.setDate(nextDate.getDate() + 1);\n              break;\n            case 'weekly':\n              nextDate.setDate(nextDate.getDate() + 7);\n              break;\n            case 'monthly':\n              nextDate.setMonth(nextDate.getMonth() + 1);\n              break;\n          }\n\n          // Create new recurring task instance\n          const newRecurringTask: Task = {\n            ...task,\n            id: Date.now().toString(),\n            completed: false,\n            subtasks: task.subtasks.map(subtask => ({\n              ...subtask,\n              id: Date.now().toString() + Math.random(),\n              completed: false\n            })),\n            createdAt: nextDate\n          };\n\n          // Add the new task to the list\n          setTimeout(() => {\n            setTasks(current => [...current, newRecurringTask]);\n          }, 100);\n        }\n\n        return updatedTask;\n      }\n      return task;\n    }));\n  }, []);\n\n  const deleteTask = useCallback((id: string) => {\n    setTasks(prev => prev.filter(task => task.id !== id));\n  }, []);\n\n  const addSubtask = useCallback((taskId: string, subtaskTitle: string) => {\n    if (subtaskTitle.trim()) {\n      setTasks(prev => prev.map(task =>\n        task.id === taskId\n          ? {\n              ...task,\n              subtasks: [...task.subtasks, {\n                id: Date.now().toString(),\n                title: subtaskTitle.trim(),\n                completed: false,\n                createdAt: new Date()\n              }]\n            }\n          : task\n      ));\n    }\n  }, []);\n\n  const toggleSubtask = useCallback((taskId: string, subtaskId: string) => {\n    setTasks(prev => prev.map(task =>\n      task.id === taskId\n        ? {\n            ...task,\n            subtasks: task.subtasks.map(subtask =>\n              subtask.id === subtaskId\n                ? { ...subtask, completed: !subtask.completed }\n                : subtask\n            )\n          }\n        : task\n    ));\n  }, []);\n\n  const deleteSubtask = useCallback((taskId: string, subtaskId: string) => {\n    setTasks(prev => prev.map(task =>\n      task.id === taskId\n        ? {\n            ...task,\n            subtasks: task.subtasks.filter(subtask => subtask.id !== subtaskId)\n          }\n        : task\n    ));\n  }, []);\n\n  const exportData = useCallback(() => {\n    try {\n      const dataToExport = {\n        tasks,\n        exportDate: new Date().toISOString(),\n        version: '1.0'\n      };\n      const dataStr = JSON.stringify(dataToExport, null, 2);\n      const dataBlob = new Blob([dataStr], { type: 'application/json' });\n      const url = URL.createObjectURL(dataBlob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `todo-backup-${new Date().toISOString().split('T')[0]}.json`;\n      link.click();\n      URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error exporting data:', error);\n    }\n  }, [tasks]);\n\n  const importData = useCallback((event: any) => {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const importedData = JSON.parse(e.target?.result as string);\n          if (importedData.tasks && Array.isArray(importedData.tasks)) {\n            const importedTasks = importedData.tasks.map((task: any) => ({\n              ...task,\n              createdAt: new Date(task.createdAt),\n              dueDate: task.dueDate ? new Date(task.dueDate) : undefined,\n              subtasks: task.subtasks?.map((subtask: any) => ({\n                ...subtask,\n                createdAt: new Date(subtask.createdAt)\n              })) || []\n            }));\n            setTasks(importedTasks);\n          }\n        } catch (error) {\n          console.error('Error importing data:', error);\n        }\n      };\n      reader.readAsText(file);\n    }\n  }, []);\n\n  const clearAllData = useCallback(() => {\n    if (confirm('Are you sure you want to clear all tasks? This action cannot be undone.')) {\n      setTasks([]);\n      localStorage.removeItem('todoTasks');\n    }\n  }, []);\n\n  // Filter and sort tasks\n  const filteredTasks = useMemo(() => {\n    let filtered = [...tasks];\n\n    // Apply category filter\n    if (filterCategory) {\n      filtered = filtered.filter(task => task.category === filterCategory);\n    }\n\n    // Apply priority filter\n    if (filterPriority) {\n      filtered = filtered.filter(task => task.priority === filterPriority);\n    }\n\n    // Apply tag filter\n    if (filterTag) {\n      filtered = filtered.filter(task => task.tags.includes(filterTag));\n    }\n\n    // Apply search filter\n    if (searchQuery.trim()) {\n      const query = searchQuery.toLowerCase().trim();\n      filtered = filtered.filter(task =>\n        task.title.toLowerCase().includes(query) ||\n        (task.description && task.description.toLowerCase().includes(query)) ||\n        task.tags.some(tagId => {\n          const tag = availableTags.find(t => t.id === tagId);\n          return tag && tag.name.toLowerCase().includes(query);\n        }) ||\n        (task.category && categories.find(c => c.id === task.category)?.name.toLowerCase().includes(query))\n      );\n    }\n\n    // Sort tasks\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case 'priority':\n          const priorityOrder = { high: 3, medium: 2, low: 1 };\n          return priorityOrder[b.priority] - priorityOrder[a.priority];\n        case 'category':\n          const aCat = a.category || 'zzz';\n          const bCat = b.category || 'zzz';\n          return aCat.localeCompare(bCat);\n        case 'created':\n        default:\n          return b.createdAt.getTime() - a.createdAt.getTime();\n      }\n    });\n\n    return filtered;\n  }, [tasks, filterCategory, filterPriority, filterTag, searchQuery, sortBy, availableTags, categories]);\n\n  const pendingTasks = filteredTasks.filter(task => !task.completed);\n  const completedTasks = filteredTasks.filter(task => task.completed);\n\n  // Get task statistics by category\n  const categoryStats = useMemo(() => {\n    return categories.map(category => {\n      const categoryTasks = tasks.filter(task => task.category === category.id);\n      return {\n        ...category,\n        total: categoryTasks.length,\n        completed: categoryTasks.filter(task => task.completed).length,\n        pending: categoryTasks.filter(task => !task.completed).length\n      };\n    });\n  }, [tasks, categories]);\n\n  return (\n    <view className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <view className=\"max-w-4xl mx-auto px-2 sm:px-4 lg:px-0\">\n        {/* Header */}\n        <view className=\"bg-white shadow-sm border-b border-gray-200 px-3 sm:px-6 py-4\">\n          <view className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-4\">\n            <text className=\"text-xl sm:text-2xl font-bold text-gray-900\">My Todo List</text>\n\n            {/* Data Management */}\n            <view className=\"flex gap-1 sm:gap-2 flex-wrap\">\n              <view\n                className=\"flex items-center gap-1 px-2 sm:px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white rounded-lg cursor-pointer transition-colors\"\n                bindtap={exportData}\n              >\n                <Download size={16} />\n                <text className=\"text-white text-xs sm:text-sm font-medium hidden sm:inline\">Export</text>\n              </view>\n\n              <view\n                className=\"flex items-center gap-1 px-2 sm:px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white rounded-lg cursor-pointer transition-colors\"\n                bindtap={() => {\n                  const input = document.createElement('input');\n                  input.type = 'file';\n                  input.accept = '.json';\n                  input.onchange = importData;\n                  input.click();\n                }}\n              >\n                <Upload size={16} />\n                <text className=\"text-white text-xs sm:text-sm font-medium hidden sm:inline\">Import</text>\n              </view>\n\n              <view\n                className=\"flex items-center gap-1 px-2 sm:px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white rounded-lg cursor-pointer transition-colors\"\n                bindtap={clearAllData}\n              >\n                <Trash2 size={16} />\n                <text className=\"text-white text-xs sm:text-sm font-medium hidden sm:inline\">Clear</text>\n              </view>\n            </view>\n          </view>\n\n          <view className=\"grid grid-cols-3 gap-2 sm:gap-4 sm:flex sm:flex-wrap\">\n            <view className=\"flex flex-col items-center bg-blue-50 rounded-lg px-2 sm:px-4 py-2 min-w-[60px] sm:min-w-[80px]\">\n              <text className=\"text-lg sm:text-xl font-bold text-blue-600\">{tasks.length}</text>\n              <text className=\"text-xs sm:text-sm text-blue-600 opacity-70\">Total</text>\n            </view>\n            <view className=\"flex flex-col items-center bg-yellow-50 rounded-lg px-2 sm:px-4 py-2 min-w-[60px] sm:min-w-[80px]\">\n              <text className=\"text-lg sm:text-xl font-bold text-yellow-600\">{pendingTasks.length}</text>\n              <text className=\"text-xs sm:text-sm text-yellow-600 opacity-70\">Pending</text>\n            </view>\n            <view className=\"flex flex-col items-center bg-green-50 rounded-lg px-2 sm:px-4 py-2 min-w-[60px] sm:min-w-[80px]\">\n              <text className=\"text-lg sm:text-xl font-bold text-green-600\">{completedTasks.length}</text>\n              <text className=\"text-xs sm:text-sm text-green-600 opacity-70\">Completed</text>\n            </view>\n          </view>\n        </view>\n\n        {/* Filter and Sort Section */}\n        <view className=\"bg-gray-50 border-b border-gray-200 px-3 sm:px-6 py-4\">\n          <text className=\"text-lg font-semibold text-gray-900 mb-4\">Search & Filter</text>\n\n          {/* Search Bar */}\n          <view className=\"mb-4\">\n            <text className=\"block text-sm font-medium text-gray-700 mb-2\">Search Tasks:</text>\n            <view className=\"flex gap-2 items-center mb-2\">\n              <view className=\"flex-1 px-3 py-2 border-2 border-gray-300 rounded-lg bg-white min-h-[40px] flex items-center\">\n                <text className=\"text-sm text-gray-500\">{searchQuery || 'Type to search tasks, tags, categories...'}</text>\n              </view>\n              <view\n                className=\"px-4 py-2 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-lg cursor-pointer transition-colors\"\n                bindtap={() => setSearchQuery('')}\n              >\n                <text className=\"text-sm font-medium text-gray-700\">Clear</text>\n              </view>\n            </view>\n\n            {/* Quick Search Suggestions */}\n            <view className=\"flex gap-2 flex-wrap\">\n              {['urgent', 'work', 'today', 'meeting', 'important'].map(suggestion => (\n                <view\n                  key={suggestion}\n                  className=\"px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded-full cursor-pointer transition-colors\"\n                  bindtap={() => setSearchQuery(suggestion)}\n                >\n                  <text className=\"text-xs text-gray-600 font-medium\">{suggestion}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Category Filter */}\n          <view className=\"mb-4\">\n            <text className=\"block text-sm font-medium text-gray-700 mb-2\">Filter by Category:</text>\n            <view className=\"flex gap-2 flex-wrap\">\n              <view\n                className={`flex items-center gap-1 px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                  !filterCategory\n                    ? 'border-blue-500 bg-blue-500 text-white'\n                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                }`}\n                bindtap={() => setFilterCategory('')}\n              >\n                <text className=\"text-sm font-medium\">All</text>\n              </view>\n              {categories.map(category => (\n                <view\n                  key={category.id}\n                  className={`flex items-center gap-1 px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                    filterCategory === category.id\n                      ? 'border-blue-500 bg-blue-500 text-white'\n                      : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                  bindtap={() => setFilterCategory(category.id)}\n                >\n                  <category.icon size={16} />\n                  <text className=\"text-sm font-medium\">{category.name}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Priority Filter */}\n          <view className=\"mb-4\">\n            <text className=\"block text-sm font-medium text-gray-700 mb-2\">Filter by Priority:</text>\n            <view className=\"flex gap-2 flex-wrap\">\n              <view\n                className={`px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                  !filterPriority\n                    ? 'border-blue-500 bg-blue-500 text-white'\n                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                }`}\n                bindtap={() => setFilterPriority('')}\n              >\n                <text className=\"text-sm font-medium\">All</text>\n              </view>\n              {(['high', 'medium', 'low'] as const).map(priority => (\n                <view\n                  key={priority}\n                  className={`px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                    filterPriority === priority\n                      ? 'border-blue-500 bg-blue-500 text-white'\n                      : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                  bindtap={() => setFilterPriority(priority)}\n                >\n                  <text className=\"text-sm font-medium\">{priority.toUpperCase()}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Tag Filter */}\n          <view className=\"mb-4\">\n            <text className=\"block text-sm font-medium text-gray-700 mb-2\">Filter by Tag:</text>\n            <view className=\"flex gap-2 flex-wrap\">\n              <view\n                className={`px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                  !filterTag\n                    ? 'border-blue-500 bg-blue-500 text-white'\n                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                }`}\n                bindtap={() => setFilterTag('')}\n              >\n                <text className=\"text-sm font-medium\">All</text>\n              </view>\n              {availableTags.map(tag => (\n                <view\n                  key={tag.id}\n                  className={`px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                    filterTag === tag.id\n                      ? 'border-blue-500 bg-blue-500 text-white'\n                      : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                  style={{ borderColor: filterTag === tag.id ? '#3b82f6' : tag.color }}\n                  bindtap={() => setFilterTag(tag.id)}\n                >\n                  <text\n                    className=\"text-sm font-medium\"\n                    style={{ color: filterTag === tag.id ? 'white' : tag.color }}\n                  >\n                    {tag.name}\n                  </text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Sort Options */}\n          <view className=\"mb-4\">\n            <text className=\"block text-sm font-medium text-gray-700 mb-2\">Sort by:</text>\n            <view className=\"flex gap-2 flex-wrap\">\n              {[\n                { id: 'created', label: 'Created Date' },\n                { id: 'priority', label: 'Priority' },\n                { id: 'category', label: 'Category' }\n              ].map(option => (\n                <view\n                  key={option.id}\n                  className={`px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                    sortBy === option.id\n                      ? 'border-blue-500 bg-blue-500 text-white'\n                      : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                  bindtap={() => setSortBy(option.id as 'created' | 'priority' | 'category')}\n                >\n                  <text className=\"text-sm font-medium\">{option.label}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Filter Status and Clear */}\n          <view className=\"mt-4 pt-4 border-t border-gray-200\">\n            {(searchQuery || filterCategory || filterPriority || filterTag) && (\n              <view className=\"mb-3\">\n                <text className=\"block text-sm font-medium text-gray-700 mb-2\">Active Filters:</text>\n                <view className=\"flex gap-2 flex-wrap mb-2\">\n                  {searchQuery && (\n                    <view className=\"flex items-center gap-1 px-2 py-1 bg-blue-500 text-white rounded-full text-xs\">\n                      <text className=\"text-white font-medium\">Search: \"{searchQuery}\"</text>\n                      <text className=\"text-white font-bold cursor-pointer px-1\" bindtap={() => setSearchQuery('')}>×</text>\n                    </view>\n                  )}\n                  {filterCategory && (\n                    <view className=\"flex items-center gap-1 px-2 py-1 bg-blue-500 text-white rounded-full text-xs\">\n                      <text className=\"text-white font-medium\">Category: {categories.find(c => c.id === filterCategory)?.name}</text>\n                      <text className=\"text-white font-bold cursor-pointer px-1\" bindtap={() => setFilterCategory('')}>×</text>\n                    </view>\n                  )}\n                  {filterPriority && (\n                    <view className=\"flex items-center gap-1 px-2 py-1 bg-blue-500 text-white rounded-full text-xs\">\n                      <text className=\"text-white font-medium\">Priority: {filterPriority.toUpperCase()}</text>\n                      <text className=\"text-white font-bold cursor-pointer px-1\" bindtap={() => setFilterPriority('')}>×</text>\n                    </view>\n                  )}\n                  {filterTag && (\n                    <view className=\"flex items-center gap-1 px-2 py-1 bg-blue-500 text-white rounded-full text-xs\">\n                      <text className=\"text-white font-medium\">Tag: {availableTags.find(t => t.id === filterTag)?.name}</text>\n                      <text className=\"text-white font-bold cursor-pointer px-1\" bindtap={() => setFilterTag('')}>×</text>\n                    </view>\n                  )}\n                </view>\n                <view\n                  className=\"inline-block px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded-lg cursor-pointer transition-colors\"\n                  bindtap={() => {\n                    setSearchQuery('');\n                    setFilterCategory('');\n                    setFilterPriority('');\n                    setFilterTag('');\n                  }}\n                >\n                  <text className=\"text-white font-medium text-sm\">Clear All</text>\n                </view>\n              </view>\n            )}\n\n            <view className=\"text-center py-2\">\n              <text className=\"text-sm text-gray-500 font-medium\">\n                Showing {pendingTasks.length + completedTasks.length} of {tasks.length} tasks\n              </text>\n            </view>\n          </view>\n\n          {/* Category Statistics */}\n          <view className=\"mt-4\">\n            <text className=\"block text-sm font-medium text-gray-700 mb-2\">Category Overview:</text>\n            <view className=\"flex gap-3 flex-wrap\">\n              {categoryStats.map(stat => (\n                <view\n                  key={stat.id}\n                  className=\"flex flex-col items-center p-3 border-2 rounded-lg bg-white min-w-[80px] hover:shadow-md transition-shadow\"\n                  style={{ borderColor: stat.color }}\n                >\n                  <stat.icon size={20} />\n                  <text className=\"text-xs font-medium text-gray-700 mb-1\">{stat.name}</text>\n                  <text className=\"text-sm font-bold text-gray-600\">{stat.pending}/{stat.total}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n        </view>\n\n        {/* Add Task Section */}\n        <view className=\"bg-white px-3 sm:px-6 py-4 border-b border-gray-200\">\n          <text className=\"text-lg font-semibold text-gray-900 mb-4\">Add New Task</text>\n\n          {/* Quick Templates */}\n          <view className=\"flex gap-2 flex-wrap mb-4\">\n            {[\n              'Buy groceries',\n              'Call dentist',\n              'Review project',\n              'Exercise',\n              'Read book'\n            ].map(template => (\n              <view\n                key={template}\n                className=\"px-3 py-1.5 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-lg cursor-pointer transition-colors\"\n                bindtap={() => setNewTaskTitle(template)}\n              >\n                <text className=\"text-sm text-gray-700\">{template}</text>\n              </view>\n            ))}\n          </view>\n\n          {/* Priority Selection */}\n          <view className=\"mb-4\">\n            <text className=\"block text-sm font-medium text-gray-700 mb-2\">Priority</text>\n            <view className=\"flex gap-2 flex-wrap\">\n              {(['low', 'medium', 'high'] as const).map(priority => (\n                <view\n                  key={priority}\n                  className={`px-4 py-2 border-2 rounded-lg cursor-pointer transition-all ${\n                    selectedPriority === priority\n                      ? 'border-blue-500 bg-blue-500 text-white'\n                      : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                  bindtap={() => setSelectedPriority(priority)}\n                >\n                  <text className=\"text-sm font-medium\">{priority.toUpperCase()}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Category Selection */}\n          <view className=\"mb-4\">\n            <text className=\"block text-sm font-medium text-gray-700 mb-2\">Category</text>\n            <view className=\"flex gap-2 flex-wrap\">\n              <view\n                className={`flex items-center gap-1 px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                  !selectedCategory\n                    ? 'border-blue-500 bg-blue-500 text-white'\n                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                }`}\n                bindtap={() => setSelectedCategory('')}\n              >\n                <text className=\"text-sm font-medium\">No Category</text>\n              </view>\n              {categories.map(category => (\n                <view\n                  key={category.id}\n                  className={`flex items-center gap-1 px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                    selectedCategory === category.id\n                      ? 'border-blue-500 bg-blue-500 text-white'\n                      : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                  }`}\n                  bindtap={() => setSelectedCategory(category.id)}\n                >\n                  <category.icon size={16} />\n                  <text className=\"text-sm font-medium\">{category.name}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Tags Selection */}\n          <view className=\"mb-4\">\n            <text className=\"block text-sm font-medium text-gray-700 mb-2\">Tags</text>\n            <view className=\"flex gap-2 flex-wrap\">\n              {availableTags.map(tag => (\n                <view\n                  key={tag.id}\n                  className={`px-3 py-1.5 border-2 rounded-full cursor-pointer transition-all text-xs font-medium ${\n                    selectedTags.includes(tag.id)\n                      ? 'text-white'\n                      : 'bg-white hover:bg-gray-50'\n                  }`}\n                  style={{\n                    backgroundColor: selectedTags.includes(tag.id) ? tag.color : 'transparent',\n                    borderColor: tag.color,\n                    color: selectedTags.includes(tag.id) ? 'white' : tag.color\n                  }}\n                  bindtap={() => {\n                    if (selectedTags.includes(tag.id)) {\n                      setSelectedTags(prev => prev.filter(id => id !== tag.id));\n                    } else {\n                      setSelectedTags(prev => [...prev, tag.id]);\n                    }\n                  }}\n                >\n                  <text\n                    className=\"text-xs font-medium\"\n                    style={{\n                      color: selectedTags.includes(tag.id) ? 'white' : tag.color\n                    }}\n                  >\n                    {tag.name}\n                  </text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Recurring Task Options */}\n          <view className=\"mb-4\">\n            <view className=\"flex items-center gap-2 mb-2\">\n              <view\n                className={`w-4 h-4 border-2 rounded cursor-pointer transition-colors flex items-center justify-center ${\n                  isRecurring\n                    ? 'bg-blue-500 border-blue-500'\n                    : 'border-gray-300 hover:border-blue-500'\n                }`}\n                bindtap={() => setIsRecurring(!isRecurring)}\n              >\n                {isRecurring && <CheckSquare size={12} className=\"text-white\" />}\n              </view>\n              <text className=\"text-sm font-medium text-gray-700\">Make this a recurring task</text>\n            </view>\n\n            {isRecurring && (\n              <view className=\"ml-6\">\n                <text className=\"block text-sm font-medium text-gray-700 mb-2\">Repeat every:</text>\n                <view className=\"flex gap-2\">\n                  {(['daily', 'weekly', 'monthly'] as const).map(type => (\n                    <view\n                      key={type}\n                      className={`px-3 py-1.5 border-2 rounded-lg cursor-pointer transition-all ${\n                        recurringType === type\n                          ? 'border-blue-500 bg-blue-500 text-white'\n                          : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'\n                      }`}\n                      bindtap={() => setRecurringType(type)}\n                    >\n                      <text className=\"text-sm font-medium\">{type.charAt(0).toUpperCase() + type.slice(1)}</text>\n                    </view>\n                  ))}\n                </view>\n              </view>\n            )}\n          </view>\n\n          {/* Add Task Actions */}\n          <view className=\"flex flex-col sm:flex-row gap-3 items-stretch sm:items-center\">\n            <view className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 min-h-[40px] flex items-center\">\n              <text className=\"text-sm text-gray-500\">{newTaskTitle || 'Select a template above or tap here to enter custom task...'}</text>\n            </view>\n            <view\n              className={`px-4 sm:px-6 py-2 rounded-lg font-semibold transition-colors cursor-pointer text-center ${\n                !newTaskTitle.trim()\n                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  : 'bg-blue-500 hover:bg-blue-600 text-white'\n              }`}\n              bindtap={!newTaskTitle.trim() ? undefined : addTask}\n            >\n              <text className={`font-semibold ${!newTaskTitle.trim() ? 'text-gray-500' : 'text-white'}`}>Add Task</text>\n            </view>\n          </view>\n        </view>\n\n        {/* Task List */}\n        <scroll-view className=\"flex-1 px-3 sm:px-6 py-4\">\n          {/* Pending Tasks */}\n          {pendingTasks.length > 0 && (\n            <view className=\"mb-6\">\n              <text className=\"text-lg font-semibold text-gray-900 mb-3\">Pending Tasks ({pendingTasks.length})</text>\n              <view className=\"space-y-2\">\n                {pendingTasks.map(task => {\n                  const category = categories.find(c => c.id === task.category);\n                  const priorityColors = {\n                    high: 'bg-red-500',\n                    medium: 'bg-yellow-500',\n                    low: 'bg-green-500'\n                  };\n\n                  return (\n                    <view key={task.id} className=\"relative flex bg-white border border-gray-200 rounded-lg p-3 sm:p-4 shadow-sm hover:shadow-md transition-shadow\">\n                      {/* Priority indicator */}\n                      <view className={`absolute left-0 top-0 bottom-0 w-1 rounded-l-lg ${priorityColors[task.priority]}`} />\n\n                      <view className=\"flex-1 ml-2\">\n                        <view className=\"flex items-start gap-3 mb-2\">\n                          <view\n                            className=\"w-5 h-5 border-2 border-gray-300 rounded flex items-center justify-center cursor-pointer hover:border-blue-500 transition-colors mt-0.5\"\n                            bindtap={() => toggleTask(task.id)}\n                          >\n                            <Square size={16} className=\"text-gray-400\" />\n                          </view>\n                          <text className=\"flex-1 text-sm sm:text-base font-medium text-gray-900 cursor-pointer\">{task.title}</text>\n                          <view className=\"flex gap-2\">\n                            <view\n                              className=\"p-1 text-red-500 hover:bg-red-50 rounded cursor-pointer transition-colors\"\n                              bindtap={() => deleteTask(task.id)}\n                            >\n                              <Trash2 size={16} />\n                            </view>\n                          </view>\n                        </view>\n\n                        {/* Task metadata */}\n                        <view className=\"flex gap-2 items-center flex-wrap mb-2\">\n                          {/* Category */}\n                          {category && (\n                            <view className=\"flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white\" style={{ backgroundColor: category.color }}>\n                              <category.icon size={12} />\n                              <text className=\"text-xs font-medium text-white\">{category.name}</text>\n                            </view>\n                          )}\n\n                          {/* Priority */}\n                          <view className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            task.priority === 'high' ? 'bg-red-100 text-red-800' :\n                            task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :\n                            'bg-green-100 text-green-800'\n                          }`}>\n                            <text className=\"text-xs font-medium\">{task.priority.toUpperCase()}</text>\n                          </view>\n\n                          {/* Recurring indicator */}\n                          {task.isRecurring && (\n                            <view className=\"flex items-center gap-1 px-2 py-1 bg-purple-100 text-purple-800 rounded-full\">\n                              <Repeat size={12} />\n                              <text className=\"text-xs font-medium\">{task.recurringType}</text>\n                            </view>\n                          )}\n\n                          {/* Created date */}\n                          <view className=\"px-2 py-1 bg-gray-100 rounded-full\">\n                            <text className=\"text-xs text-gray-600\">Created: {task.createdAt.toLocaleDateString()}</text>\n                          </view>\n                        </view>\n\n                        {/* Tags */}\n                        {task.tags.length > 0 && (\n                          <view className=\"flex gap-1 flex-wrap mt-2\">\n                            {task.tags.map(tagId => {\n                              const tag = availableTags.find(t => t.id === tagId);\n                              return tag ? (\n                                <view\n                                  key={tagId}\n                                  className=\"px-2 py-0.5 rounded-full text-xs text-white\"\n                                  style={{ backgroundColor: tag.color }}\n                                >\n                                  <text className=\"text-xs font-medium text-white\">{tag.name}</text>\n                                </view>\n                              ) : null;\n                            })}\n                          </view>\n                        )}\n\n                        {/* Subtasks */}\n                        {task.subtasks.length > 0 && (\n                          <view className=\"mt-3 pl-4 border-l-2 border-gray-200\">\n                            <text className=\"text-sm font-medium text-gray-700 mb-2\">Subtasks ({task.subtasks.filter(s => s.completed).length}/{task.subtasks.length})</text>\n                            <view className=\"space-y-1\">\n                              {task.subtasks.map(subtask => (\n                                <view key={subtask.id} className=\"flex items-center gap-2\">\n                                  <view\n                                    className={`w-4 h-4 border rounded cursor-pointer transition-colors ${\n                                      subtask.completed\n                                        ? 'bg-green-500 border-green-500'\n                                        : 'border-gray-300 hover:border-blue-500'\n                                    }`}\n                                    bindtap={() => toggleSubtask(task.id, subtask.id)}\n                                  >\n                                    {subtask.completed && <CheckSquare size={12} className=\"text-white\" />}\n                                  </view>\n                                  <text className={`flex-1 text-sm ${subtask.completed ? 'line-through text-gray-500' : 'text-gray-700'}`}>\n                                    {subtask.title}\n                                  </text>\n                                  <view\n                                    className=\"text-red-500 hover:bg-red-50 rounded p-1 cursor-pointer\"\n                                    bindtap={() => deleteSubtask(task.id, subtask.id)}\n                                  >\n                                    <Trash2 size={12} />\n                                  </view>\n                                </view>\n                              ))}\n                            </view>\n                          </view>\n                        )}\n\n                        {/* Add Subtask */}\n                        <view className=\"mt-3 pt-2 border-t border-gray-100\">\n                          <view className=\"flex gap-2 items-center\">\n                            <view className=\"flex-1 px-2 py-1 border border-gray-300 rounded text-xs bg-gray-50\">\n                              <text className=\"text-xs text-gray-500\">Add subtask...</text>\n                            </view>\n                            <view\n                              className=\"flex items-center justify-center px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs cursor-pointer transition-colors\"\n                              bindtap={() => addSubtask(task.id, 'New subtask')}\n                            >\n                              <Plus size={12} />\n                            </view>\n                          </view>\n                        </view>\n                      </view>\n                    </view>\n                  );\n                })}\n              </view>\n            </view>\n          )}\n\n          {/* Completed Tasks */}\n          {completedTasks.length > 0 && (\n            <view className=\"mb-6\">\n              <text className=\"text-lg font-semibold text-gray-900 mb-3\">Completed Tasks ({completedTasks.length})</text>\n              <view className=\"space-y-2\">\n                {completedTasks.map(task => {\n                  const category = categories.find(c => c.id === task.category);\n                  const priorityColors = {\n                    high: 'bg-red-500',\n                    medium: 'bg-yellow-500',\n                    low: 'bg-green-500'\n                  };\n\n                  return (\n                    <view key={task.id} className=\"relative flex bg-gray-50 border border-gray-200 rounded-lg p-4 opacity-70\">\n                      {/* Priority indicator */}\n                      <view className={`absolute left-0 top-0 bottom-0 w-1 rounded-l-lg ${priorityColors[task.priority]} opacity-50`} />\n\n                      <view className=\"flex-1 ml-2\">\n                        <view className=\"flex items-start gap-3 mb-2\">\n                          <view\n                            className=\"w-5 h-5 bg-green-500 border-2 border-green-500 rounded flex items-center justify-center cursor-pointer mt-0.5\"\n                            bindtap={() => toggleTask(task.id)}\n                          >\n                            <CheckSquare size={16} className=\"text-white\" />\n                          </view>\n                          <text className=\"flex-1 text-base font-medium text-gray-600 line-through cursor-pointer\">{task.title}</text>\n                          <view className=\"flex gap-2\">\n                            <view\n                              className=\"p-1 text-red-500 hover:bg-red-50 rounded cursor-pointer transition-colors\"\n                              bindtap={() => deleteTask(task.id)}\n                            >\n                              <Trash2 size={16} />\n                            </view>\n                          </view>\n                        </view>\n\n                        {/* Task metadata */}\n                        <view className=\"flex gap-2 items-center flex-wrap mb-2 opacity-70\">\n                          {/* Category */}\n                          {category && (\n                            <view className=\"flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white\" style={{ backgroundColor: category.color }}>\n                              <category.icon size={12} />\n                              <text className=\"text-xs font-medium text-white\">{category.name}</text>\n                            </view>\n                          )}\n\n                          {/* Priority */}\n                          <view className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            task.priority === 'high' ? 'bg-red-100 text-red-800' :\n                            task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :\n                            'bg-green-100 text-green-800'\n                          }`}>\n                            <text className=\"text-xs font-medium\">{task.priority.toUpperCase()}</text>\n                          </view>\n\n                          {/* Created date */}\n                          <view className=\"px-2 py-1 bg-gray-100 rounded-full\">\n                            <text className=\"text-xs text-gray-600\">Completed</text>\n                          </view>\n                        </view>\n\n                        {/* Tags */}\n                        {task.tags.length > 0 && (\n                          <view className=\"flex gap-1 flex-wrap mt-2 opacity-70\">\n                            {task.tags.map(tagId => {\n                              const tag = availableTags.find(t => t.id === tagId);\n                              return tag ? (\n                                <view\n                                  key={tagId}\n                                  className=\"px-2 py-0.5 rounded-full text-xs text-white\"\n                                  style={{ backgroundColor: tag.color }}\n                                >\n                                  <text className=\"text-xs font-medium text-white\">{tag.name}</text>\n                                </view>\n                              ) : null;\n                            })}\n                          </view>\n                        )}\n                      </view>\n                    </view>\n                  );\n                })}\n              </view>\n            </view>\n          )}\n\n          {/* Empty State */}\n          {tasks.length === 0 && (\n            <view className=\"flex flex-col items-center justify-center py-16 text-center\">\n              <view className=\"mb-4 opacity-50\">\n                <CheckSquare size={64} className=\"text-gray-400\" />\n              </view>\n              <text className=\"text-xl font-semibold text-gray-700 mb-2\">No tasks yet</text>\n              <text className=\"text-gray-500 max-w-xs leading-relaxed\">\n                Select a template above to create your first task!\n              </text>\n            </view>\n          )}\n        </scroll-view>\n      </view>\n    </view>\n  )\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "import { root } from '@lynx-js/react'\n\nimport { App } from './App.jsx'\n\nroot.render(<App />)\n\nif (import.meta.webpackHot) {\n  import.meta.webpackHot.accept()\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "export {}", "// getDefaultExport function for compatibility with non-ESM modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};\n", "__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n        if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n            Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n        }\n    }\n};", "__webpack_require__.hmd = (module) => {\n  module = Object.create(module);\n  if (!module.children) module.children = [];\n  Object.defineProperty(module, 'exports', {\n      enumerable: true,\n      set: () => {\n          throw new Error('ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: ' + module.id);\n      }\n  });\n  return module;\n};", "__webpack_require__.hu = (chunkId) => ('' + chunkId + '.' + __webpack_require__.h() + '.hot-update.js')", "__webpack_require__.h = () => (\"a97e645bc0f29fb2\")", "__webpack_require__.hmrF = function () {\n            return \"main__main-thread.\" + __webpack_require__.h() + \".hot-update.json\";\n         };\n        ", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "var currentModuleData = {};\nvar installedModules = __webpack_require__.c;\n\n// module and require creation\nvar currentChildModule;\nvar currentParents = [];\n\n// status\nvar registeredStatusHandlers = [];\nvar currentStatus = \"idle\";\n\n// while downloading\nvar blockingPromises = 0;\nvar blockingPromisesWaiting = [];\n\n// The update info\nvar currentUpdateApplyHandlers;\nvar queuedInvalidatedModules;\n\n__webpack_require__.hmrD = currentModuleData;\n__webpack_require__.i.push(function (options) {\n\tvar module = options.module;\n\tvar require = createRequire(options.require, options.id);\n\tmodule.hot = createModuleHotObject(options.id, module);\n\tmodule.parents = currentParents;\n\tmodule.children = [];\n\tcurrentParents = [];\n\toptions.require = require;\n});\n\n__webpack_require__.hmrC = {};\n__webpack_require__.hmrI = {};\n\nfunction createRequire(require, moduleId) {\n\tvar me = installedModules[moduleId];\n\tif (!me) return require;\n\tvar fn = function (request) {\n\t\tif (me.hot.active) {\n\t\t\tif (installedModules[request]) {\n\t\t\t\tvar parents = installedModules[request].parents;\n\t\t\t\tif (parents.indexOf(moduleId) === -1) {\n\t\t\t\t\tparents.push(moduleId);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcurrentParents = [moduleId];\n\t\t\t\tcurrentChildModule = request;\n\t\t\t}\n\t\t\tif (me.children.indexOf(request) === -1) {\n\t\t\t\tme.children.push(request);\n\t\t\t}\n\t\t} else {\n\t\t\tconsole.warn(\n\t\t\t\t\"[HMR] unexpected require(\" +\n\t\t\t\trequest +\n\t\t\t\t\") from disposed module \" +\n\t\t\t\tmoduleId\n\t\t\t);\n\t\t\tcurrentParents = [];\n\t\t}\n\t\treturn require(request);\n\t};\n\tvar createPropertyDescriptor = function (name) {\n\t\treturn {\n\t\t\tconfigurable: true,\n\t\t\tenumerable: true,\n\t\t\tget: function () {\n\t\t\t\treturn require[name];\n\t\t\t},\n\t\t\tset: function (value) {\n\t\t\t\trequire[name] = value;\n\t\t\t}\n\t\t};\n\t};\n\tfor (var name in require) {\n\t\tif (Object.prototype.hasOwnProperty.call(require, name) && name !== \"e\") {\n\t\t\tObject.defineProperty(fn, name, createPropertyDescriptor(name));\n\t\t}\n\t}\n\n\tfn.e = function (chunkId, fetchPriority) {\n\t\treturn trackBlockingPromise(require.e(chunkId, fetchPriority));\n\t};\n\n\treturn fn;\n}\n\nfunction createModuleHotObject(moduleId, me) {\n\tvar _main = currentChildModule !== moduleId;\n\tvar hot = {\n\t\t_acceptedDependencies: {},\n\t\t_acceptedErrorHandlers: {},\n\t\t_declinedDependencies: {},\n\t\t_selfAccepted: false,\n\t\t_selfDeclined: false,\n\t\t_selfInvalidated: false,\n\t\t_disposeHandlers: [],\n\t\t_main: _main,\n\t\t_requireSelf: function () {\n\t\t\tcurrentParents = me.parents.slice();\n\t\t\tcurrentChildModule = _main ? undefined : moduleId;\n\t\t\t__webpack_require__(moduleId);\n\t\t},\n\t\tactive: true,\n\t\taccept: function (dep, callback, errorHandler) {\n\t\t\tif (dep === undefined) hot._selfAccepted = true;\n\t\t\telse if (typeof dep === \"function\") hot._selfAccepted = dep;\n\t\t\telse if (typeof dep === \"object\" && dep !== null) {\n\t\t\t\tfor (var i = 0; i < dep.length; i++) {\n\t\t\t\t\thot._acceptedDependencies[dep[i]] = callback || function () { };\n\t\t\t\t\thot._acceptedErrorHandlers[dep[i]] = errorHandler;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\thot._acceptedDependencies[dep] = callback || function () { };\n\t\t\t\thot._acceptedErrorHandlers[dep] = errorHandler;\n\t\t\t}\n\t\t},\n\t\tdecline: function (dep) {\n\t\t\tif (dep === undefined) hot._selfDeclined = true;\n\t\t\telse if (typeof dep === \"object\" && dep !== null)\n\t\t\t\tfor (var i = 0; i < dep.length; i++)\n\t\t\t\t\thot._declinedDependencies[dep[i]] = true;\n\t\t\telse hot._declinedDependencies[dep] = true;\n\t\t},\n\t\tdispose: function (callback) {\n\t\t\thot._disposeHandlers.push(callback);\n\t\t},\n\t\taddDisposeHandler: function (callback) {\n\t\t\thot._disposeHandlers.push(callback);\n\t\t},\n\t\tremoveDisposeHandler: function (callback) {\n\t\t\tvar idx = hot._disposeHandlers.indexOf(callback);\n\t\t\tif (idx >= 0) hot._disposeHandlers.splice(idx, 1);\n\t\t},\n\t\tinvalidate: function () {\n\t\t\tthis._selfInvalidated = true;\n\t\t\tswitch (currentStatus) {\n\t\t\t\tcase \"idle\":\n\t\t\t\t\tcurrentUpdateApplyHandlers = [];\n\t\t\t\t\tObject.keys(__webpack_require__.hmrI).forEach(function (key) {\n\t\t\t\t\t\t__webpack_require__.hmrI[key](moduleId, currentUpdateApplyHandlers);\n\t\t\t\t\t});\n\t\t\t\t\tsetStatus(\"ready\");\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"ready\":\n\t\t\t\t\tObject.keys(__webpack_require__.hmrI).forEach(function (key) {\n\t\t\t\t\t\t__webpack_require__.hmrI[key](moduleId, currentUpdateApplyHandlers);\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"prepare\":\n\t\t\t\tcase \"check\":\n\t\t\t\tcase \"dispose\":\n\t\t\t\tcase \"apply\":\n\t\t\t\t\t(queuedInvalidatedModules = queuedInvalidatedModules || []).push(\n\t\t\t\t\t\tmoduleId\n\t\t\t\t\t);\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\tcheck: hotCheck,\n\t\tapply: hotApply,\n\t\tstatus: function (l) {\n\t\t\tif (!l) return currentStatus;\n\t\t\tregisteredStatusHandlers.push(l);\n\t\t},\n\t\taddStatusHandler: function (l) {\n\t\t\tregisteredStatusHandlers.push(l);\n\t\t},\n\t\tremoveStatusHandler: function (l) {\n\t\t\tvar idx = registeredStatusHandlers.indexOf(l);\n\t\t\tif (idx >= 0) registeredStatusHandlers.splice(idx, 1);\n\t\t},\n\t\tdata: currentModuleData[moduleId]\n\t};\n\tcurrentChildModule = undefined;\n\treturn hot;\n}\n\nfunction setStatus(newStatus) {\n\tcurrentStatus = newStatus; \n\tvar results = [];\n\tfor (var i = 0; i < registeredStatusHandlers.length; i++)\n\t\tresults[i] = registeredStatusHandlers[i].call(null, newStatus);\n\n\treturn Promise.all(results).then(function () { });\n}\n\nfunction unblock() {\n\tif (--blockingPromises === 0) {\n\t\tsetStatus(\"ready\").then(function () {\n\t\t\tif (blockingPromises === 0) {\n\t\t\t\tvar list = blockingPromisesWaiting;\n\t\t\t\tblockingPromisesWaiting = [];\n\t\t\t\tfor (var i = 0; i < list.length; i++) {\n\t\t\t\t\tlist[i]();\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n}\n\nfunction trackBlockingPromise(promise) {\n\tswitch (currentStatus) {\n\t\tcase \"ready\":\n\t\t\tsetStatus(\"prepare\");\n\t\tcase \"prepare\":\n\t\t\tblockingPromises++;\n\t\t\tpromise.then(unblock, unblock);\n\t\t\treturn promise;\n\t\tdefault:\n\t\t\treturn promise;\n\t}\n}\n\nfunction waitForBlockingPromises(fn) {\n\tif (blockingPromises === 0) return fn();\n\treturn new Promise(function (resolve) {\n\t\tblockingPromisesWaiting.push(function () {\n\t\t\tresolve(fn());\n\t\t});\n\t});\n}\n\nfunction hotCheck(applyOnUpdate) {\n\tif (currentStatus !== \"idle\") {\n\t\tthrow new Error(\"check() is only allowed in idle status\");\n\t} \n\treturn setStatus(\"check\")\n\t\t.then(__webpack_require__.hmrM)\n\t\t.then(function (update) {\n\t\t\tif (!update) {\n\t\t\t\treturn setStatus(applyInvalidatedModules() ? \"ready\" : \"idle\").then(\n\t\t\t\t\tfunction () {\n\t\t\t\t\t\treturn null;\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t}\n\n\t\t\treturn setStatus(\"prepare\").then(function () {\n\t\t\t\tvar updatedModules = [];\n\t\t\t\tcurrentUpdateApplyHandlers = [];\n\n\t\t\t\treturn Promise.all(\n\t\t\t\t\tObject.keys(__webpack_require__.hmrC).reduce(function (\n\t\t\t\t\t\tpromises,\n\t\t\t\t\t\tkey\n\t\t\t\t\t) {\n\t\t\t\t\t\t__webpack_require__.hmrC[key](\n\t\t\t\t\t\t\tupdate.c,\n\t\t\t\t\t\t\tupdate.r,\n\t\t\t\t\t\t\tupdate.m,\n\t\t\t\t\t\t\tpromises,\n\t\t\t\t\t\t\tcurrentUpdateApplyHandlers,\n\t\t\t\t\t\t\tupdatedModules\n\t\t\t\t\t\t);\n\t\t\t\t\t\treturn promises;\n\t\t\t\t\t},\n\t\t\t\t\t\t[])\n\t\t\t\t).then(function () {\n\t\t\t\t\treturn waitForBlockingPromises(function () {\n\t\t\t\t\t\tif (applyOnUpdate) {\n\t\t\t\t\t\t\treturn internalApply(applyOnUpdate);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn setStatus(\"ready\").then(function () {\n\t\t\t\t\t\t\treturn updatedModules;\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t});\n\t\t});\n}\n\nfunction hotApply(options) {\n\tif (currentStatus !== \"ready\") {\n\t\treturn Promise.resolve().then(function () {\n\t\t\tthrow new Error(\n\t\t\t\t\"apply() is only allowed in ready status (state: \" + currentStatus + \")\"\n\t\t\t);\n\t\t});\n\t}\n\treturn internalApply(options);\n}\n\nfunction internalApply(options) {\n\toptions = options || {};\n\tapplyInvalidatedModules();\n\tvar results = currentUpdateApplyHandlers.map(function (handler) {\n\t\treturn handler(options);\n\t});\n\tcurrentUpdateApplyHandlers = undefined;\n\tvar errors = results\n\t\t.map(function (r) {\n\t\t\treturn r.error;\n\t\t})\n\t\t.filter(Boolean);\n\n\tif (errors.length > 0) {\n\t\treturn setStatus(\"abort\").then(function () {\n\t\t\tthrow errors[0];\n\t\t});\n\t}\n\n\tvar disposePromise = setStatus(\"dispose\");\n\n\tresults.forEach(function (result) {\n\t\tif (result.dispose) result.dispose();\n\t});\n\n\tvar applyPromise = setStatus(\"apply\");\n\n\tvar error;\n\tvar reportError = function (err) {\n\t\tif (!error) error = err;\n\t};\n\n\tvar outdatedModules = [];\n\tresults.forEach(function (result) {\n\t\tif (result.apply) {\n\t\t\tvar modules = result.apply(reportError);\n\t\t\tif (modules) {\n\t\t\t\tfor (var i = 0; i < modules.length; i++) {\n\t\t\t\t\toutdatedModules.push(modules[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n\n\treturn Promise.all([disposePromise, applyPromise]).then(function () {\n\t\tif (error) {\n\t\t\treturn setStatus(\"fail\").then(function () {\n\t\t\t\tthrow error;\n\t\t\t});\n\t\t}\n\n\t\tif (queuedInvalidatedModules) {\n\t\t\treturn internalApply(options).then(function (list) {\n\t\t\t\toutdatedModules.forEach(function (moduleId) {\n\t\t\t\t\tif (list.indexOf(moduleId) < 0) list.push(moduleId);\n\t\t\t\t});\n\t\t\t\treturn list;\n\t\t\t});\n\t\t}\n\n\t\treturn setStatus(\"idle\").then(function () {\n\t\t\treturn outdatedModules;\n\t\t});\n\t});\n}\n\nfunction applyInvalidatedModules() {\n\tif (queuedInvalidatedModules) {\n\t\tif (!currentUpdateApplyHandlers) currentUpdateApplyHandlers = [];\n\t\tObject.keys(__webpack_require__.hmrI).forEach(function (key) {\n\t\t\tqueuedInvalidatedModules.forEach(function (moduleId) {\n\t\t\t\t__webpack_require__.hmrI[key](moduleId, currentUpdateApplyHandlers);\n\t\t\t});\n\t\t});\n\t\tqueuedInvalidatedModules = undefined;\n\t\treturn true;\n\t}\n}\n// @ts-nocheck\n__webpack_require__.i.push(function(options) {\n  if (\n    // This means this is in main-thread\n    !globalThis.__PREFRESH__\n    // Loading a module of background layer in main-thread, we replace the layer with the main-thread.\n    && options.id.includes('(react:background)')\n  ) {\n    // We may serialize the snapshot from background to main-thread.\n    // The `(react:background)` layer in the module id cannot be found in the main-thread.\n    // Thus we replace it here to make HMR work.\n    //\n    // Maybe it is better to run chunk loading on main thread.\n    options.id = options.id.replace(\n      `(react:background)`, // This is replaced by ReactRefreshWebpackPlugin\n      '(react:main-thread)', // This is replaced by ReactRefreshWebpackPlugin\n    );\n    const factory = __webpack_modules__[options.id];\n    if (factory) {\n      options.factory = factory;\n    }\n    return;\n  }\n  var originalFactory = options.factory;\n  options.factory = function(moduleObject, moduleExports, webpackRequire) {\n    var prevRefreshReg = globalThis.$RefreshReg$;\n    var prevRefreshSig = globalThis.$RefreshSig$;\n    globalThis.$RefreshSig$ = function() {\n      var status = 'begin';\n      var savedType;\n\n      return function(type, key, forceReset, getCustomHooks) {\n        // `globalThis.__PREFRESH__` may not exist when requiring `react`:\n        //   - require('react-refresh')\n        //     - require('react').options\n        //       - require('useSyncExternalStore')\n        //         - __REFRESH__.sign // not a function\n        // TODO(wangqingyu): Replace globalThis.__PREFRESH__ with lynx.__PREFRESH__\n        if (!globalThis.__PREFRESH__) {\n          return type;\n        }\n        if (!savedType) savedType = type;\n\n        status = globalThis.__PREFRESH__.sign(\n          type || savedType,\n          key,\n          forceReset,\n          getCustomHooks,\n          status,\n        );\n        return type;\n      };\n    };\n    var reg = function(currentModuleId) {\n      globalThis.$RefreshReg$ = function(type, id) {\n        // `globalThis.__PREFRESH__` may not exist when requiring `react`:\n        //   - require('react-refresh')\n        //     - require('react').options\n        //       - require('useSyncExternalStore')\n        //         - __REFRESH__.sign // not a function\n        if (globalThis.__PREFRESH__) {\n          globalThis.__PREFRESH__.register(type, currentModuleId + ' ' + id);\n        }\n      };\n    };\n    reg();\n    try {\n      originalFactory.call(this, moduleObject, moduleExports, webpackRequire);\n    } finally {\n      globalThis.$RefreshReg$ = prevRefreshReg;\n      globalThis.$RefreshSig$ = prevRefreshSig;\n    }\n  };\n});\n\n// noop fns to prevent runtime errors during initialization\nif (typeof globalThis !== \"undefined\") {\n  globalThis.$RefreshReg$ = function () {};\n  globalThis.$RefreshSig$ = function () {\n    return function(type) {\n      return type;\n    };\n  };\n}", "\n__webpack_require__.cssHotUpdateList = [[\"main__main-thread\",\".rspeedy/main__main-thread/main__main-thread.css.hot-update.json\"]];\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.p = \"http://192.168.68.103:3000/\";", "// lynx async chunks ids\n__webpack_require__.lynx_aci = {}", "// object to store loaded chunks\n// \"1\" means \"loaded\", otherwise not loaded yet\nvar installedChunks = __webpack_require__.hmrS_require = __webpack_require__.hmrS_require || {\n\t\"main__main-thread\": 1\n};\n// no on chunks loaded\n// no chunk install function needed\n// no chunk loading\n\n    function loadUpdateChunk(chunkId, updatedModulesList) {\n        return new Promise((resolve, reject) => {\n            lynx.requireModuleAsync(__webpack_require__.p\n                + __webpack_require__.hu(chunkId), (err, update) => {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                var updatedModules = update.modules;\n                var runtime = update.runtime;\n                for (var moduleId in updatedModules) {\n                    if (__webpack_require__.o(updatedModules, moduleId)) {\n                        currentUpdate[moduleId] = updatedModules[moduleId];\n                        if (updatedModulesList)\n                            updatedModulesList.push(moduleId);\n                    }\n                }\n                if (runtime)\n                    currentUpdateRuntime.push(runtime);\n                resolve();\n            });\n        });\n    }\n\n    var currentUpdateChunks;\n    var currentUpdate;\n    var currentUpdateRemovedChunks;\n    var currentUpdateRuntime;\n    function applyHandler(options) {\n        if (__webpack_require__.f) {\n            delete __webpack_require__.f.requireHmr;\n        }\n        currentUpdateChunks = undefined;\n        function getAffectedModuleEffects(updateModuleId) {\n            var outdatedModules = [updateModuleId];\n            var outdatedDependencies = {};\n            var queue = outdatedModules.map(function (id) {\n                return {\n                    chain: [id],\n                    id: id,\n                };\n            });\n            while (queue.length > 0) {\n                var queueItem = queue.pop();\n                var moduleId = queueItem.id;\n                var chain = queueItem.chain;\n                var module = __webpack_require__.c[moduleId];\n                if (!module\n                    || (module.hot._selfAccepted && !module.hot._selfInvalidated)) {\n                    continue;\n                }\n                if (module.hot._selfDeclined) {\n                    return {\n                        type: 'self-declined',\n                        chain: chain,\n                        moduleId: moduleId,\n                    };\n                }\n                if (module.hot._main) {\n                    return {\n                        type: 'unaccepted',\n                        chain: chain,\n                        moduleId: moduleId,\n                    };\n                }\n                for (var i = 0; i < module.parents.length; i++) {\n                    var parentId = module.parents[i];\n                    var parent = __webpack_require__.c[parentId];\n                    if (!parent)\n                        continue;\n                    if (parent.hot._declinedDependencies[moduleId]) {\n                        return {\n                            type: 'declined',\n                            chain: chain.concat([parentId]),\n                            moduleId: moduleId,\n                            parentId: parentId,\n                        };\n                    }\n                    if (outdatedModules.indexOf(parentId) !== -1)\n                        continue;\n                    if (parent.hot._acceptedDependencies[moduleId]) {\n                        if (!outdatedDependencies[parentId]) {\n                            outdatedDependencies[parentId] = [];\n                        }\n                        addAllToSet(outdatedDependencies[parentId], [moduleId]);\n                        continue;\n                    }\n                    delete outdatedDependencies[parentId];\n                    outdatedModules.push(parentId);\n                    queue.push({\n                        chain: chain.concat([parentId]),\n                        id: parentId,\n                    });\n                }\n            }\n            return {\n                type: 'accepted',\n                moduleId: updateModuleId,\n                outdatedModules: outdatedModules,\n                outdatedDependencies: outdatedDependencies,\n            };\n        }\n        function addAllToSet(a, b) {\n            for (var i = 0; i < b.length; i++) {\n                var item = b[i];\n                if (a.indexOf(item) === -1)\n                    a.push(item);\n            }\n        }\n        // at begin all updates modules are outdated\n        // the \"outdated\" status can propagate to parents if they don't accept the children\n        var outdatedDependencies = {};\n        var outdatedModules = [];\n        var appliedUpdate = {};\n        var warnUnexpectedRequire = function warnUnexpectedRequire(module) {\n            console.warn('[HMR] unexpected require(' + module.id + ') to disposed module');\n        };\n        for (var moduleId in currentUpdate) {\n            if (__webpack_require__.o(currentUpdate, moduleId)) {\n                var newModuleFactory = currentUpdate[moduleId];\n                /** @type {TODO} */\n                var result;\n                if (newModuleFactory) {\n                    result = getAffectedModuleEffects(moduleId);\n                }\n                else {\n                    result = {\n                        type: 'disposed',\n                        moduleId: moduleId,\n                    };\n                }\n                /** @type {Error|false} */\n                var abortError = false;\n                var doApply = false;\n                var doDispose = false;\n                var chainInfo = '';\n                if (result.chain) {\n                    chainInfo = '\\nUpdate propagation: ' + result.chain.join(' -> ');\n                }\n                switch (result.type) {\n                    case 'self-declined':\n                        if (options.onDeclined)\n                            options.onDeclined(result);\n                        if (!options.ignoreDeclined) {\n                            abortError = new Error('Aborted because of self decline: '\n                                + result.moduleId\n                                + chainInfo);\n                        }\n                        break;\n                    case 'declined':\n                        if (options.onDeclined)\n                            options.onDeclined(result);\n                        if (!options.ignoreDeclined) {\n                            abortError = new Error('Aborted because of declined dependency: '\n                                + result.moduleId\n                                + ' in '\n                                + result.parentId\n                                + chainInfo);\n                        }\n                        break;\n                    case 'unaccepted':\n                        if (options.onUnaccepted)\n                            options.onUnaccepted(result);\n                        if (!options.ignoreUnaccepted) {\n                            abortError = new Error('Aborted because ' + moduleId + ' is not accepted' + chainInfo);\n                        }\n                        break;\n                    case 'accepted':\n                        if (options.onAccepted)\n                            options.onAccepted(result);\n                        doApply = true;\n                        break;\n                    case 'disposed':\n                        if (options.onDisposed)\n                            options.onDisposed(result);\n                        doDispose = true;\n                        break;\n                    default:\n                        throw new Error('Un-exception type ' + result.type);\n                }\n                if (abortError) {\n                    return {\n                        error: abortError,\n                    };\n                }\n                if (doApply) {\n                    appliedUpdate[moduleId] = newModuleFactory;\n                    addAllToSet(outdatedModules, result.outdatedModules);\n                    for (moduleId in result.outdatedDependencies) {\n                        if (__webpack_require__.o(result.outdatedDependencies, moduleId)) {\n                            if (!outdatedDependencies[moduleId]) {\n                                outdatedDependencies[moduleId] = [];\n                            }\n                            addAllToSet(outdatedDependencies[moduleId], result.outdatedDependencies[moduleId]);\n                        }\n                    }\n                }\n                if (doDispose) {\n                    addAllToSet(outdatedModules, [result.moduleId]);\n                    appliedUpdate[moduleId] = warnUnexpectedRequire;\n                }\n            }\n        }\n        currentUpdate = undefined;\n        // Store self accepted outdated modules to require them later by the module system\n        var outdatedSelfAcceptedModules = [];\n        for (var j = 0; j < outdatedModules.length; j++) {\n            var outdatedModuleId = outdatedModules[j];\n            var module = __webpack_require__.c[outdatedModuleId];\n            if (module\n                && (module.hot._selfAccepted || module.hot._main)\n                // removed self-accepted modules should not be required\n                && appliedUpdate[outdatedModuleId] !== warnUnexpectedRequire\n                // when called invalidate self-accepting is not possible\n                && !module.hot._selfInvalidated) {\n                outdatedSelfAcceptedModules.push({\n                    module: outdatedModuleId,\n                    require: module.hot._requireSelf,\n                    errorHandler: module.hot._selfAccepted,\n                });\n            }\n        }\n        var moduleOutdatedDependencies;\n        return {\n            dispose: function () {\n                currentUpdateRemovedChunks.forEach(function (chunkId) {\n                    delete installedChunks[chunkId];\n                });\n                currentUpdateRemovedChunks = undefined;\n                var idx;\n                var queue = outdatedModules.slice();\n                while (queue.length > 0) {\n                    var moduleId = queue.pop();\n                    var module = __webpack_require__.c[moduleId];\n                    if (!module)\n                        continue;\n                    var data = {};\n                    // Call dispose handlers\n                    var disposeHandlers = module.hot._disposeHandlers;\n                    for (j = 0; j < disposeHandlers.length; j++) {\n                        disposeHandlers[j].call(null, data);\n                    }\n                    __webpack_require__.hmrD[moduleId] = data;\n                    // disable module (this disables requires from this module)\n                    module.hot.active = false;\n                    // remove module from cache\n                    delete __webpack_require__.c[moduleId];\n                    // when disposing there is no need to call dispose handler\n                    delete outdatedDependencies[moduleId];\n                    // remove \"parents\" references from all children\n                    for (j = 0; j < module.children.length; j++) {\n                        var child = __webpack_require__.c[module.children[j]];\n                        if (!child)\n                            continue;\n                        idx = child.parents.indexOf(moduleId);\n                        if (idx >= 0) {\n                            child.parents.splice(idx, 1);\n                        }\n                    }\n                }\n                // remove outdated dependency from module children\n                var dependency;\n                for (var outdatedModuleId in outdatedDependencies) {\n                    if (__webpack_require__.o(outdatedDependencies, outdatedModuleId)) {\n                        module = __webpack_require__.c[outdatedModuleId];\n                        if (module) {\n                            moduleOutdatedDependencies =\n                                outdatedDependencies[outdatedModuleId];\n                            for (j = 0; j < moduleOutdatedDependencies.length; j++) {\n                                dependency = moduleOutdatedDependencies[j];\n                                idx = module.children.indexOf(dependency);\n                                if (idx >= 0)\n                                    module.children.splice(idx, 1);\n                            }\n                        }\n                    }\n                }\n            },\n            apply: function (reportError) {\n                // insert new code\n                for (var updateModuleId in appliedUpdate) {\n                    if (__webpack_require__.o(appliedUpdate, updateModuleId)) {\n                        __webpack_require__.m[updateModuleId] =\n                            appliedUpdate[updateModuleId];\n                    }\n                }\n                // run new runtime modules\n                for (var i = 0; i < currentUpdateRuntime.length; i++) {\n                    currentUpdateRuntime[i](__webpack_require__);\n                }\n                // call accept handlers\n                for (var outdatedModuleId in outdatedDependencies) {\n                    if (__webpack_require__.o(outdatedDependencies, outdatedModuleId)) {\n                        var module = __webpack_require__.c[outdatedModuleId];\n                        if (module) {\n                            moduleOutdatedDependencies =\n                                outdatedDependencies[outdatedModuleId];\n                            var callbacks = [];\n                            var errorHandlers = [];\n                            var dependenciesForCallbacks = [];\n                            for (var j = 0; j < moduleOutdatedDependencies.length; j++) {\n                                var dependency = moduleOutdatedDependencies[j];\n                                var acceptCallback = module.hot._acceptedDependencies[dependency];\n                                var errorHandler = module.hot._acceptedErrorHandlers[dependency];\n                                if (acceptCallback) {\n                                    if (callbacks.indexOf(acceptCallback) !== -1)\n                                        continue;\n                                    callbacks.push(acceptCallback);\n                                    errorHandlers.push(errorHandler);\n                                    dependenciesForCallbacks.push(dependency);\n                                }\n                            }\n                            for (var k = 0; k < callbacks.length; k++) {\n                                try {\n                                    callbacks[k].call(null, moduleOutdatedDependencies);\n                                }\n                                catch (err) {\n                                    if (typeof errorHandlers[k] === 'function') {\n                                        try {\n                                            errorHandlers[k](err, {\n                                                moduleId: outdatedModuleId,\n                                                dependencyId: dependenciesForCallbacks[k],\n                                            });\n                                        }\n                                        catch (err2) {\n                                            if (options.onErrored) {\n                                                options.onErrored({\n                                                    type: 'accept-error-handler-errored',\n                                                    moduleId: outdatedModuleId,\n                                                    dependencyId: dependenciesForCallbacks[k],\n                                                    error: err2,\n                                                    originalError: err,\n                                                });\n                                            }\n                                            if (!options.ignoreErrored) {\n                                                reportError(err2);\n                                                reportError(err);\n                                            }\n                                        }\n                                    }\n                                    else {\n                                        if (options.onErrored) {\n                                            options.onErrored({\n                                                type: 'accept-errored',\n                                                moduleId: outdatedModuleId,\n                                                dependencyId: dependenciesForCallbacks[k],\n                                                error: err,\n                                            });\n                                        }\n                                        if (!options.ignoreErrored) {\n                                            reportError(err);\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n                // Load self accepted modules\n                for (var o = 0; o < outdatedSelfAcceptedModules.length; o++) {\n                    var item = outdatedSelfAcceptedModules[o];\n                    var moduleId = item.module;\n                    try {\n                        item.require(moduleId);\n                    }\n                    catch (err) {\n                        if (typeof item.errorHandler === 'function') {\n                            try {\n                                item.errorHandler(err, {\n                                    moduleId: moduleId,\n                                    module: __webpack_require__.c[moduleId],\n                                });\n                            }\n                            catch (err2) {\n                                if (options.onErrored) {\n                                    options.onErrored({\n                                        type: 'self-accept-error-handler-errored',\n                                        moduleId: moduleId,\n                                        error: err2,\n                                        originalError: err,\n                                    });\n                                }\n                                if (!options.ignoreErrored) {\n                                    reportError(err2);\n                                    reportError(err);\n                                }\n                            }\n                        }\n                        else {\n                            if (options.onErrored) {\n                                options.onErrored({\n                                    type: 'self-accept-errored',\n                                    moduleId: moduleId,\n                                    error: err,\n                                });\n                            }\n                            if (!options.ignoreErrored) {\n                                reportError(err);\n                            }\n                        }\n                    }\n                }\n                return outdatedModules;\n            },\n        };\n    }\n    __webpack_require__.hmrI.require = function (moduleId, applyHandlers) {\n        if (!currentUpdate) {\n            currentUpdate = {};\n            currentUpdateRuntime = [];\n            currentUpdateRemovedChunks = [];\n            applyHandlers.push(applyHandler);\n        }\n        if (!__webpack_require__.o(currentUpdate, moduleId)) {\n            currentUpdate[moduleId] = __webpack_require__.m[moduleId];\n        }\n    };\n    __webpack_require__.hmrC.require = function (chunkIds, removedChunks, removedModules, promises, applyHandlers, updatedModulesList) {\n        applyHandlers.push(applyHandler);\n        currentUpdateChunks = {};\n        currentUpdateRemovedChunks = removedChunks;\n        currentUpdate = removedModules.reduce(function (obj, key) {\n            obj[key] = false;\n            return obj;\n        }, {});\n        currentUpdateRuntime = [];\n        chunkIds.forEach(function (chunkId) {\n            if (__webpack_require__.o(installedChunks, chunkId)\n                && installedChunks[chunkId] !== undefined) {\n                promises.push(loadUpdateChunk(chunkId, updatedModulesList));\n                currentUpdateChunks[chunkId] = true;\n            }\n            else {\n                currentUpdateChunks[chunkId] = false;\n            }\n        });\n        if (__webpack_require__.f) {\n            __webpack_require__.f.requireHmr = function (chunkId, promises) {\n                if (currentUpdateChunks\n                    && __webpack_require__.o(currentUpdateChunks, chunkId)\n                    && !currentUpdateChunks[chunkId]) {\n                    promises.push(loadUpdateChunk(chunkId));\n                    currentUpdateChunks[chunkId] = true;\n                }\n            };\n        }\n    };\n\n    __webpack_require__.hmrM = function () {\n        return new Promise((resolve, reject) => lynx.requireModuleAsync(__webpack_require__.p\n            + __webpack_require__.hmrF(), (err, ret) => {\n            if (err)\n                return reject(err);\n            resolve(ret);\n        }))['catch'](function (err) {\n            if (err.code !== 'MODULE_NOT_FOUND')\n                throw err;\n        });\n    };"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAKA;AAGA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;ACnCA;AAEA;AACA;AAEA;AACA;AAMA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAIA;AAOA;AACA;AAEA;AACA;AAGA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AC3DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACzPA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AACA;AAKA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;;ACjEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA;AAEA;AACA;AAKA;AAeA;;AACA;AAIA;AAlBA;AACA;AACA;AACA;AACA;AACA;AAOA;AAOA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AClGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACnEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;;;;;;;;;AClDA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAkBA;AAjBA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AADA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAGA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACxDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAWA;AAEA;AACA;AACA;;;;;;;;AAQA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;AC/BA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;;;;;;;;;;;;;;;;;;;;;;;;;;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AADA;;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AAGA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/QA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;;AAEA;AAGA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACzCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;;;;;;;;;;;;;;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACRA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;AC3BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVA;AACA;AACA;AACA;AAEA;AACA;AAKA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AAGA;AACA;AACA;AAwDA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;;AAEA;AAEA;AACA;AACA;;AAEA;AAEA;AACA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;ACnIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AC7CA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;ACVA;AACA;AACA;AACA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACjCA;AACA;AACA;AACA;;;;;;;;;AASA;AAEA;AACA;AACA;;;;AAIA;AAEA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AAIA;AACA;AAYA;AAEA;AACA;;;AAIA;AAEA;AAGA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAGA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;AChDA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AAAA;AACA;;;AAGA;AAEA;;;AAGA;AAEA;AAIA;AAEA;AACA;;AAEA;AAEA;AAGA;AACA;AACA;AACA;;;AAGA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AA3BA;AACA;AACA;AA0BA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7EA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAEA;AACA;AACA;AAEA;AACA;AAGA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;AC5EA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AAGA;AACA;AAEA;AACA;AACA;AAEA;AAGA;AACA;AAGA;AACA;AACA;AAGA;AAGA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;AChDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAaA;AAZA;AACA;AACA;AAGA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAAA;AACA;AACA;AACA;;;;;;;;;AASA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AASA;AARA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAKA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAGA;AAAA;AACA;AAAA;AACA;AAGA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACvQA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAGA;AACA;AAEA;AAIA;AAHA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AA9IA;AA2BA;AACA;AACA;AACA;AACA;AA9BA;AACA;AACA;AACA;AA2IA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtJA;AACA;AACA;AAEA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;AAQA;AAEA;AAEA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;AAEA;AACA;;;;;AAKA;AAEA;AACA;;;;;;;;;;;;;;;AAeA;AAEA;;;;;;;;;;;;AAYA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;ACxHA;AACA;AACA;AAEA;AACA;AACA;AAIA;AACA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AAGA;AACA;AACA;AAIA;AACA;AAiBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AACA;;AAEA;;;;;;;;;;;;;;AChIA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACHA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AAYA;AACA;AACA;AAAA;AACA;AAIA;AAEA;;;;;;;;;;;;;;;;;;AC7BA;AAOA;AAKA;AAFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AAAA;AAIA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAQA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;ACxFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAGA;AADA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AAAA;AAEA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;AC/CA;AACA;AACA;AACA;AACA;;;;;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAsCA;AACA;AACA;AACA;AAeA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrJA;AACA;AACA;AACA;AAEA;AACA;AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AA0BA;AAxBA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAIA;AAEA;AAEA;AACA;AAGA;AACA;AAEA;;AAEA;AACA;AACA;AAAA;AACA;AAYA;AAXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AAWA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;ACzHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACtDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AChIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBA;AACA;AACA;AACA;;;;AAIA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAGA;AAGA;AAEA;AACA;AACA;;;;;;;;AAQA;AAEA;AACA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAIA;AAIA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACrOA;AACA;AACA;AAEA;AACA;;;AAGA;AAEA;AACA;AACA;AAEA;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AAAA;AACA;AACA;AACA;AAKA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AAEA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAiBA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;;;;;;AAMA;AAuBA;AACA;AACA;AACA;AACA;AACA;AAIA;AAAA;AAWA;AAGA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAIA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAIA;AAEA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAOA;AACA;AACA;AACA;AACA;AAGA;AAIA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AAIA;;AAGA;AAEA;AACA;AACA;AACA;AACA;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AAIA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAlUA;AAJA;AAyHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA7HA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAyTA;;;;;;;;;;;;;;;;;;AChgBA;AACA;AACA;AACA;;;;;;AAMA;AAEA;;AAEA;;;;;;;;;;;;;;;;;;ACbA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACfA;AACA;AACA;AAEA;AACA;AACA;AACA;AAGA;AAIA;AAEA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;ACtBA;AACA;AACA;AACA;AACA;AAEA;AACA;AAGA;AAGA;AACA;AAGA;AAGA;;;;;;;;;;;;;;;;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACrBA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAFA;AACA;;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCA;AACA;AACA;AACA;AACA;;AACA;AACA;AAGA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AAEA;AAGA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AAIA;AAEA;AACA;AACA;AAGA;AAGA;AACA;AACA;AACA;AAGA;AACA;AAGA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1GA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAGA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AAKA;AAEA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAGA;AAGA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AAKA;AAEA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AAGA;AACA;AAGA;AACA;AACA;AACA;AAGA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AC/RA;AACA;AACA;AACA;AACA;AAEA;AACA;AAGA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;ACrBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAIA;AAAA;AAAA;;AAGA;AACA;AAIA;AAHA;AAGA;;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAOA;AAEA;;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;ACvEA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AAIA;;;;;;;;;;;;;;;;;;;;ACpCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACtBA;AACA;AACA;AACA;AACA;AACA;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACzBA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAZA;AACA;;AAYA;AACA;;;;;;;;;;;;;;;;;;;;;;;AClBA;AACA;AACA;AACA;AAGA;AACA;AAgBA;;AAEA;AAwBA;;AAEA;AAMA;;;;;;;;;;;;;;;;;AAiBA;AAEA;AAMA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AC5GA;;;;;;;;;;;;;;;;;AAiBA;AAGA;AAmBA;;;;;;;;;;;;;;;;;;AC3CA;AACA;;;;AAIA;AAEA;AACA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA+BA;AAIA;AAIA;AACA;AACA;AAIA;AAGA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AAvCA;;AAEA;AAEA;AACA;AAMA;AAEA;AA2BA;AACA;;;;;;AAMA;AAEA;AACA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AC9FA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;ACvBA;AACA;AACA;AACA;;;;AAIA;AAEA;AAAA;AACA;AACA;;;;;;AAMA;;AAEA;AACA;AACA;;;;AAIA;AAEA;AAAA;AAGA;AACA;;;;AAIA;AAEA;AACA;AACA;;;;AAIA;;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;AC/CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;;;;AAIA;AAEA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACrBA;AACA;AACA;AACA;;;;;;;AAOA;;AAEA;AACA;AAGA;AACA;AAGA;AACA;;AAEA;;;AAEA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AC7BA;;;;;;;;;;;;ACAA;;;;;AAKA;AAGA;AACA;AAEA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AAIA;;;;;;;;;;;;;;;;;;;;;ACzCA;;;;;AAKA;AAGA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAKA;AACA;AAEA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;AC5BA;;;;;AAKA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACnBA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACrBA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACfA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACfA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;AChBA;;;;;AAKA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACpBA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACrBA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACfA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACjBA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACtBA;;;;;AAKA;AAIA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;AClBA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACdA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;AClBA;;;;;AAKA;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;AChBA;;;;;AAKA;AAGA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3BA;AACA;AACA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AACA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AACA;AAEA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AACA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAGA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAIA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AAEA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;;;;AAIA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;;;;;;;;;;;;;;;;AAgBA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;;;AAGA;AAEA;AACA;AAEA;;;;AAIA;AAEA;AACA;AAEA;;;;AAIA;AAEA;AACA;AAEA;;;;AAIA;AAEA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AAEA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AAEA;;;;;;;;AAQA;AAEA;AACA;AAEA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;;;;AAIA;AAEA;AAEA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC72BA;AAEA;AAEA;AAEA;AAGA;AAEA;AACA;AACA;AAEA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AACA;AACA;AAEA;;;;;;;;AAQA;AAEA;AAIA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAGA;AACA;AACA;AAEA;AACA;AACA;AAEA;;;;;;;;AAQA;AAEA;AACA;AAEA;AAEA;;;;;;;;AAQA;AAEA;AAIA;AACA;AAAA;AAAA;AACA;AACA;AAIA;AACA;AACA;AACA;AAKA;AACA;AACA;AAEA;;;;;;;;;;;AAWA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AAEA;;;;;;;;AAQA;AAEA;AACA;AACA;AAGA;AAEA;AAGA;AAEA;AAIA;AACA;AACA;AACA;AAGA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;AASA;AAGA;;;AAGA;AAEA;AAIA;AACA;AACA;AACA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AAEA;AAIA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AAGA;;;;;;;AAOA;AAGA;AAEA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;AAqBA;AAEA;AAMA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;AAGA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AAEA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AAEA;AAEA;AACA;AACA;AAGA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;AACA;AACA;AAEA;AAEA;;;;;AAKA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AACA;AAAA;AAEA;AACA;AAEA;AAEA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AAEA;AAGA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;AAOA;AAEA;AACA;AACA;AAEA;AACA;AAGA;AACA;AAKA;AACA;AACA;AAKA;AACA;AAGA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AACA;AAGA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAGA;AAEA;AACA;AAEA;;;;AAIA;AAEA;;;;AAIA;AAEA;AACA;AACA;AAMA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;AAiBA;AAEA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAGA;AACA;AAGA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;AAGA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAGA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAGA;AACA;AACA;AAGA;AAGA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;;;;AAIA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;AAcA;AAEA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AAGA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAKA;AAEA;AACA;AACA;AACA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AACA;AAGA;AACA;AAGA;AACA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AACA;AACA;AACA;AAIA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;;;;AAMA;AAEA;AACA;AACA;AACA;AAAA;AAIA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAKA;AACA;AAIA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;;;;;;AAMA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAEA;AAGA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;;;;AAIA;AAEA;AACA;AAEA;;;;;;;;AAQA;AAEA;AAIA;AACA;AAGA;AACA;AAAA;AAAA;AAGA;AAEA;AACA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACt6CA;AAEA;AAGA;AAGA;AAGA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;;;;AAIA;AAEA;AACA;AACA;AAEA;;;;;;;AAOA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AAEA;AAGA;AAEA;;;AAGA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AAEA;AAGA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;AASA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;AACA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AAEA;AACA;AACA;AACA;AAEA;;;;;AAKA;AAEA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACngBA;AACA;AAeA;;;;AAyXA;;;;;;;;AAIA;;;;;;;;AAIA;;;;;;;;AAeA;;;;;;;;AAeA;;AAGA;;;;;;;;;;;;;;;;;AAPA;;;;;;;;AAsCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA;;;;;;;;;;;;;;;;;;;;;AAgCA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;;;;;;;;;;;;;;;;;;;;AAfA;;;;;;;;AA4BA;;AACA;;;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;AAIA;;AACA;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;AAIA;;AACA;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;AAIA;;AACA;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;AAxBA;;AACA;;;;;AACA;;;AA2BA;;;AAQA;;;;;;;;;;;;;;;;;;;;;;;;AAuBA;;;;;;;;AAJA;;;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATA;;;;;;;;AA+BA;;AAGA;;;;;;;;;;;;;;;;;AAbA;;;;;;;;;AAgCA;;;;;;;;;;;;;;;;;;;;AAXA;;;;;;;;AA0CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;;;;;;;;;;;;;;;;;;;;;;;;;;AAvBA;;;;;;;;;;;;;;;;;;;;;AAiEA;;;;;;;;;;;;;;;;;;;;AAbA;;AACA;;;;;AACA;;;;;;;;;;;;;;;;;AAsBA;;;;;;;;AAsCA;;;;;;;;;;AAKA;;;;;;;;AAGA;;;;;;;;;;AAcA;;;;;;;;AAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA;;;;;;;;AAOA;;;;;;;;AAFA;;;;;;;;;;;;;;;;;;;;;;;AAoBA;;AAGA;;;;;;;;;;;;;;;;;;;AATA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;;;;;;;;;;AAfA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;;;;;;;;AAFA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;;;;;;;;;;AAjHA;;;;AAIA;;;AACA;;;;;;;AAQA;;;;;AAWA;;;;;;;;;;;AA2BA;;;AACA;;;;;;;;;;;AAuDA;;;AACA;;;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxHA;;;;;;;;AAFA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;;;;;;;;;;AAKA;;;;;;;;AAGA;;;;;;;;;;AAcA;;;;;;;;AAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA;;;;;;;;AAiBA;;AAGA;;;;;;;;;;;;;;;;;;;AATA;;;;;;;;AAlDA;;;;AAIA;;;AACA;;;;;;;AAQA;;;;;AAWA;;;;;;;;;AAmBA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAtDA;;;;;;;;AAFA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsFA;;AACA;;;AAGA;;;;;AACA;;;;;;;;;;;;;;;;;;;;;AA7OA;;;;;;;;AAncA;;AACA;;;AAEA;;;AACA;;;AACA;;;;;AAGA;;;AAEA;;;;;AAIA;;;;;AAIA;;;;;AAUA;;;;;AAIA;;;;;AAIA;;;;;AAKA;;;AACA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAMA;;;AACA;;;;;AAGA;;;AACA;;;;;AACA;;;AACA;;;;;AAIA;;;AAGA;;;;;;;AAmBA;;;AACA;;;;;AACA;;;;;AASA;;;;;;;AAoBA;;;AACA;;;;;AACA;;;;;AASA;;;;;;;AAmBA;;;AACA;;;;;AACA;;;;;AASA;;;;;;;AAyBA;;;AACA;;;;;;;AAuBA;;;;;AA4CA;;;AACA;;;;;;;;;;;;;AAOA;;;AACA;;;;;;;AAkBA;;;AACA;;;;;;;AAsBA;;;AACA;;;;;;;AAmBA;;;AACA;;;;;AACA;;;;;AASA;;;;;;;AAoBA;;;AACA;;;;;;;AAqCA;;;AACA;;;;;AAWA;;;;;;;AA0BA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxuBA;AAuhBA;AAphBA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAKA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAIA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAIA;AAEA;AACA;AAGA;AACA;AAEA;AAAA;AAAA;AAGA;AAGA;AAEA;AACA;AAGA;AACA;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAIA;AAJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAIA;AACA;AAIA;AACA;AAIA;AACA;AACA;AACA;AAMA;AANA;AAIA;AACA;AACA;;AAGA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;;;;;;AAgGA;;AA+BA;;AA8BA;;AA8KA;;AA4GA;;AAOA;;;;AAhbA;AAAA;;;;;;;;AAcA;AAAA;;;;;;;;AAQA;AAAA;;;;;;;AAQA;AAAA;;;;;;AAIA;AAAA;;;;;;AAIA;AAAA;;;;;;AAeA;AAAA;;;;;;AAWA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAMA;AAJA;;;;;;;;;;;AAwBA;;AAGA;;;;;AAOA;AAAA;;;;;;;AACA;AAAA;;;;;;;AATA;;;;;;;AA6BA;AAAA;AAAA;AAAA;AAAA;;AAGA;;;AAOA;AARA;;;;;;;AA4BA;;AAGA;AAKA;AAAA;AAAA;;AAKA;AAAA;AAAA;;AAEA;AAbA;;;;;;AAuBA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;AAGA;;;AAOA;AARA;;;;;;;;;;;AAgBA;;;;;AAIA;;;;AAEA;;;;;;AAIA;;;;;;;;;;AAMA;;;;AAEA;;;;;;AAIA;;;;AAEA;;;;;;;;;;;;;;AAqBA;;;AAAA;;AAQA;AACA;;AAIA;AAAA;AAAA;;;;AAEA;AAAA;;;;;;;AACA;AAAA;;;;;;;AACA;;;AAAA;;;AANA;;;;;;;;;;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAMA;AAJA;;;;;;;;;;AAYA;AACA;AAAA;AAAA;AAAA;AAAA;;AAGA;;;AAOA;AARA;;;;;;;;;;;AA4BA;;AAGA;;;;;AAOA;AAAA;;;;;;;AACA;AAAA;;;;;;;AATA;;;;;;AAkBA;AACA;;AAGA;AAKA;AACA;AACA;AACA;AACA;;AAWA;AACA;AACA;;AAEA;AAzBA;;;;;;;;;;AAmCA;;AACA;;;AAOA;AAAA;AAAA;;;;;;;;;;;;AAKA;AAIA;AAAA;AAAA;AAAA;AAAA;;AAGA;;;AAOA;AARA;;;;;;;;;;;AAmBA;AAAA;;;;;;AAgBA;;AAEA;;;AAEA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAGA;AAgCA;;;AA5BA;;;;AAIA;AAAA;AAAA;;;;;;;;;;;AAEA;AAAA;;;;;;AAEA;;;;AAIA;AAAA;;;;;;;;;;;;AAQA;;AACA;AAAA;AAAA;;;;AACA;AAAA;;;;;;;AACA;AAAA;;;;;;;;;;;;;AAUA;AAAA;;;;;;;AAIA;;;AAEA;AAAA;;;;;;;AACA;AAAA;;;;;;;;;;;;;;AAMA;;;AAKA;AAEA;AACA;AACA;;AAIA;AAAA;AAAA;;AAEA;AAJA;;;;AAMA;AACA;;;;;;;;AAKA;;;AAEA;;;AAAA;;AACA;AACA;;AAEA;;AACA;;;AAOA;AAAA;AAAA;;;;;;;;;;;AAEA;;AAAA;;AACA;;;;;;AAEA;;;;AAIA;AAAA;;;;;;;;;;;;AAlBA;;;;;;;;;;;;;;;;;AAgCA;;;;AAIA;AAAA;;;;;;;;;;;;AApHA;;;;;AA2HA;;;;;;;;;;;;AAMA;;;AAEA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAGA;AAgCA;;;AA5BA;;;;AAIA;AAAA;AAAA;;;;;;;;;;;AAEA;AAAA;;;;;;AAEA;;;;AAIA;AAAA;;;;;;;;;;;;AAQA;;AACA;AAAA;AAAA;;;;AACA;AAAA;;;;;;;AACA;AAAA;;;;;;;;;;;;;AAUA;AAAA;;;;;;;AAUA;AAEA;AACA;AACA;;AAIA;AAAA;AAAA;;AAEA;AAJA;;;;AAMA;AACA;;;;;;;;AA9DA;;;;;AAoEA;;;;;;;;;;;;AAMA;AAGA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;AAYA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AC3kCA;AAEA;AAEA;;;;;AAEA;AAKA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AE5DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;ACVA;;;;ACAA;;;;ACAA;AACA;AACA;AACA;;;;ACHA;;;;ACAA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AC5b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}
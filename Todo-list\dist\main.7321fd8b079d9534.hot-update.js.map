{"version": 3, "file": "main.7321fd8b079d9534.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\context\\appReducer.ts", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\utils\\helpers.ts", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx", "file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\context\\AppContext.tsx", "file://webpack/runtime/get_full_hash"], "sourcesContent": ["import { AppState, AppAction, Task, Category, Tag } from '../types';\nimport { generateId } from '../utils/helpers';\n\nexport function appReducer(state: AppState, action: AppAction): AppState {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n\n    case 'SET_ERROR':\n      return {\n        ...state,\n        error: action.payload\n      };\n\n    case 'ADD_TASK': {\n      const now = new Date();\n      const newTask: Task = {\n        ...action.payload,\n        id: generateId(),\n        createdAt: now,\n        updatedAt: now,\n        status: action.payload.status || 'pending',\n        priority: action.payload.priority || 'medium',\n        tags: action.payload.tags || [],\n        subtasks: action.payload.subtasks || []\n      };\n\n      return {\n        ...state,\n        tasks: [...state.tasks, newTask]\n      };\n    }\n\n    case 'UPDATE_TASK': {\n      const { id, updates } = action.payload;\n      return {\n        ...state,\n        tasks: state.tasks.map(task =>\n          task.id === id\n            ? { ...task, ...updates, updatedAt: new Date() }\n            : task\n        )\n      };\n    }\n\n    case 'DELETE_TASK':\n      return {\n        ...state,\n        tasks: state.tasks.filter(task => task.id !== action.payload),\n        selectedTaskId: state.selectedTaskId === action.payload ? undefined : state.selectedTaskId\n      };\n\n    case 'TOGGLE_TASK_STATUS': {\n      const taskId = action.payload;\n      return {\n        ...state,\n        tasks: state.tasks.map(task => {\n          if (task.id === taskId) {\n            const newStatus = task.status === 'completed' ? 'pending' : 'completed';\n            const now = new Date();\n            return {\n              ...task,\n              status: newStatus,\n              completedAt: newStatus === 'completed' ? now : undefined,\n              updatedAt: now\n            };\n          }\n          return task;\n        })\n      };\n    }\n\n    case 'ADD_SUBTASK': {\n      const { taskId, subtask } = action.payload;\n      const newSubtask = {\n        ...subtask,\n        id: generateId(),\n        createdAt: new Date()\n      };\n\n      return {\n        ...state,\n        tasks: state.tasks.map(task =>\n          task.id === taskId\n            ? {\n                ...task,\n                subtasks: [...task.subtasks, newSubtask],\n                updatedAt: new Date()\n              }\n            : task\n        )\n      };\n    }\n\n    case 'UPDATE_SUBTASK': {\n      const { taskId, subtaskId, updates } = action.payload;\n      return {\n        ...state,\n        tasks: state.tasks.map(task =>\n          task.id === taskId\n            ? {\n                ...task,\n                subtasks: task.subtasks.map(subtask =>\n                  subtask.id === subtaskId\n                    ? { ...subtask, ...updates }\n                    : subtask\n                ),\n                updatedAt: new Date()\n              }\n            : task\n        )\n      };\n    }\n\n    case 'DELETE_SUBTASK': {\n      const { taskId, subtaskId } = action.payload;\n      return {\n        ...state,\n        tasks: state.tasks.map(task =>\n          task.id === taskId\n            ? {\n                ...task,\n                subtasks: task.subtasks.filter(subtask => subtask.id !== subtaskId),\n                updatedAt: new Date()\n              }\n            : task\n        )\n      };\n    }\n\n    case 'ADD_CATEGORY': {\n      const newCategory: Category = {\n        ...action.payload,\n        id: generateId()\n      };\n\n      return {\n        ...state,\n        categories: [...state.categories, newCategory]\n      };\n    }\n\n    case 'UPDATE_CATEGORY': {\n      const { id, updates } = action.payload;\n      return {\n        ...state,\n        categories: state.categories.map(category =>\n          category.id === id\n            ? { ...category, ...updates }\n            : category\n        )\n      };\n    }\n\n    case 'DELETE_CATEGORY': {\n      const categoryId = action.payload;\n      return {\n        ...state,\n        categories: state.categories.filter(category => category.id !== categoryId),\n        // Remove category from tasks\n        tasks: state.tasks.map(task =>\n          task.categoryId === categoryId\n            ? { ...task, categoryId: undefined, updatedAt: new Date() }\n            : task\n        )\n      };\n    }\n\n    case 'ADD_TAG': {\n      const newTag: Tag = {\n        ...action.payload,\n        id: generateId()\n      };\n\n      return {\n        ...state,\n        tags: [...state.tags, newTag]\n      };\n    }\n\n    case 'UPDATE_TAG': {\n      const { id, updates } = action.payload;\n      return {\n        ...state,\n        tags: state.tags.map(tag =>\n          tag.id === id\n            ? { ...tag, ...updates }\n            : tag\n        )\n      };\n    }\n\n    case 'DELETE_TAG': {\n      const tagId = action.payload;\n      return {\n        ...state,\n        tags: state.tags.filter(tag => tag.id !== tagId),\n        // Remove tag from tasks\n        tasks: state.tasks.map(task => ({\n          ...task,\n          tags: task.tags.filter(id => id !== tagId),\n          updatedAt: task.tags.includes(tagId) ? new Date() : task.updatedAt\n        }))\n      };\n    }\n\n    case 'SET_FILTER':\n      return {\n        ...state,\n        filter: { ...state.filter, ...action.payload }\n      };\n\n    case 'CLEAR_FILTER':\n      return {\n        ...state,\n        filter: {}\n      };\n\n    case 'SET_SORT':\n      return {\n        ...state,\n        sort: action.payload\n      };\n\n    case 'SELECT_TASK':\n      return {\n        ...state,\n        selectedTaskId: action.payload\n      };\n\n    case 'UPDATE_SETTINGS':\n      return {\n        ...state,\n        settings: { ...state.settings, ...action.payload }\n      };\n\n    case 'LOAD_DATA': {\n      const { tasks, categories, tags } = action.payload;\n      return {\n        ...state,\n        tasks,\n        categories,\n        tags\n      };\n    }\n\n    case 'RESET_APP':\n      return {\n        ...state,\n        tasks: [],\n        categories: state.categories, // Keep default categories\n        tags: state.tags, // Keep default tags\n        filter: {},\n        selectedTaskId: undefined,\n        error: undefined\n      };\n\n    default:\n      return state;\n  }\n}\n", "import { Task, Priority, TaskStatus } from '../types';\n\n// Generate unique ID\nexport function generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n\n// Format date for display\nexport function formatDate(date: Date): string {\n  const now = new Date();\n  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);\n  const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);\n\n  const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());\n\n  if (taskDate.getTime() === today.getTime()) {\n    return 'Today';\n  } else if (taskDate.getTime() === yesterday.getTime()) {\n    return 'Yesterday';\n  } else if (taskDate.getTime() === tomorrow.getTime()) {\n    return 'Tomorrow';\n  } else {\n    return date.toLocaleDateString();\n  }\n}\n\n// Format date and time for display\nexport function formatDateTime(date: Date): string {\n  return `${formatDate(date)} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;\n}\n\n// Check if a task is overdue\nexport function isTaskOverdue(task: Task): boolean {\n  if (!task.dueDate || task.status === 'completed') {\n    return false;\n  }\n  \n  const now = new Date();\n  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n  const taskDate = new Date(task.dueDate.getFullYear(), task.dueDate.getMonth(), task.dueDate.getDate());\n  \n  return taskDate < today;\n}\n\n// Check if a task is due today\nexport function isTaskDueToday(task: Task): boolean {\n  if (!task.dueDate || task.status === 'completed') {\n    return false;\n  }\n  \n  const now = new Date();\n  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n  const taskDate = new Date(task.dueDate.getFullYear(), task.dueDate.getMonth(), task.dueDate.getDate());\n  \n  return taskDate.getTime() === today.getTime();\n}\n\n// Check if a task is due this week\nexport function isTaskDueThisWeek(task: Task): boolean {\n  if (!task.dueDate || task.status === 'completed') {\n    return false;\n  }\n  \n  const now = new Date();\n  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n  const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n  \n  return task.dueDate >= today && task.dueDate < weekFromNow;\n}\n\n// Get priority color\nexport function getPriorityColor(priority: Priority): string {\n  switch (priority) {\n    case 'high':\n      return '#EF4444'; // Red\n    case 'medium':\n      return '#F59E0B'; // Orange\n    case 'low':\n      return '#10B981'; // Green\n    default:\n      return '#6B7280'; // Gray\n  }\n}\n\n// Get status color\nexport function getStatusColor(status: TaskStatus): string {\n  switch (status) {\n    case 'completed':\n      return '#10B981'; // Green\n    case 'pending':\n      return '#3B82F6'; // Blue\n    case 'cancelled':\n      return '#6B7280'; // Gray\n    default:\n      return '#6B7280'; // Gray\n  }\n}\n\n// Calculate task completion percentage\nexport function getTaskCompletionPercentage(task: Task): number {\n  if (task.status === 'completed') {\n    return 100;\n  }\n  \n  if (task.subtasks.length === 0) {\n    return 0;\n  }\n  \n  const completedSubtasks = task.subtasks.filter(subtask => subtask.completed).length;\n  return Math.round((completedSubtasks / task.subtasks.length) * 100);\n}\n\n// Sort tasks by priority\nexport function sortTasksByPriority(tasks: Task[]): Task[] {\n  const priorityOrder: Record<Priority, number> = {\n    high: 3,\n    medium: 2,\n    low: 1\n  };\n  \n  return [...tasks].sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);\n}\n\n// Sort tasks by due date\nexport function sortTasksByDueDate(tasks: Task[]): Task[] {\n  return [...tasks].sort((a, b) => {\n    if (!a.dueDate && !b.dueDate) return 0;\n    if (!a.dueDate) return 1;\n    if (!b.dueDate) return -1;\n    return a.dueDate.getTime() - b.dueDate.getTime();\n  });\n}\n\n// Filter tasks by search query\nexport function filterTasksBySearch(tasks: Task[], query: string): Task[] {\n  if (!query.trim()) {\n    return tasks;\n  }\n  \n  const searchTerm = query.toLowerCase().trim();\n  \n  return tasks.filter(task => \n    task.title.toLowerCase().includes(searchTerm) ||\n    task.description?.toLowerCase().includes(searchTerm) ||\n    task.subtasks.some(subtask => subtask.title.toLowerCase().includes(searchTerm))\n  );\n}\n\n// Get tasks due in a specific time range\nexport function getTasksDueInRange(tasks: Task[], startDate: Date, endDate: Date): Task[] {\n  return tasks.filter(task => {\n    if (!task.dueDate || task.status === 'completed') {\n      return false;\n    }\n    \n    return task.dueDate >= startDate && task.dueDate <= endDate;\n  });\n}\n\n// Create a new task with default values\nexport function createDefaultTask(overrides: Partial<Task> = {}): Omit<Task, 'id' | 'createdAt' | 'updatedAt'> {\n  return {\n    title: '',\n    status: 'pending',\n    priority: 'medium',\n    tags: [],\n    subtasks: [],\n    ...overrides\n  };\n}\n\n// Validate task data\nexport function validateTask(task: Partial<Task>): string[] {\n  const errors: string[] = [];\n  \n  if (!task.title || task.title.trim().length === 0) {\n    errors.push('Task title is required');\n  }\n  \n  if (task.title && task.title.length > 200) {\n    errors.push('Task title must be less than 200 characters');\n  }\n  \n  if (task.description && task.description.length > 1000) {\n    errors.push('Task description must be less than 1000 characters');\n  }\n  \n  if (task.dueDate && task.dueDate < new Date()) {\n    // Allow past dates for editing existing tasks\n    // errors.push('Due date cannot be in the past');\n  }\n  \n  return errors;\n}\n\n// Export task data as JSON\nexport function exportTasksAsJSON(tasks: Task[]): string {\n  return JSON.stringify(tasks, null, 2);\n}\n\n// Import tasks from JSON\nexport function importTasksFromJSON(jsonString: string): Task[] {\n  try {\n    const data = JSON.parse(jsonString);\n    \n    if (!Array.isArray(data)) {\n      throw new Error('Invalid format: expected an array of tasks');\n    }\n    \n    return data.map((task: any) => ({\n      ...task,\n      id: task.id || generateId(),\n      createdAt: new Date(task.createdAt || Date.now()),\n      updatedAt: new Date(task.updatedAt || Date.now()),\n      dueDate: task.dueDate ? new Date(task.dueDate) : undefined,\n      completedAt: task.completedAt ? new Date(task.completedAt) : undefined,\n      subtasks: (task.subtasks || []).map((subtask: any) => ({\n        ...subtask,\n        id: subtask.id || generateId(),\n        createdAt: new Date(subtask.createdAt || Date.now())\n      }))\n    }));\n  } catch (error) {\n    throw new Error('Failed to parse JSON data');\n  }\n}\n\n// Debounce function for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n", "import { useEffect } from '@lynx-js/react'\nimport './App.css'\nimport { AppProvider } from './context/AppContext'\nimport { TaskList } from './components/TaskList'\n\nexport function App(props: {\n  onMounted?: () => void\n}) {\n  useEffect(() => {\n    console.info('Hello, Todo App with ReactLynx!')\n    props.onMounted?.()\n  }, [])\n\n  return (\n    <AppProvider>\n      <view>\n        <view className='Background' />\n        <view className='App'>\n          <TaskList />\n        </view>\n      </view>\n    </AppProvider>\n  )\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "import { useState, useCallback } from '@lynx-js/react';\nimport { Priority, CreateTaskInput } from '../types';\nimport { useApp } from '../context/AppContext';\nimport { validateTask } from '../utils/helpers';\n\ninterface AddTaskFormProps {\n  onClose: () => void;\n  onTaskAdded?: () => void;\n}\n\nexport function AddTaskForm({ onClose, onTaskAdded }: AddTaskFormProps) {\n  const { addTask, state } = useApp();\n  \n  const [formData, setFormData] = useState<CreateTaskInput>({\n    title: '',\n    description: '',\n    priority: 'medium',\n    dueDate: undefined,\n    categoryId: '',\n    tagIds: [],\n    estimatedDuration: undefined\n  });\n  \n  const [errors, setErrors] = useState<string[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleInputChange = useCallback((field: keyof CreateTaskInput, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // Clear errors when user starts typing\n    if (errors.length > 0) {\n      setErrors([]);\n    }\n  }, [errors.length]);\n\n  const handleSubmit = useCallback(async () => {\n    setIsSubmitting(true);\n    \n    // Validate form\n    const validationErrors = validateTask(formData);\n    if (validationErrors.length > 0) {\n      setErrors(validationErrors);\n      setIsSubmitting(false);\n      return;\n    }\n\n    try {\n      // Create task\n      const taskData = {\n        ...formData,\n        status: 'pending' as const,\n        tags: formData.tagIds || [],\n        subtasks: []\n      };\n\n      addTask(taskData);\n      onTaskAdded?.();\n      onClose();\n    } catch (error) {\n      setErrors(['Failed to create task. Please try again.']);\n    } finally {\n      setIsSubmitting(false);\n    }\n  }, [formData, addTask, onTaskAdded, onClose]);\n\n  const handleTagToggle = useCallback((tagId: string) => {\n    const currentTags = formData.tagIds || [];\n    const newTags = currentTags.includes(tagId)\n      ? currentTags.filter(id => id !== tagId)\n      : [...currentTags, tagId];\n    \n    handleInputChange('tagIds', newTags);\n  }, [formData.tagIds, handleInputChange]);\n\n  return (\n    <view className=\"add-task-form\">\n      <view className=\"form-header\">\n        <text className=\"form-title\">Add New Task</text>\n        <text className=\"close-button\" bindtap={onClose}>✕</text>\n      </view>\n\n      <view className=\"form-content\">\n        {/* Errors */}\n        {errors.length > 0 && (\n          <view className=\"form-errors\">\n            {errors.map((error, index) => (\n              <text key={index} className=\"error-text\">{error}</text>\n            ))}\n          </view>\n        )}\n\n        {/* Title */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Title *</text>\n          <input\n            className=\"field-input\"\n            placeholder=\"Enter task title...\"\n            value={formData.title}\n            onInput={(e: any) => handleInputChange('title', e.detail.value)}\n          />\n        </view>\n\n        {/* Description */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Description</text>\n          <textarea\n            className=\"field-textarea\"\n            placeholder=\"Enter task description...\"\n            value={formData.description}\n            onInput={(e: any) => handleInputChange('description', e.detail.value)}\n          />\n        </view>\n\n        {/* Priority */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Priority</text>\n          <view className=\"priority-options\">\n            {(['low', 'medium', 'high'] as Priority[]).map(priority => (\n              <view\n                key={priority}\n                className={`priority-option ${formData.priority === priority ? 'selected' : ''}`}\n                bindtap={() => handleInputChange('priority', priority)}\n              >\n                <text className=\"priority-text\">{priority.toUpperCase()}</text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Due Date */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Due Date</text>\n          <input\n            className=\"field-input\"\n            type=\"date\"\n            value={formData.dueDate ? formData.dueDate.toISOString().split('T')[0] : ''}\n            onInput={(e: any) => {\n              const value = e.detail.value;\n              handleInputChange('dueDate', value ? new Date(value) : undefined);\n            }}\n          />\n        </view>\n\n        {/* Category */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Category</text>\n          <view className=\"category-options\">\n            <view\n              className={`category-option ${!formData.categoryId ? 'selected' : ''}`}\n              bindtap={() => handleInputChange('categoryId', '')}\n            >\n              <text className=\"category-text\">No Category</text>\n            </view>\n            {state.categories.map(category => (\n              <view\n                key={category.id}\n                className={`category-option ${formData.categoryId === category.id ? 'selected' : ''}`}\n                style={{ borderColor: category.color }}\n                bindtap={() => handleInputChange('categoryId', category.id)}\n              >\n                <text className=\"category-icon\">{category.icon}</text>\n                <text className=\"category-text\">{category.name}</text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Tags */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Tags</text>\n          <view className=\"tag-options\">\n            {state.tags.map(tag => (\n              <view\n                key={tag.id}\n                className={`tag-option ${(formData.tagIds || []).includes(tag.id) ? 'selected' : ''}`}\n                style={{ \n                  backgroundColor: (formData.tagIds || []).includes(tag.id) ? tag.color : 'transparent',\n                  borderColor: tag.color\n                }}\n                bindtap={() => handleTagToggle(tag.id)}\n              >\n                <text \n                  className=\"tag-text\"\n                  style={{ \n                    color: (formData.tagIds || []).includes(tag.id) ? 'white' : tag.color \n                  }}\n                >\n                  {tag.name}\n                </text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Estimated Duration */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Estimated Duration (minutes)</text>\n          <input\n            className=\"field-input\"\n            type=\"number\"\n            placeholder=\"e.g., 30\"\n            value={formData.estimatedDuration?.toString() || ''}\n            onInput={(e: any) => {\n              const value = parseInt(e.detail.value);\n              handleInputChange('estimatedDuration', isNaN(value) ? undefined : value);\n            }}\n          />\n        </view>\n      </view>\n\n      {/* Actions */}\n      <view className=\"form-actions\">\n        <view className=\"action-button secondary\" bindtap={onClose}>\n          <text className=\"button-text\">Cancel</text>\n        </view>\n        <view \n          className={`action-button primary ${isSubmitting ? 'disabled' : ''}`}\n          bindtap={isSubmitting ? undefined : handleSubmit}\n        >\n          <text className=\"button-text\">\n            {isSubmitting ? 'Creating...' : 'Create Task'}\n          </text>\n        </view>\n      </view>\n    </view>\n  );\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "import { useState, useCallback, useEffect } from '@lynx-js/react';\nimport { Task, Priority } from '../types';\nimport { useApp } from '../context/AppContext';\nimport { validateTask } from '../utils/helpers';\n\ninterface EditTaskFormProps {\n  task: Task;\n  onClose: () => void;\n  onTaskUpdated?: () => void;\n}\n\nexport function EditTaskForm({ task, onClose, onTaskUpdated }: EditTaskFormProps) {\n  const { updateTask, state } = useApp();\n  \n  const [formData, setFormData] = useState({\n    title: task.title,\n    description: task.description || '',\n    priority: task.priority,\n    dueDate: task.dueDate,\n    categoryId: task.categoryId || '',\n    tagIds: task.tags,\n    estimatedDuration: task.estimatedDuration\n  });\n  \n  const [errors, setErrors] = useState<string[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleInputChange = useCallback((field: string, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // Clear errors when user starts typing\n    if (errors.length > 0) {\n      setErrors([]);\n    }\n  }, [errors.length]);\n\n  const handleSubmit = useCallback(async () => {\n    setIsSubmitting(true);\n    \n    // Validate form\n    const validationErrors = validateTask(formData);\n    if (validationErrors.length > 0) {\n      setErrors(validationErrors);\n      setIsSubmitting(false);\n      return;\n    }\n\n    try {\n      // Update task\n      const updates = {\n        title: formData.title,\n        description: formData.description || undefined,\n        priority: formData.priority,\n        dueDate: formData.dueDate,\n        categoryId: formData.categoryId || undefined,\n        tags: formData.tagIds,\n        estimatedDuration: formData.estimatedDuration\n      };\n\n      updateTask(task.id, updates);\n      onTaskUpdated?.();\n      onClose();\n    } catch (error) {\n      setErrors(['Failed to update task. Please try again.']);\n    } finally {\n      setIsSubmitting(false);\n    }\n  }, [formData, task.id, updateTask, onTaskUpdated, onClose]);\n\n  const handleTagToggle = useCallback((tagId: string) => {\n    const currentTags = formData.tagIds || [];\n    const newTags = currentTags.includes(tagId)\n      ? currentTags.filter(id => id !== tagId)\n      : [...currentTags, tagId];\n    \n    handleInputChange('tagIds', newTags);\n  }, [formData.tagIds, handleInputChange]);\n\n  return (\n    <view className=\"edit-task-form\">\n      <view className=\"form-header\">\n        <text className=\"form-title\">Edit Task</text>\n        <text className=\"close-button\" bindtap={onClose}>✕</text>\n      </view>\n\n      <view className=\"form-content\">\n        {/* Errors */}\n        {errors.length > 0 && (\n          <view className=\"form-errors\">\n            {errors.map((error, index) => (\n              <text key={index} className=\"error-text\">{error}</text>\n            ))}\n          </view>\n        )}\n\n        {/* Title */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Title *</text>\n          <input\n            className=\"field-input\"\n            placeholder=\"Enter task title...\"\n            value={formData.title}\n            onInput={(e: any) => handleInputChange('title', e.detail.value)}\n          />\n        </view>\n\n        {/* Description */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Description</text>\n          <textarea\n            className=\"field-textarea\"\n            placeholder=\"Enter task description...\"\n            value={formData.description}\n            onInput={(e: any) => handleInputChange('description', e.detail.value)}\n          />\n        </view>\n\n        {/* Priority */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Priority</text>\n          <view className=\"priority-options\">\n            {(['low', 'medium', 'high'] as Priority[]).map(priority => (\n              <view\n                key={priority}\n                className={`priority-option ${formData.priority === priority ? 'selected' : ''}`}\n                bindtap={() => handleInputChange('priority', priority)}\n              >\n                <text className=\"priority-text\">{priority.toUpperCase()}</text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Due Date */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Due Date</text>\n          <input\n            className=\"field-input\"\n            type=\"date\"\n            value={formData.dueDate ? formData.dueDate.toISOString().split('T')[0] : ''}\n            onInput={(e: any) => {\n              const value = e.detail.value;\n              handleInputChange('dueDate', value ? new Date(value) : undefined);\n            }}\n          />\n        </view>\n\n        {/* Category */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Category</text>\n          <view className=\"category-options\">\n            <view\n              className={`category-option ${!formData.categoryId ? 'selected' : ''}`}\n              bindtap={() => handleInputChange('categoryId', '')}\n            >\n              <text className=\"category-text\">No Category</text>\n            </view>\n            {state.categories.map(category => (\n              <view\n                key={category.id}\n                className={`category-option ${formData.categoryId === category.id ? 'selected' : ''}`}\n                style={{ borderColor: category.color }}\n                bindtap={() => handleInputChange('categoryId', category.id)}\n              >\n                <text className=\"category-icon\">{category.icon}</text>\n                <text className=\"category-text\">{category.name}</text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Tags */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Tags</text>\n          <view className=\"tag-options\">\n            {state.tags.map(tag => (\n              <view\n                key={tag.id}\n                className={`tag-option ${(formData.tagIds || []).includes(tag.id) ? 'selected' : ''}`}\n                style={{ \n                  backgroundColor: (formData.tagIds || []).includes(tag.id) ? tag.color : 'transparent',\n                  borderColor: tag.color\n                }}\n                bindtap={() => handleTagToggle(tag.id)}\n              >\n                <text \n                  className=\"tag-text\"\n                  style={{ \n                    color: (formData.tagIds || []).includes(tag.id) ? 'white' : tag.color \n                  }}\n                >\n                  {tag.name}\n                </text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Estimated Duration */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Estimated Duration (minutes)</text>\n          <input\n            className=\"field-input\"\n            type=\"number\"\n            placeholder=\"e.g., 30\"\n            value={formData.estimatedDuration?.toString() || ''}\n            onInput={(e: any) => {\n              const value = parseInt(e.detail.value);\n              handleInputChange('estimatedDuration', isNaN(value) ? undefined : value);\n            }}\n          />\n        </view>\n\n        {/* Task Status */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Status</text>\n          <view className=\"status-info\">\n            <text className=\"status-text\">\n              Current status: <text className=\"status-value\">{task.status}</text>\n            </text>\n            <text className=\"status-note\">\n              Use the checkbox in the task list to change completion status\n            </text>\n          </view>\n        </view>\n      </view>\n\n      {/* Actions */}\n      <view className=\"form-actions\">\n        <view className=\"action-button secondary\" bindtap={onClose}>\n          <text className=\"button-text\">Cancel</text>\n        </view>\n        <view \n          className={`action-button primary ${isSubmitting ? 'disabled' : ''}`}\n          bindtap={isSubmitting ? undefined : handleSubmit}\n        >\n          <text className=\"button-text\">\n            {isSubmitting ? 'Updating...' : 'Update Task'}\n          </text>\n        </view>\n      </view>\n    </view>\n  );\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "import { useCallback } from '@lynx-js/react';\nimport { Task, Priority } from '../types';\nimport { useApp } from '../context/AppContext';\nimport { formatDate, isTaskOverdue, isTaskDueToday, getPriorityColor, getTaskCompletionPercentage } from '../utils/helpers';\n\ninterface TaskItemProps {\n  task: Task;\n  onEdit?: (task: Task) => void;\n}\n\nexport function TaskItem({ task, onEdit }: TaskItemProps) {\n  const { toggleTaskStatus, deleteTask, getCategoryById, getTagById } = useApp();\n\n  const handleToggleComplete = useCallback(() => {\n    toggleTaskStatus(task.id);\n  }, [task.id, toggleTaskStatus]);\n\n  const handleDelete = useCallback(() => {\n    deleteTask(task.id);\n  }, [task.id, deleteTask]);\n\n  const handleEdit = useCallback(() => {\n    onEdit?.(task);\n  }, [task, onEdit]);\n\n  const category = task.categoryId ? getCategoryById(task.categoryId) : null;\n  const isOverdue = isTaskOverdue(task);\n  const isDueToday = isTaskDueToday(task);\n  const completionPercentage = getTaskCompletionPercentage(task);\n\n  const priorityColor = getPriorityColor(task.priority);\n  const isCompleted = task.status === 'completed';\n\n  return (\n    <view className={`task-item ${isCompleted ? 'completed' : ''} ${isOverdue ? 'overdue' : ''}`}>\n      {/* Priority indicator */}\n      <view \n        className=\"priority-indicator\"\n        style={{ backgroundColor: priorityColor }}\n      />\n      \n      {/* Main content */}\n      <view className=\"task-content\">\n        {/* Header */}\n        <view className=\"task-header\">\n          {/* Checkbox */}\n          <view \n            className={`task-checkbox ${isCompleted ? 'checked' : ''}`}\n            bindtap={handleToggleComplete}\n          >\n            {isCompleted && <text className=\"checkmark\">✓</text>}\n          </view>\n          \n          {/* Title */}\n          <text \n            className={`task-title ${isCompleted ? 'completed' : ''}`}\n            bindtap={handleEdit}\n          >\n            {task.title}\n          </text>\n          \n          {/* Actions */}\n          <view className=\"task-actions\">\n            <text className=\"action-button edit\" bindtap={handleEdit}>✏️</text>\n            <text className=\"action-button delete\" bindtap={handleDelete}>🗑️</text>\n          </view>\n        </view>\n\n        {/* Description */}\n        {task.description && (\n          <text className=\"task-description\">{task.description}</text>\n        )}\n\n        {/* Metadata */}\n        <view className=\"task-metadata\">\n          {/* Category */}\n          {category && (\n            <view className=\"task-category\" style={{ backgroundColor: category.color }}>\n              <text className=\"category-icon\">{category.icon}</text>\n              <text className=\"category-name\">{category.name}</text>\n            </view>\n          )}\n\n          {/* Due date */}\n          {task.dueDate && (\n            <view className={`task-due-date ${isOverdue ? 'overdue' : isDueToday ? 'due-today' : ''}`}>\n              <text className=\"due-date-icon\">📅</text>\n              <text className=\"due-date-text\">{formatDate(task.dueDate)}</text>\n            </view>\n          )}\n\n          {/* Priority */}\n          <view className=\"task-priority\" style={{ color: priorityColor }}>\n            <text className=\"priority-text\">{task.priority.toUpperCase()}</text>\n          </view>\n        </view>\n\n        {/* Tags */}\n        {task.tags.length > 0 && (\n          <view className=\"task-tags\">\n            {task.tags.map(tagId => {\n              const tag = getTagById(tagId);\n              return tag ? (\n                <view \n                  key={tagId}\n                  className=\"task-tag\"\n                  style={{ backgroundColor: tag.color }}\n                >\n                  <text className=\"tag-text\">{tag.name}</text>\n                </view>\n              ) : null;\n            })}\n          </view>\n        )}\n\n        {/* Subtasks progress */}\n        {task.subtasks.length > 0 && (\n          <view className=\"subtasks-progress\">\n            <view className=\"progress-bar\">\n              <view \n                className=\"progress-fill\"\n                style={{ width: `${completionPercentage}%` }}\n              />\n            </view>\n            <text className=\"progress-text\">\n              {task.subtasks.filter(st => st.completed).length}/{task.subtasks.length} subtasks\n            </text>\n          </view>\n        )}\n      </view>\n    </view>\n  );\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "import { useState, useCallback, useMemo } from '@lynx-js/react';\nimport { Task } from '../types';\nimport { useApp } from '../context/AppContext';\nimport { TaskItem } from './TaskItem';\nimport { AddTaskForm } from './AddTaskForm';\nimport { EditTaskForm } from './EditTaskForm';\n\nexport function TaskList() {\n  const { getFilteredTasks, getTaskStats } = useApp();\n  \n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingTask, setEditingTask] = useState<Task | null>(null);\n\n  const tasks = useMemo(() => getFilteredTasks(), [getFilteredTasks]);\n  const stats = useMemo(() => getTaskStats(), [getTaskStats]);\n\n  const handleAddTask = useCallback(() => {\n    setShowAddForm(true);\n  }, []);\n\n  const handleCloseAddForm = useCallback(() => {\n    setShowAddForm(false);\n  }, []);\n\n  const handleEditTask = useCallback((task: Task) => {\n    setEditingTask(task);\n  }, []);\n\n  const handleCloseEditForm = useCallback(() => {\n    setEditingTask(null);\n  }, []);\n\n  const handleTaskAdded = useCallback(() => {\n    setShowAddForm(false);\n  }, []);\n\n  const handleTaskUpdated = useCallback(() => {\n    setEditingTask(null);\n  }, []);\n\n  // Group tasks by status for better organization\n  const pendingTasks = useMemo(() => \n    tasks.filter(task => task.status === 'pending'), [tasks]\n  );\n  \n  const completedTasks = useMemo(() => \n    tasks.filter(task => task.status === 'completed'), [tasks]\n  );\n\n  return (\n    <view className=\"task-list\">\n      {/* Header */}\n      <view className=\"task-list-header\">\n        <view className=\"header-content\">\n          <text className=\"header-title\">My Tasks</text>\n          <view className=\"add-task-button\" bindtap={handleAddTask}>\n            <text className=\"add-button-text\">+ Add Task</text>\n          </view>\n        </view>\n        \n        {/* Stats */}\n        <view className=\"task-stats\">\n          <view className=\"stat-item\">\n            <text className=\"stat-number\">{stats.total}</text>\n            <text className=\"stat-label\">Total</text>\n          </view>\n          <view className=\"stat-item\">\n            <text className=\"stat-number\">{stats.pending}</text>\n            <text className=\"stat-label\">Pending</text>\n          </view>\n          <view className=\"stat-item\">\n            <text className=\"stat-number\">{stats.completed}</text>\n            <text className=\"stat-label\">Completed</text>\n          </view>\n          <view className=\"stat-item overdue\">\n            <text className=\"stat-number\">{stats.overdue}</text>\n            <text className=\"stat-label\">Overdue</text>\n          </view>\n          <view className=\"stat-item due-today\">\n            <text className=\"stat-number\">{stats.dueToday}</text>\n            <text className=\"stat-label\">Due Today</text>\n          </view>\n        </view>\n      </view>\n\n      {/* Task sections */}\n      <scroll-view className=\"task-list-content\">\n        {/* Pending Tasks */}\n        {pendingTasks.length > 0 && (\n          <view className=\"task-section\">\n            <view className=\"section-header\">\n              <text className=\"section-title\">Pending Tasks ({pendingTasks.length})</text>\n            </view>\n            <view className=\"task-items\">\n              {pendingTasks.map(task => (\n                <TaskItem\n                  key={task.id}\n                  task={task}\n                  onEdit={handleEditTask}\n                />\n              ))}\n            </view>\n          </view>\n        )}\n\n        {/* Completed Tasks */}\n        {completedTasks.length > 0 && (\n          <view className=\"task-section\">\n            <view className=\"section-header\">\n              <text className=\"section-title\">Completed Tasks ({completedTasks.length})</text>\n            </view>\n            <view className=\"task-items\">\n              {completedTasks.map(task => (\n                <TaskItem\n                  key={task.id}\n                  task={task}\n                  onEdit={handleEditTask}\n                />\n              ))}\n            </view>\n          </view>\n        )}\n\n        {/* Empty state */}\n        {tasks.length === 0 && (\n          <view className=\"empty-state\">\n            <text className=\"empty-icon\">📝</text>\n            <text className=\"empty-title\">No tasks yet</text>\n            <text className=\"empty-description\">\n              Create your first task to get started with organizing your work!\n            </text>\n            <view className=\"empty-action\" bindtap={handleAddTask}>\n              <text className=\"empty-action-text\">Create First Task</text>\n            </view>\n          </view>\n        )}\n      </scroll-view>\n\n      {/* Add Task Form Modal */}\n      {showAddForm && (\n        <view className=\"modal-overlay\">\n          <view className=\"modal-content\">\n            <AddTaskForm\n              onClose={handleCloseAddForm}\n              onTaskAdded={handleTaskAdded}\n            />\n          </view>\n        </view>\n      )}\n\n      {/* Edit Task Form Modal */}\n      {editingTask && (\n        <view className=\"modal-overlay\">\n          <view className=\"modal-content\">\n            <EditTaskForm\n              task={editingTask}\n              onClose={handleCloseEditForm}\n              onTaskUpdated={handleTaskUpdated}\n            />\n          </view>\n        </view>\n      )}\n    </view>\n  );\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "import { createContext, useContext, useReducer, useEffect, ReactNode } from '@lynx-js/react';\nimport { AppState, AppAction, Task, Category, Tag, AppSettings, TaskSort } from '../types';\nimport { appReducer } from './appReducer';\nimport { generateId } from '../utils/helpers';\n\n// Initial state\nconst initialSettings: AppSettings = {\n  theme: 'light',\n  defaultPriority: 'medium',\n  enableNotifications: true,\n  autoMarkOverdue: false,\n  showCompletedTasks: true,\n  taskSortOrder: { field: 'dueDate', direction: 'asc' }\n};\n\nconst initialState: AppState = {\n  tasks: [],\n  categories: [\n    { id: 'work', name: 'Work', color: '#3B82F6', icon: '💼' },\n    { id: 'personal', name: 'Personal', color: '#10B981', icon: '🏠' },\n    { id: 'shopping', name: 'Shopping', color: '#F59E0B', icon: '🛒' },\n    { id: 'health', name: 'Health', color: '#EF4444', icon: '🏥' }\n  ],\n  tags: [\n    { id: 'urgent', name: 'Urgent', color: '#DC2626' },\n    { id: 'important', name: 'Important', color: '#7C3AED' },\n    { id: 'quick', name: 'Quick Task', color: '#059669' },\n    { id: 'meeting', name: 'Meeting', color: '#2563EB' }\n  ],\n  filter: {},\n  sort: { field: 'dueDate', direction: 'asc' },\n  settings: initialSettings,\n  isLoading: false\n};\n\n// Context\ninterface AppContextType {\n  state: AppState;\n  dispatch: React.Dispatch<AppAction>;\n  // Helper functions\n  addTask: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateTask: (id: string, updates: Partial<Task>) => void;\n  deleteTask: (id: string) => void;\n  toggleTaskStatus: (id: string) => void;\n  addCategory: (category: Omit<Category, 'id'>) => void;\n  addTag: (tag: Omit<Tag, 'id'>) => void;\n  getTaskById: (id: string) => Task | undefined;\n  getCategoryById: (id: string) => Category | undefined;\n  getTagById: (id: string) => Tag | undefined;\n  getFilteredTasks: () => Task[];\n  getTaskStats: () => {\n    total: number;\n    completed: number;\n    pending: number;\n    overdue: number;\n    dueToday: number;\n    dueThisWeek: number;\n  };\n}\n\nconst AppContext = createContext<AppContextType | undefined>(undefined);\n\n// Provider component\ninterface AppProviderProps {\n  children: ReactNode;\n}\n\nexport function AppProvider({ children }: AppProviderProps) {\n  const [state, dispatch] = useReducer(appReducer, initialState);\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const savedData = localStorage.getItem('todoApp');\n    if (savedData) {\n      try {\n        const parsedData = JSON.parse(savedData);\n        // Convert date strings back to Date objects\n        const tasks = parsedData.tasks?.map((task: any) => ({\n          ...task,\n          createdAt: new Date(task.createdAt),\n          updatedAt: new Date(task.updatedAt),\n          dueDate: task.dueDate ? new Date(task.dueDate) : undefined,\n          completedAt: task.completedAt ? new Date(task.completedAt) : undefined,\n          subtasks: task.subtasks?.map((subtask: any) => ({\n            ...subtask,\n            createdAt: new Date(subtask.createdAt)\n          })) || []\n        })) || [];\n        \n        dispatch({\n          type: 'LOAD_DATA',\n          payload: {\n            tasks,\n            categories: parsedData.categories || initialState.categories,\n            tags: parsedData.tags || initialState.tags\n          }\n        });\n      } catch (error) {\n        console.error('Failed to load saved data:', error);\n      }\n    }\n  }, []);\n\n  // Save data to localStorage whenever state changes\n  useEffect(() => {\n    const dataToSave = {\n      tasks: state.tasks,\n      categories: state.categories,\n      tags: state.tags,\n      settings: state.settings\n    };\n    localStorage.setItem('todoApp', JSON.stringify(dataToSave));\n  }, [state.tasks, state.categories, state.tags, state.settings]);\n\n  // Helper functions\n  const addTask = (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => {\n    dispatch({ type: 'ADD_TASK', payload: task });\n  };\n\n  const updateTask = (id: string, updates: Partial<Task>) => {\n    dispatch({ type: 'UPDATE_TASK', payload: { id, updates } });\n  };\n\n  const deleteTask = (id: string) => {\n    dispatch({ type: 'DELETE_TASK', payload: id });\n  };\n\n  const toggleTaskStatus = (id: string) => {\n    dispatch({ type: 'TOGGLE_TASK_STATUS', payload: id });\n  };\n\n  const addCategory = (category: Omit<Category, 'id'>) => {\n    dispatch({ type: 'ADD_CATEGORY', payload: category });\n  };\n\n  const addTag = (tag: Omit<Tag, 'id'>) => {\n    dispatch({ type: 'ADD_TAG', payload: tag });\n  };\n\n  const getTaskById = (id: string): Task | undefined => {\n    return state.tasks.find(task => task.id === id);\n  };\n\n  const getCategoryById = (id: string): Category | undefined => {\n    return state.categories.find(category => category.id === id);\n  };\n\n  const getTagById = (id: string): Tag | undefined => {\n    return state.tags.find(tag => tag.id === id);\n  };\n\n  const getFilteredTasks = (): Task[] => {\n    let filteredTasks = [...state.tasks];\n    const { filter } = state;\n\n    // Apply filters\n    if (filter.status && filter.status.length > 0) {\n      filteredTasks = filteredTasks.filter(task => filter.status!.includes(task.status));\n    }\n\n    if (filter.priority && filter.priority.length > 0) {\n      filteredTasks = filteredTasks.filter(task => filter.priority!.includes(task.priority));\n    }\n\n    if (filter.categoryId) {\n      filteredTasks = filteredTasks.filter(task => task.categoryId === filter.categoryId);\n    }\n\n    if (filter.tagIds && filter.tagIds.length > 0) {\n      filteredTasks = filteredTasks.filter(task => \n        filter.tagIds!.some(tagId => task.tags.includes(tagId))\n      );\n    }\n\n    if (filter.searchQuery) {\n      const query = filter.searchQuery.toLowerCase();\n      filteredTasks = filteredTasks.filter(task => \n        task.title.toLowerCase().includes(query) ||\n        task.description?.toLowerCase().includes(query)\n      );\n    }\n\n    // Apply sorting\n    filteredTasks.sort((a, b) => {\n      const { field, direction } = state.sort;\n      let aValue: any = a[field];\n      let bValue: any = b[field];\n\n      if (field === 'dueDate') {\n        aValue = a.dueDate?.getTime() || Infinity;\n        bValue = b.dueDate?.getTime() || Infinity;\n      } else if (field === 'priority') {\n        const priorityOrder = { low: 1, medium: 2, high: 3 };\n        aValue = priorityOrder[a.priority];\n        bValue = priorityOrder[b.priority];\n      } else if (aValue instanceof Date) {\n        aValue = aValue.getTime();\n        bValue = bValue.getTime();\n      }\n\n      if (direction === 'asc') {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n\n    return filteredTasks;\n  };\n\n  const getTaskStats = () => {\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n\n    const stats = {\n      total: state.tasks.length,\n      completed: state.tasks.filter(task => task.status === 'completed').length,\n      pending: state.tasks.filter(task => task.status === 'pending').length,\n      overdue: state.tasks.filter(task => \n        task.status === 'pending' && task.dueDate && task.dueDate < today\n      ).length,\n      dueToday: state.tasks.filter(task => \n        task.status === 'pending' && task.dueDate && \n        task.dueDate >= today && task.dueDate < new Date(today.getTime() + 24 * 60 * 60 * 1000)\n      ).length,\n      dueThisWeek: state.tasks.filter(task => \n        task.status === 'pending' && task.dueDate && \n        task.dueDate >= today && task.dueDate < weekFromNow\n      ).length\n    };\n\n    return stats;\n  };\n\n  const contextValue: AppContextType = {\n    state,\n    dispatch,\n    addTask,\n    updateTask,\n    deleteTask,\n    toggleTaskStatus,\n    addCategory,\n    addTag,\n    getTaskById,\n    getCategoryById,\n    getTagById,\n    getFilteredTasks,\n    getTaskStats\n  };\n\n  return (\n    <AppContext.Provider value={contextValue}>\n      {children}\n    </AppContext.Provider>\n  );\n}\n\n// Hook to use the context\nexport function useApp() {\n  const context = useContext(AppContext);\n  if (context === undefined) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "__webpack_require__.h = () => (\"75a4dab894e07586\")"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;AAAA;AAAA;AAGA;AACA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;ACrQA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AAGA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAIA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAIA;AAEA;AAAA;AAAA;;AAKA;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAIA;AAIA;AAIA;AAKA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAIA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AC/OA;AACA;AACA;AACA;;;;;AAaA;;;AACA;;;;;;;;;;;;;AAZA;AAGA;;AACA;AACA;AACA;AAEA;AAEA;AAGA;;;;;;;;;;;;;;;AAKA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AC3EA;AAEA;AACA;;;;AAkFA;;;;;;;;AAFA;;;;;;;;;AAuCA;;;;;;;;;;;;;;;;;;;;AAPA;;;;;;;;AA4CA;;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;;;;;;;;;;;;;;;;;;;;;AAZA;;;;;;;;AAiDA;;;;;;;;AAhJA;;AACA;;;AACA;;;;;AACA;;;;;AAGA;;;;;AAWA;;;AACA;;;;AACA;AACA;AACA;;;AAOA;;;AACA;;;;AACA;AACA;AACA;;;AAOA;;;AACA;;;;;;;AAeA;;;AACA;;;;AACA;AACA;AACA;;;AAUA;;;AACA;;;;;AACA;;;;;AAKA;;;;;;;AAiBA;;;AACA;;;;;;;AA0BA;;;AACA;;;;AACA;AACA;AACA;AACA;;;AAWA;;;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1MA;AA8LA;AA7LA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAGA;AAAA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;;AAIA;AAmBA;AACA;AAUA;AACA;AA0BA;AACA;AACA;AACA;AACA;AASA;AACA;;AAqDA;AACA;AACA;AACA;AAOA;AAIA;AACA;;;;AAtIA;AAEA;AACA;AAAA;;;;;;;;;;;AA8BA;AACA;AAAA;AAAA;AAAA;AAAA;;AAGA;AACA;;AAEA;AAJA;;;;;;;;;;;AAkCA;;AAGA;AACA;AAAA;AAAA;AACA;;;AAEA;AAAA;;;;;;AACA;AAAA;;;;;;;AANA;;;;;;AAeA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;;AAEA;AAdA;;;;;;;;;;AA8CA;AACA;;;;;;;;;;;;AAMA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACrRA;AAEA;AACA;;;;AAsFA;;;;;;;;AAFA;;;;;;;;;AAuCA;;;;;;;;;;;;;;;;;;;;AAPA;;;;;;;;AA4CA;;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;;;;;;;;;;;;;;;;;;;;;AAZA;;;;;;;;AA4CA;;;;;;;;AAkBA;;;;;;;;AA7JA;;AACA;;;AACA;;;;;AACA;;;;;AAGA;;;;;AAWA;;;AACA;;;;AACA;AACA;AACA;;;AAOA;;;AACA;;;;AACA;AACA;AACA;;;AAOA;;;AACA;;;;;;;AAeA;;;AACA;;;;AACA;AACA;AACA;;;AAUA;;;AACA;;;;;AACA;;;;;AAKA;;;;;;;AAiBA;;;AACA;;;;;;;AA0BA;;;AACA;;;;AACA;AACA;AACA;AACA;;;AAUA;;;AACA;;;;;AACA;;;AACA;;;;;;;AAGA;;;;;AAQA;;;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1NA;AAiMA;AAhMA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAGA;AAAA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;;AAIA;AAmBA;AACA;AAUA;AACA;AA0BA;AACA;AACA;AACA;AACA;AASA;AACA;;AAqDA;AACA;AACA;AACA;AAoBA;AAIA;AACA;;;;AAnJA;AAEA;AACA;AAAA;;;;;;;;;;;AA8BA;AACA;AAAA;AAAA;AAAA;AAAA;;AAGA;AACA;;AAEA;AAJA;;;;;;;;;;;AAkCA;;AAGA;AACA;AAAA;AAAA;AACA;;;AAEA;AAAA;;;;;;AACA;AAAA;;;;;;;AANA;;;;;;AAeA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;;AAEA;AAdA;;;;;;;;;;AAyCA;AAAA;;;;;;AAkBA;AACA;;;;;;;;;;;;AAMA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACtSA;AAEA;AACA;;;;AA+CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;;;AAQA;;;;;;;;AACA;;;;;;;;AAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA;;;;;AACA;;;;;;;;;;;;;;;;;;;;;AAMA;;;;;;;;AAYA;;AAGA;;;;;;;;;;;;;;;;;;;AATA;;;;;;;;AAkBA;;AACA;;;AAEA;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvFA;;;AAKA;;;AAEA;;;;;;;AAkBA;;;AACA;;;;;AACA;;;;;;;AAUA;;;;;;;AAkBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlFA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;;AACA;AAIA;AAAA;AAAA;AAyBA;AACA;AA4BA;AAAA;AAAA;;;AA9CA;;AACA;AACA;;AAEA;;;;;;;;;;AAIA;;AACA;AACA;;AAEA;;;;;;;AAWA;AACA;;;;;;;;AAMA;;AACA;AAAA;AAAA;;;AACA;AAAA;;;;;;AACA;AAAA;;;;;;;;;;;;;;AAKA;;AACA;;AAEA;;;;;;;AAMA;AAAA;;;;;;;AAKA;AAEA;AACA;AACA;;AAIA;AAAA;AAAA;;AAEA;AAJA;;;;AAMA;AACA;;;;;;;;AAKA;;AAKA;AAAA;AAAA;;;;AAIA;;;AAAA;;;;;;;;;;;;;;;AAOA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACxLA;AAEA;AACA;AACA;AACA;;;;AA0DA;;;;;;;;AAIA;;;;;;;;AAIA;;;;;;;;AAIA;;;;;;;;AAIA;;;;;;;;AAcA;;;;;;;;AAJA;;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;;;AAJA;;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA;;AACA;;;;;AACA;;;;;AACA;;;;;AAGA;;;AACA;;;;;;;;;;;;;;;;;;;;;;AA9CA;;;;;;;;AAsDA;;AACA;;;;;;;;;;;;;;;AAWA;;AACA;;;;;;;;;;;;;;;AAvGA;;AAEA;;;AACA;;;AACA;;;;;AACA;;;AACA;;;;;AAKA;;;AACA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAzEA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAGA;AACA;AAAA;AAGA;;AAMA;;;AAQA;AAAA;;;;;;AAIA;AAAA;;;;;;AAIA;AAAA;;;;;;AAIA;AAAA;;;;;;AAIA;AAAA;;;;;;AAOA;;AAEA;;;AAGA;;AAEA;AACA;AAGA;AACA;AAFA;;;;;;;;;;;;;;;;AAUA;;;AAGA;;AAEA;AACA;AAGA;AACA;AAFA;;;;;;;;;;;;;;;;AAUA;;AAOA;;;;;;;;;;;;;;AAQA;AAGA;AACA;AACA;;;;;;;;;;;;;AAOA;AAGA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAOA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACxNA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AA2BA;AAOA;AACA;AAEA;AACA;AACA;AACA;AAIA;AAFA;AACA;AACA;AAMA;AANA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AAIA;AAIA;AAIA;AAMA;AACA;AACA;AAAA;AAAA;;AAIA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAGA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAGA;AAIA;AAIA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;AAGA;AAEA;AACA;AACA;AACA;AAGA;AACA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;AC7TA"}
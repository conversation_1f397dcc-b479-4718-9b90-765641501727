import { useEffect, useState, useCallback, useMemo } from '@lynx-js/react'
import './App.css'

interface Task {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  dueDate?: Date;
  priority: 'low' | 'medium' | 'high';
  category?: string;
  tags: string[];
  createdAt: Date;
}

export function App(props: {
  onMounted?: () => void
}) {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [selectedPriority, setSelectedPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [filterCategory, setFilterCategory] = useState<string>('');
  const [filterPriority, setFilterPriority] = useState<string>('');
  const [filterTag, setFilterTag] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<'created' | 'priority' | 'category'>('created');

  const categories = [
    { id: 'work', name: 'Work', icon: '💼', color: '#3B82F6' },
    { id: 'personal', name: 'Personal', icon: '🏠', color: '#10B981' },
    { id: 'shopping', name: 'Shopping', icon: '🛒', color: '#F59E0B' },
    { id: 'health', name: 'Health', icon: '🏥', color: '#EF4444' },
    { id: 'learning', name: 'Learning', icon: '📚', color: '#8B5CF6' },
    { id: 'finance', name: 'Finance', icon: '💰', color: '#06B6D4' }
  ];

  const availableTags = [
    { id: 'urgent', name: 'Urgent', color: '#DC2626' },
    { id: 'important', name: 'Important', color: '#7C3AED' },
    { id: 'quick', name: 'Quick Task', color: '#059669' },
    { id: 'meeting', name: 'Meeting', color: '#2563EB' },
    { id: 'deadline', name: 'Deadline', color: '#EA580C' },
    { id: 'research', name: 'Research', color: '#0891B2' },
    { id: 'creative', name: 'Creative', color: '#C026D3' },
    { id: 'routine', name: 'Routine', color: '#65A30D' }
  ];

  useEffect(() => {
    console.info('Hello, Todo App with ReactLynx!')
    props.onMounted?.()
  }, [])

  const addTask = useCallback(() => {
    if (newTaskTitle.trim()) {
      const newTask: Task = {
        id: Date.now().toString(),
        title: newTaskTitle.trim(),
        completed: false,
        priority: selectedPriority,
        category: selectedCategory || undefined,
        tags: selectedTags,
        createdAt: new Date()
      };
      setTasks(prev => [...prev, newTask]);
      setNewTaskTitle('');
      setSelectedPriority('medium');
      setSelectedCategory('');
      setSelectedTags([]);
    }
  }, [newTaskTitle, selectedPriority, selectedCategory, selectedTags]);

  const toggleTask = useCallback((id: string) => {
    setTasks(prev => prev.map(task =>
      task.id === id ? { ...task, completed: !task.completed } : task
    ));
  }, []);

  const deleteTask = useCallback((id: string) => {
    setTasks(prev => prev.filter(task => task.id !== id));
  }, []);

  // Filter and sort tasks
  const filteredTasks = useMemo(() => {
    let filtered = [...tasks];

    // Apply category filter
    if (filterCategory) {
      filtered = filtered.filter(task => task.category === filterCategory);
    }

    // Apply priority filter
    if (filterPriority) {
      filtered = filtered.filter(task => task.priority === filterPriority);
    }

    // Apply tag filter
    if (filterTag) {
      filtered = filtered.filter(task => task.tags.includes(filterTag));
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(query) ||
        (task.description && task.description.toLowerCase().includes(query)) ||
        task.tags.some(tagId => {
          const tag = availableTags.find(t => t.id === tagId);
          return tag && tag.name.toLowerCase().includes(query);
        }) ||
        (task.category && categories.find(c => c.id === task.category)?.name.toLowerCase().includes(query))
      );
    }

    // Sort tasks
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        case 'category':
          const aCat = a.category || 'zzz';
          const bCat = b.category || 'zzz';
          return aCat.localeCompare(bCat);
        case 'created':
        default:
          return b.createdAt.getTime() - a.createdAt.getTime();
      }
    });

    return filtered;
  }, [tasks, filterCategory, filterPriority, filterTag, searchQuery, sortBy, availableTags, categories]);

  const pendingTasks = filteredTasks.filter(task => !task.completed);
  const completedTasks = filteredTasks.filter(task => task.completed);

  // Get task statistics by category
  const categoryStats = useMemo(() => {
    return categories.map(category => {
      const categoryTasks = tasks.filter(task => task.category === category.id);
      return {
        ...category,
        total: categoryTasks.length,
        completed: categoryTasks.filter(task => task.completed).length,
        pending: categoryTasks.filter(task => !task.completed).length
      };
    });
  }, [tasks, categories]);

  return (
    <view>
      <view className='Background' />
      <view className='App'>
        {/* Header */}
        <view className="task-list-header">
          <text className="header-title">My Todo List</text>
          <view className="task-stats">
            <view className="stat-item">
              <text className="stat-number">{tasks.length}</text>
              <text className="stat-label">Total</text>
            </view>
            <view className="stat-item">
              <text className="stat-number">{pendingTasks.length}</text>
              <text className="stat-label">Pending</text>
            </view>
            <view className="stat-item">
              <text className="stat-number">{completedTasks.length}</text>
              <text className="stat-label">Completed</text>
            </view>
          </view>
        </view>

        {/* Filter and Sort Section */}
        <view className="filter-section">
          <text className="section-title">Search & Filter</text>

          {/* Search Bar */}
          <view className="search-group">
            <text className="filter-label">Search Tasks:</text>
            <view className="search-bar">
              <view className="search-input">
                <text className="search-text">{searchQuery || 'Type to search tasks, tags, categories...'}</text>
              </view>
              <view className="search-actions">
                <view
                  className="search-button"
                  bindtap={() => setSearchQuery('')}
                >
                  <text className="search-button-text">Clear</text>
                </view>
              </view>
            </view>

            {/* Quick Search Suggestions */}
            <view className="quick-search">
              {['urgent', 'work', 'today', 'meeting', 'important'].map(suggestion => (
                <view
                  key={suggestion}
                  className="search-suggestion"
                  bindtap={() => setSearchQuery(suggestion)}
                >
                  <text className="suggestion-text">{suggestion}</text>
                </view>
              ))}
            </view>
          </view>

          {/* Category Filter */}
          <view className="filter-group">
            <text className="filter-label">Filter by Category:</text>
            <view className="filter-options">
              <view
                className={`filter-option ${!filterCategory ? 'selected' : ''}`}
                bindtap={() => setFilterCategory('')}
              >
                <text className="filter-text">All</text>
              </view>
              {categories.map(category => (
                <view
                  key={category.id}
                  className={`filter-option ${filterCategory === category.id ? 'selected' : ''}`}
                  style={{ borderColor: category.color }}
                  bindtap={() => setFilterCategory(category.id)}
                >
                  <text className="category-icon">{category.icon}</text>
                  <text className="filter-text">{category.name}</text>
                </view>
              ))}
            </view>
          </view>

          {/* Priority Filter */}
          <view className="filter-group">
            <text className="filter-label">Filter by Priority:</text>
            <view className="filter-options">
              <view
                className={`filter-option ${!filterPriority ? 'selected' : ''}`}
                bindtap={() => setFilterPriority('')}
              >
                <text className="filter-text">All</text>
              </view>
              {(['high', 'medium', 'low'] as const).map(priority => (
                <view
                  key={priority}
                  className={`filter-option ${filterPriority === priority ? 'selected' : ''}`}
                  bindtap={() => setFilterPriority(priority)}
                >
                  <text className="filter-text">{priority.toUpperCase()}</text>
                </view>
              ))}
            </view>
          </view>

          {/* Tag Filter */}
          <view className="filter-group">
            <text className="filter-label">Filter by Tag:</text>
            <view className="filter-options">
              <view
                className={`filter-option ${!filterTag ? 'selected' : ''}`}
                bindtap={() => setFilterTag('')}
              >
                <text className="filter-text">All</text>
              </view>
              {availableTags.map(tag => (
                <view
                  key={tag.id}
                  className={`filter-option ${filterTag === tag.id ? 'selected' : ''}`}
                  style={{ borderColor: tag.color }}
                  bindtap={() => setFilterTag(tag.id)}
                >
                  <text className="filter-text" style={{ color: tag.color }}>{tag.name}</text>
                </view>
              ))}
            </view>
          </view>

          {/* Sort Options */}
          <view className="filter-group">
            <text className="filter-label">Sort by:</text>
            <view className="filter-options">
              {[
                { id: 'created', label: 'Created Date' },
                { id: 'priority', label: 'Priority' },
                { id: 'category', label: 'Category' }
              ].map(option => (
                <view
                  key={option.id}
                  className={`filter-option ${sortBy === option.id ? 'selected' : ''}`}
                  bindtap={() => setSortBy(option.id as 'created' | 'priority' | 'category')}
                >
                  <text className="filter-text">{option.label}</text>
                </view>
              ))}
            </view>
          </view>

          {/* Filter Status and Clear */}
          <view className="filter-status">
            <view className="active-filters">
              {(searchQuery || filterCategory || filterPriority || filterTag) && (
                <view className="filter-summary">
                  <text className="filter-label">Active Filters:</text>
                  <view className="filter-chips">
                    {searchQuery && (
                      <view className="filter-chip">
                        <text className="chip-text">Search: "{searchQuery}"</text>
                        <text className="chip-remove" bindtap={() => setSearchQuery('')}>×</text>
                      </view>
                    )}
                    {filterCategory && (
                      <view className="filter-chip">
                        <text className="chip-text">Category: {categories.find(c => c.id === filterCategory)?.name}</text>
                        <text className="chip-remove" bindtap={() => setFilterCategory('')}>×</text>
                      </view>
                    )}
                    {filterPriority && (
                      <view className="filter-chip">
                        <text className="chip-text">Priority: {filterPriority.toUpperCase()}</text>
                        <text className="chip-remove" bindtap={() => setFilterPriority('')}>×</text>
                      </view>
                    )}
                    {filterTag && (
                      <view className="filter-chip">
                        <text className="chip-text">Tag: {availableTags.find(t => t.id === filterTag)?.name}</text>
                        <text className="chip-remove" bindtap={() => setFilterTag('')}>×</text>
                      </view>
                    )}
                  </view>
                  <view
                    className="clear-all-filters"
                    bindtap={() => {
                      setSearchQuery('');
                      setFilterCategory('');
                      setFilterPriority('');
                      setFilterTag('');
                    }}
                  >
                    <text className="clear-text">Clear All</text>
                  </view>
                </view>
              )}
            </view>

            <view className="results-count">
              <text className="count-text">
                Showing {pendingTasks.length + completedTasks.length} of {tasks.length} tasks
              </text>
            </view>
          </view>

          {/* Category Statistics */}
          <view className="category-stats">
            <text className="filter-label">Category Overview:</text>
            <view className="stats-grid">
              {categoryStats.map(stat => (
                <view key={stat.id} className="category-stat" style={{ borderColor: stat.color }}>
                  <text className="stat-icon">{stat.icon}</text>
                  <text className="stat-name">{stat.name}</text>
                  <text className="stat-count">{stat.pending}/{stat.total}</text>
                </view>
              ))}
            </view>
          </view>
        </view>

        {/* Add Task Section */}
        <view className="add-task-section">
          <text className="section-title">Add New Task</text>

          {/* Quick Templates */}
          <view className="quick-templates">
            {[
              'Buy groceries',
              'Call dentist',
              'Review project',
              'Exercise',
              'Read book'
            ].map(template => (
              <view
                key={template}
                className="template-option"
                bindtap={() => setNewTaskTitle(template)}
              >
                <text className="template-text">{template}</text>
              </view>
            ))}
          </view>

          {/* Priority Selection */}
          <view className="form-field">
            <text className="field-label">Priority</text>
            <view className="priority-options">
              {(['low', 'medium', 'high'] as const).map(priority => (
                <view
                  key={priority}
                  className={`priority-option ${selectedPriority === priority ? 'selected' : ''}`}
                  bindtap={() => setSelectedPriority(priority)}
                >
                  <text className="priority-text">{priority.toUpperCase()}</text>
                </view>
              ))}
            </view>
          </view>

          {/* Category Selection */}
          <view className="form-field">
            <text className="field-label">Category</text>
            <view className="category-options">
              <view
                className={`category-option ${!selectedCategory ? 'selected' : ''}`}
                bindtap={() => setSelectedCategory('')}
              >
                <text className="category-text">No Category</text>
              </view>
              {categories.map(category => (
                <view
                  key={category.id}
                  className={`category-option ${selectedCategory === category.id ? 'selected' : ''}`}
                  style={{ borderColor: category.color }}
                  bindtap={() => setSelectedCategory(category.id)}
                >
                  <text className="category-icon">{category.icon}</text>
                  <text className="category-text">{category.name}</text>
                </view>
              ))}
            </view>
          </view>

          {/* Tags Selection */}
          <view className="form-field">
            <text className="field-label">Tags</text>
            <view className="tag-options">
              {availableTags.map(tag => (
                <view
                  key={tag.id}
                  className={`tag-option ${selectedTags.includes(tag.id) ? 'selected' : ''}`}
                  style={{
                    backgroundColor: selectedTags.includes(tag.id) ? tag.color : 'transparent',
                    borderColor: tag.color
                  }}
                  bindtap={() => {
                    if (selectedTags.includes(tag.id)) {
                      setSelectedTags(prev => prev.filter(id => id !== tag.id));
                    } else {
                      setSelectedTags(prev => [...prev, tag.id]);
                    }
                  }}
                >
                  <text
                    className="tag-text"
                    style={{
                      color: selectedTags.includes(tag.id) ? 'white' : tag.color
                    }}
                  >
                    {tag.name}
                  </text>
                </view>
              ))}
            </view>
          </view>

          {/* Add Task Actions */}
          <view className="add-task-actions">
            <view className="simple-input">
              <text className="input-text">{newTaskTitle || 'Select a template above or tap here to enter custom task...'}</text>
            </view>
            <view
              className={`action-button primary ${!newTaskTitle.trim() ? 'disabled' : ''}`}
              bindtap={!newTaskTitle.trim() ? undefined : addTask}
            >
              <text className="button-text">Add Task</text>
            </view>
          </view>
        </view>

        {/* Task List */}
        <scroll-view className="task-list-content">
          {/* Pending Tasks */}
          {pendingTasks.length > 0 && (
            <view className="task-section">
              <text className="section-title">Pending Tasks ({pendingTasks.length})</text>
              <view className="task-items">
                {pendingTasks.map(task => {
                  const category = categories.find(c => c.id === task.category);
                  const priorityColor = task.priority === 'high' ? '#EF4444' :
                                       task.priority === 'medium' ? '#F59E0B' : '#10B981';

                  return (
                    <view key={task.id} className="task-item">
                      {/* Priority indicator */}
                      <view
                        className="priority-indicator"
                        style={{ backgroundColor: priorityColor }}
                      />

                      <view className="task-content">
                        <view className="task-header">
                          <view
                            className="task-checkbox"
                            bindtap={() => toggleTask(task.id)}
                          >
                            <text className="checkbox-text">☐</text>
                          </view>
                          <text className="task-title">{task.title}</text>
                          <view className="task-actions">
                            <text
                              className="action-button delete"
                              bindtap={() => deleteTask(task.id)}
                            >
                              🗑️
                            </text>
                          </view>
                        </view>

                        {/* Task metadata */}
                        <view className="task-metadata">
                          {/* Category */}
                          {category && (
                            <view className="task-category" style={{ backgroundColor: category.color }}>
                              <text className="category-icon">{category.icon}</text>
                              <text className="category-name">{category.name}</text>
                            </view>
                          )}

                          {/* Priority */}
                          <view className="task-priority" style={{ color: priorityColor }}>
                            <text className="priority-text">{task.priority.toUpperCase()}</text>
                          </view>

                          {/* Created date */}
                          <view className="task-date">
                            <text className="date-text">Created: {task.createdAt.toLocaleDateString()}</text>
                          </view>
                        </view>

                        {/* Tags */}
                        {task.tags.length > 0 && (
                          <view className="task-tags">
                            {task.tags.map(tagId => {
                              const tag = availableTags.find(t => t.id === tagId);
                              return tag ? (
                                <view
                                  key={tagId}
                                  className="task-tag"
                                  style={{ backgroundColor: tag.color }}
                                >
                                  <text className="tag-text">{tag.name}</text>
                                </view>
                              ) : null;
                            })}
                          </view>
                        )}
                      </view>
                    </view>
                  );
                })}
              </view>
            </view>
          )}

          {/* Completed Tasks */}
          {completedTasks.length > 0 && (
            <view className="task-section">
              <text className="section-title">Completed Tasks ({completedTasks.length})</text>
              <view className="task-items">
                {completedTasks.map(task => {
                  const category = categories.find(c => c.id === task.category);
                  const priorityColor = task.priority === 'high' ? '#EF4444' :
                                       task.priority === 'medium' ? '#F59E0B' : '#10B981';

                  return (
                    <view key={task.id} className="task-item completed">
                      {/* Priority indicator */}
                      <view
                        className="priority-indicator"
                        style={{ backgroundColor: priorityColor, opacity: 0.5 }}
                      />

                      <view className="task-content">
                        <view className="task-header">
                          <view
                            className="task-checkbox checked"
                            bindtap={() => toggleTask(task.id)}
                          >
                            <text className="checkbox-text">☑</text>
                          </view>
                          <text className="task-title completed">{task.title}</text>
                          <view className="task-actions">
                            <text
                              className="action-button delete"
                              bindtap={() => deleteTask(task.id)}
                            >
                              🗑️
                            </text>
                          </view>
                        </view>

                        {/* Task metadata */}
                        <view className="task-metadata">
                          {/* Category */}
                          {category && (
                            <view className="task-category" style={{ backgroundColor: category.color, opacity: 0.7 }}>
                              <text className="category-icon">{category.icon}</text>
                              <text className="category-name">{category.name}</text>
                            </view>
                          )}

                          {/* Priority */}
                          <view className="task-priority" style={{ color: priorityColor, opacity: 0.7 }}>
                            <text className="priority-text">{task.priority.toUpperCase()}</text>
                          </view>

                          {/* Created date */}
                          <view className="task-date">
                            <text className="date-text">Completed</text>
                          </view>
                        </view>

                        {/* Tags */}
                        {task.tags.length > 0 && (
                          <view className="task-tags">
                            {task.tags.map(tagId => {
                              const tag = availableTags.find(t => t.id === tagId);
                              return tag ? (
                                <view
                                  key={tagId}
                                  className="task-tag"
                                  style={{ backgroundColor: tag.color, opacity: 0.7 }}
                                >
                                  <text className="tag-text">{tag.name}</text>
                                </view>
                              ) : null;
                            })}
                          </view>
                        )}
                      </view>
                    </view>
                  );
                })}
              </view>
            </view>
          )}

          {/* Empty State */}
          {tasks.length === 0 && (
            <view className="empty-state">
              <text className="empty-icon">📝</text>
              <text className="empty-title">No tasks yet</text>
              <text className="empty-description">
                Select a template above to create your first task!
              </text>
            </view>
          )}
        </scroll-view>
      </view>
    </view>
  )
}

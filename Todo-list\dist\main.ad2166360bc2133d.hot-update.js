(function(){
  'use strict';
  var g = (new Function('return this;'))();
  function __init_card_bundle__(lynxCoreInject) {
    g.__bundle__holder = undefined;
    var globDynamicComponentEntry = g.globDynamicComponentEntry || '__Card__';
    var tt = lynxCoreInject.tt;
    tt.define("main.ad2166360bc2133d.hot-update.js", function(require, module, exports, __Card,setTimeout,setInterval,clearInterval,clearTimeout,NativeModules,tt,console,__Component,__ReactLynx,nativeAppId,__Behavior,LynxJSBI,lynx,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,fetch,XMLHttpRequest,__WebSocket__,webkit,Reporter,print,global,requestAnimationFrame,cancelAnimationFrame) {
lynx = lynx || {};
lynx.targetSdkVersion=lynx.targetSdkVersion||"3.2";
var Promise = lynx.Promise;
fetch = fetch || lynx.fetch;
requestAnimationFrame = requestAnimationFrame || lynx.requestAnimationFrame;
cancelAnimationFrame = cancelAnimationFrame || lynx.cancelAnimationFrame;

// This needs to be wrapped in an IIFE because it needs to be isolated against Lynx injected variables.
(() => {
// lynx chunks entries
if (!lynx.__chunk_entries__) {
  // Initialize once
  lynx.__chunk_entries__ = {};
}
if (!lynx.__chunk_entries__["main"]) {
  lynx.__chunk_entries__["main"] = globDynamicComponentEntry;
} else {
  globDynamicComponentEntry = lynx.__chunk_entries__["main"];
}

"use strict";
exports.ids = ["main"];
exports.modules = {
"(react:background)/./src/context/AppContext.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  AppProvider: () => (AppProvider),
  useApp: () => (useApp)
});
/* ESM import */var _lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/jsx-dev-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:background)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _appReducer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:background)/./src/context/appReducer.ts");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:background)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");



// Initial state
const initialSettings = {
    theme: 'light',
    defaultPriority: 'medium',
    enableNotifications: true,
    autoMarkOverdue: false,
    showCompletedTasks: true,
    taskSortOrder: {
        field: 'dueDate',
        direction: 'asc'
    }
};
const initialState = {
    tasks: [],
    categories: [
        {
            id: 'work',
            name: 'Work',
            color: '#3B82F6',
            icon: "\u{1F4BC}"
        },
        {
            id: 'personal',
            name: 'Personal',
            color: '#10B981',
            icon: "\u{1F3E0}"
        },
        {
            id: 'shopping',
            name: 'Shopping',
            color: '#F59E0B',
            icon: "\u{1F6D2}"
        },
        {
            id: 'health',
            name: 'Health',
            color: '#EF4444',
            icon: "\u{1F3E5}"
        }
    ],
    tags: [
        {
            id: 'urgent',
            name: 'Urgent',
            color: '#DC2626'
        },
        {
            id: 'important',
            name: 'Important',
            color: '#7C3AED'
        },
        {
            id: 'quick',
            name: 'Quick Task',
            color: '#059669'
        },
        {
            id: 'meeting',
            name: 'Meeting',
            color: '#2563EB'
        }
    ],
    filter: {},
    sort: {
        field: 'dueDate',
        direction: 'asc'
    },
    settings: initialSettings,
    isLoading: false
};
const AppContext = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);
function AppProvider({ children }) {
    const [state, dispatch] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(_appReducer_js__WEBPACK_IMPORTED_MODULE_2__.appReducer, initialState);
    // Load data from localStorage on mount
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const savedData = localStorage.getItem('todoApp');
        if (savedData) try {
            var _parsedData_tasks;
            const parsedData = JSON.parse(savedData);
            // Convert date strings back to Date objects
            const tasks = ((_parsedData_tasks = parsedData.tasks) === null || _parsedData_tasks === void 0 ? void 0 : _parsedData_tasks.map((task)=>{
                var _task_subtasks;
                return {
                    ...task,
                    createdAt: new Date(task.createdAt),
                    updatedAt: new Date(task.updatedAt),
                    dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
                    completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
                    subtasks: ((_task_subtasks = task.subtasks) === null || _task_subtasks === void 0 ? void 0 : _task_subtasks.map((subtask)=>({
                            ...subtask,
                            createdAt: new Date(subtask.createdAt)
                        }))) || []
                };
            })) || [];
            dispatch({
                type: 'LOAD_DATA',
                payload: {
                    tasks,
                    categories: parsedData.categories || initialState.categories,
                    tags: parsedData.tags || initialState.tags
                }
            });
        } catch (error1) {
            console.error('Failed to load saved data:', error1);
        }
    }, []);
    // Save data to localStorage whenever state changes
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{
        const dataToSave = {
            tasks: state.tasks,
            categories: state.categories,
            tags: state.tags,
            settings: state.settings
        };
        localStorage.setItem('todoApp', JSON.stringify(dataToSave));
    }, [
        state.tasks,
        state.categories,
        state.tags,
        state.settings
    ]);
    // Helper functions
    const addTask = (task)=>{
        dispatch({
            type: 'ADD_TASK',
            payload: task
        });
    };
    const updateTask = (id, updates)=>{
        dispatch({
            type: 'UPDATE_TASK',
            payload: {
                id,
                updates
            }
        });
    };
    const deleteTask = (id)=>{
        dispatch({
            type: 'DELETE_TASK',
            payload: id
        });
    };
    const toggleTaskStatus = (id)=>{
        dispatch({
            type: 'TOGGLE_TASK_STATUS',
            payload: id
        });
    };
    const addCategory = (category)=>{
        dispatch({
            type: 'ADD_CATEGORY',
            payload: category
        });
    };
    const addTag = (tag)=>{
        dispatch({
            type: 'ADD_TAG',
            payload: tag
        });
    };
    const getTaskById = (id)=>{
        return state.tasks.find((task)=>task.id === id);
    };
    const getCategoryById = (id)=>{
        return state.categories.find((category)=>category.id === id);
    };
    const getTagById = (id)=>{
        return state.tags.find((tag)=>tag.id === id);
    };
    const getFilteredTasks = ()=>{
        let filteredTasks = [
            ...state.tasks
        ];
        const { filter } = state;
        // Apply filters
        if (filter.status && filter.status.length > 0) filteredTasks = filteredTasks.filter((task)=>filter.status.includes(task.status));
        if (filter.priority && filter.priority.length > 0) filteredTasks = filteredTasks.filter((task)=>filter.priority.includes(task.priority));
        if (filter.categoryId) filteredTasks = filteredTasks.filter((task)=>task.categoryId === filter.categoryId);
        if (filter.tagIds && filter.tagIds.length > 0) filteredTasks = filteredTasks.filter((task)=>filter.tagIds.some((tagId)=>task.tags.includes(tagId)));
        if (filter.searchQuery) {
            const query = filter.searchQuery.toLowerCase();
            filteredTasks = filteredTasks.filter((task)=>{
                var _task_description;
                return task.title.toLowerCase().includes(query) || ((_task_description = task.description) === null || _task_description === void 0 ? void 0 : _task_description.toLowerCase().includes(query));
            });
        }
        // Apply sorting
        filteredTasks.sort((a, b)=>{
            const { field, direction } = state.sort;
            let aValue = a[field];
            let bValue = b[field];
            if (field === 'dueDate') {
                var _a_dueDate, _b_dueDate;
                aValue = ((_a_dueDate = a.dueDate) === null || _a_dueDate === void 0 ? void 0 : _a_dueDate.getTime()) || Infinity;
                bValue = ((_b_dueDate = b.dueDate) === null || _b_dueDate === void 0 ? void 0 : _b_dueDate.getTime()) || Infinity;
            } else if (field === 'priority') {
                const priorityOrder = {
                    low: 1,
                    medium: 2,
                    high: 3
                };
                aValue = priorityOrder[a.priority];
                bValue = priorityOrder[b.priority];
            } else if (aValue instanceof Date) {
                aValue = aValue.getTime();
                bValue = bValue.getTime();
            }
            if (direction === 'asc') return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
            else return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        });
        return filteredTasks;
    };
    const getTaskStats = ()=>{
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const weekFromNow = new Date(today.getTime() + 604800000);
        const stats = {
            total: state.tasks.length,
            completed: state.tasks.filter((task)=>task.status === 'completed').length,
            pending: state.tasks.filter((task)=>task.status === 'pending').length,
            overdue: state.tasks.filter((task)=>task.status === 'pending' && task.dueDate && task.dueDate < today).length,
            dueToday: state.tasks.filter((task)=>task.status === 'pending' && task.dueDate && task.dueDate >= today && task.dueDate < new Date(today.getTime() + 86400000)).length,
            dueThisWeek: state.tasks.filter((task)=>task.status === 'pending' && task.dueDate && task.dueDate >= today && task.dueDate < weekFromNow).length
        };
        return stats;
    };
    const contextValue = {
        state,
        dispatch,
        addTask,
        updateTask,
        deleteTask,
        toggleTaskStatus,
        addCategory,
        addTag,
        getTaskById,
        getCategoryById,
        getTagById,
        getFilteredTasks,
        getTaskStats
    };
    return /*#__PURE__*/ (0,_lynx_js_react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\context\\AppContext.tsx",
        lineNumber: 253,
        columnNumber: 5
    }, this);
}
// Hook to use the context
function useApp() {
    const context = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext);
    if (context === undefined) throw new Error('useApp must be used within an AppProvider');
    return context;
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}


}),

};
exports.runtime = function(__webpack_require__) {
// webpack/runtime/get_full_hash
(() => {
__webpack_require__.h = () => ("2204caf890eedfe7")
})();

}
;
;

})();
    });
    return tt.require("main.ad2166360bc2133d.hot-update.js");
  };
  if (g && g.bundleSupportLoadScript){
    var res = {init: __init_card_bundle__};
    g.__bundle__holder = res;
    return res;
  } else {
    __init_card_bundle__({"tt": tt});
  };
})();

//# sourceMappingURL=http://**************:3000/main.ad2166360bc2133d.hot-update.js.map
{"version": 3, "file": "main.19c32eda7916d8b3.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.css", "file://webpack/runtime/get_full_hash", "file://webpack/runtime/lynx css hot update"], "sourcesContent": ["// extracted by mini-css-extract-plugin\nexport {};\n  if (module.hot) {\n    (function() {\n      var localsJsonString = undefined;\n      // 1752249078275\n      var cssReload = require(\"../node_modules/@lynx-js/css-extract-webpack-plugin/runtime/hotModuleReplacement.cjs\")(module.id, {}, \"\");\n      // only invalidate when locals change\n      if (\n        module.hot.data &&\n        module.hot.data.value &&\n        module.hot.data.value !== localsJsonString\n      ) {\n        module.hot.invalidate();\n      } else {\n        module.hot.accept();\n      }\n      module.hot.dispose(function(data) {\n        data.value = localsJsonString;\n        cssReload();\n      });\n    })();\n  }", "__webpack_require__.h = () => (\"e395bb1a08a8bfe5\")", "\n__webpack_require__.cssHotUpdateList = [[\"main\",\".rspeedy/main/main.19c32eda7916d8b3.css.hot-update.json\"]];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACtBA;;;;ACAA;AACA"}
(function(){
  'use strict';
  var g = (new Function('return this;'))();
  function __init_card_bundle__(lynxCoreInject) {
    g.__bundle__holder = undefined;
    var globDynamicComponentEntry = g.globDynamicComponentEntry || '__Card__';
    var tt = lynxCoreInject.tt;
    tt.define("main__main-thread.7fe26f9bd98b1dc6.hot-update.js", function(require, module, exports, __Card,setTimeout,setInterval,clearInterval,clearTimeout,NativeModules,tt,console,__Component,__ReactLynx,nativeAppId,__Behavior,LynxJSBI,lynx,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,fetch,XMLHttpRequest,__WebSocket__,webkit,Reporter,print,global,requestAnimationFrame,cancelAnimationFrame) {
lynx = lynx || {};
lynx.targetSdkVersion=lynx.targetSdkVersion||"3.2";
var Promise = lynx.Promise;
fetch = fetch || lynx.fetch;
requestAnimationFrame = requestAnimationFrame || lynx.requestAnimationFrame;
cancelAnimationFrame = cancelAnimationFrame || lynx.cancelAnimationFrame;

// This needs to be wrapped in an IIFE because it needs to be isolated against Lynx injected variables.
(() => {
// lynx chunks entries
if (!lynx.__chunk_entries__) {
  // Initialize once
  lynx.__chunk_entries__ = {};
}
if (!lynx.__chunk_entries__["main__main-thread"]) {
  lynx.__chunk_entries__["main__main-thread"] = globDynamicComponentEntry;
} else {
  globDynamicComponentEntry = lynx.__chunk_entries__["main__main-thread"];
}

"use strict";
exports.ids = ["main__main-thread"];
exports.modules = {
"(react:main-thread)/./src/components/AddTaskForm.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  AddTaskForm: () => (AddTaskForm)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _context_AppContext_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/context/AppContext.tsx");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");



const __snapshot_1e839_0ab4f_3 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_3", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "error-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_2 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_2", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "form-errors");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_5 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_5", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "priority-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_4 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_4", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "priority-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_7 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_7", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_8 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_8", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_6 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_6", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_10 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_10", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "tag-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[1], ctx.__values[3]);
    }
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_9 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_9", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "tag-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_11 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_11", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "button-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_0ab4f_1 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_0ab4f_1", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "add-task-form");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "form-header");
    __AppendElement(el, el1);
    const el2 = __CreateText(pageId);
    __SetClasses(el2, "form-title");
    __AppendElement(el1, el2);
    const el3 = __CreateRawText("Add New Task");
    __AppendElement(el2, el3);
    const el4 = __CreateText(pageId);
    __SetClasses(el4, "close-button");
    __AppendElement(el1, el4);
    const el5 = __CreateRawText("\u2715");
    __AppendElement(el4, el5);
    const el6 = __CreateView(pageId);
    __SetClasses(el6, "form-content");
    __AppendElement(el, el6);
    const el7 = __CreateWrapperElement(pageId);
    __AppendElement(el6, el7);
    const el8 = __CreateView(pageId);
    __SetClasses(el8, "form-field");
    __AppendElement(el6, el8);
    const el9 = __CreateText(pageId);
    __SetClasses(el9, "field-label");
    __AppendElement(el8, el9);
    const el10 = __CreateRawText("Title *");
    __AppendElement(el9, el10);
    const el11 = __CreateElement("input", pageId);
    __SetClasses(el11, "field-input");
    __SetAttribute(el11, "placeholder", "Enter task title...");
    __AppendElement(el8, el11);
    const el12 = __CreateView(pageId);
    __SetClasses(el12, "form-field");
    __AppendElement(el6, el12);
    const el13 = __CreateText(pageId);
    __SetClasses(el13, "field-label");
    __AppendElement(el12, el13);
    const el14 = __CreateRawText("Description");
    __AppendElement(el13, el14);
    const el15 = __CreateElement("textarea", pageId);
    __SetClasses(el15, "field-textarea");
    __SetAttribute(el15, "placeholder", "Enter task description...");
    __AppendElement(el12, el15);
    const el16 = __CreateView(pageId);
    __SetClasses(el16, "form-field");
    __AppendElement(el6, el16);
    const el17 = __CreateText(pageId);
    __SetClasses(el17, "field-label");
    __AppendElement(el16, el17);
    const el18 = __CreateRawText("Priority");
    __AppendElement(el17, el18);
    const el19 = __CreateWrapperElement(pageId);
    __AppendElement(el16, el19);
    const el20 = __CreateView(pageId);
    __SetClasses(el20, "form-field");
    __AppendElement(el6, el20);
    const el21 = __CreateText(pageId);
    __SetClasses(el21, "field-label");
    __AppendElement(el20, el21);
    const el22 = __CreateRawText("Due Date");
    __AppendElement(el21, el22);
    const el23 = __CreateElement("input", pageId);
    __SetClasses(el23, "field-input");
    __SetAttribute(el23, "type", "date");
    __AppendElement(el20, el23);
    const el24 = __CreateView(pageId);
    __SetClasses(el24, "form-field");
    __AppendElement(el6, el24);
    const el25 = __CreateText(pageId);
    __SetClasses(el25, "field-label");
    __AppendElement(el24, el25);
    const el26 = __CreateRawText("Category");
    __AppendElement(el25, el26);
    const el27 = __CreateView(pageId);
    __SetClasses(el27, "category-options");
    __AppendElement(el24, el27);
    const el28 = __CreateView(pageId);
    __AppendElement(el27, el28);
    const el29 = __CreateText(pageId);
    __SetClasses(el29, "category-text");
    __AppendElement(el28, el29);
    const el30 = __CreateRawText("No Category");
    __AppendElement(el29, el30);
    const el31 = __CreateWrapperElement(pageId);
    __AppendElement(el27, el31);
    const el32 = __CreateView(pageId);
    __SetClasses(el32, "form-field");
    __AppendElement(el6, el32);
    const el33 = __CreateText(pageId);
    __SetClasses(el33, "field-label");
    __AppendElement(el32, el33);
    const el34 = __CreateRawText("Tags");
    __AppendElement(el33, el34);
    const el35 = __CreateWrapperElement(pageId);
    __AppendElement(el32, el35);
    const el36 = __CreateView(pageId);
    __SetClasses(el36, "form-field");
    __AppendElement(el6, el36);
    const el37 = __CreateText(pageId);
    __SetClasses(el37, "field-label");
    __AppendElement(el36, el37);
    const el38 = __CreateRawText("Estimated Duration (minutes)");
    __AppendElement(el37, el38);
    const el39 = __CreateElement("input", pageId);
    __SetClasses(el39, "field-input");
    __SetAttribute(el39, "type", "number");
    __SetAttribute(el39, "placeholder", "e.g., 30");
    __AppendElement(el36, el39);
    const el40 = __CreateView(pageId);
    __SetClasses(el40, "form-actions");
    __AppendElement(el, el40);
    const el41 = __CreateView(pageId);
    __SetClasses(el41, "action-button secondary");
    __AppendElement(el40, el41);
    const el42 = __CreateText(pageId);
    __SetClasses(el42, "button-text");
    __AppendElement(el41, el42);
    const el43 = __CreateRawText("Cancel");
    __AppendElement(el42, el43);
    const el44 = __CreateView(pageId);
    __AppendElement(el40, el44);
    const el45 = __CreateWrapperElement(pageId);
    __AppendElement(el44, el45);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17,
        el18,
        el19,
        el20,
        el21,
        el22,
        el23,
        el24,
        el25,
        el26,
        el27,
        el28,
        el29,
        el30,
        el31,
        el32,
        el33,
        el34,
        el35,
        el36,
        el37,
        el38,
        el39,
        el40,
        el41,
        el42,
        el43,
        el44,
        el45
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 4, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[11], "value", ctx.__values[1]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[11], "onInput", ctx.__values[2]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[15], "value", ctx.__values[3]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[15], "onInput", ctx.__values[4]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[23], "value", ctx.__values[5]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[23], "onInput", ctx.__values[6]);
    },
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[28], ctx.__values[7] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 28, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[39], "value", ctx.__values[9]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[39], "onInput", ctx.__values[10]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 41, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[44], ctx.__values[12] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 44, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        7
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        19
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        31
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        35
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        45
    ]
], undefined, globDynamicComponentEntry, null);
function AddTaskForm({ onClose, onTaskAdded }) {
    var _formData_estimatedDuration;
    const { addTask, state } = (0,_context_AppContext_js__WEBPACK_IMPORTED_MODULE_2__.useApp)();
    const [title, setTitle] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [priority, setPriority] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');
    const [categoryId, setCategoryId] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [selectedTags, setSelectedTags] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [isSubmitting, setIsSubmitting] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        if (!title.trim()) return;
        setIsSubmitting(true);
        try {
            // Create task with simplified data
            const taskData = {
                title: title.trim(),
                status: 'pending',
                priority,
                categoryId: categoryId || undefined,
                tags: selectedTags,
                subtasks: []
            };
            addTask(taskData);
            onTaskAdded === null || onTaskAdded === void 0 ? void 0 : onTaskAdded();
            onClose();
        } catch (error1) {
            console.error('Failed to create task:', error1);
        } finally{
            setIsSubmitting(false);
        }
    }, [
        title,
        priority,
        categoryId,
        selectedTags,
        addTask,
        onTaskAdded,
        onClose
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((tagId)=>{
        setSelectedTags((prev)=>prev.includes(tagId) ? prev.filter((id)=>id !== tagId) : [
                ...prev,
                tagId
            ]);
    }, []);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPriority)=>{
        setPriority(newPriority);
    }, []);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newCategoryId)=>{
        setCategoryId(newCategoryId);
    }, []);
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_1, {
        values: [
            1,
            formData.title,
            (e)=>handleInputChange('title', e.detail.value),
            formData.description,
            (e)=>handleInputChange('description', e.detail.value),
            formData.dueDate ? formData.dueDate.toISOString().split('T')[0] : '',
            (e)=>{
                const value = e.detail.value;
                handleInputChange('dueDate', value ? new Date(value) : undefined);
            },
            `category-option ${!formData.categoryId ? 'selected' : ''}`,
            1,
            ((_formData_estimatedDuration = formData.estimatedDuration) === null || _formData_estimatedDuration === void 0 ? void 0 : _formData_estimatedDuration.toString()) || '',
            (e)=>{
                const value = parseInt(e.detail.value);
                handleInputChange('estimatedDuration', isNaN(value) ? undefined : value);
            },
            1,
            `action-button primary ${isSubmitting ? 'disabled' : ''}`,
            1
        ],
        children: [
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: errors.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_2, {
                    children: errors.map((error1, index)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_3, {
                            children: error1
                        }, index, false, {
                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                            lineNumber: 75,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                    lineNumber: 73,
                    columnNumber: 11
                }, this)
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_4, {
                children: [
                    'low',
                    'medium',
                    'high'
                ].map((priority)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_5, {
                        values: [
                            `priority-option ${formData.priority === priority ? 'selected' : ''}`,
                            1
                        ],
                        children: priority.toUpperCase()
                    }, priority, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                        lineNumber: 107,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                lineNumber: 105,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: state.categories.map((category)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_6, {
                        values: [
                            `category-option ${formData.categoryId === category.id ? 'selected' : ''}`,
                            {
                                borderColor: category.color
                            },
                            1
                        ],
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_7, {
                                children: category.icon
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                                lineNumber: 149,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_8, {
                                children: category.name
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                                lineNumber: 150,
                                columnNumber: 17
                            }, this)
                        ]
                    }, category.id, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                        lineNumber: 143,
                        columnNumber: 15
                    }, this))
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_9, {
                children: state.tags.map((tag)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_10, {
                        values: [
                            `tag-option ${(formData.tagIds || []).includes(tag.id) ? 'selected' : ''}`,
                            {
                                backgroundColor: (formData.tagIds || []).includes(tag.id) ? tag.color : 'transparent',
                                borderColor: tag.color
                            },
                            1,
                            {
                                color: (formData.tagIds || []).includes(tag.id) ? 'white' : tag.color
                            }
                        ],
                        children: tag.name
                    }, tag.id, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                        lineNumber: 161,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                lineNumber: 159,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_0ab4f_11, {
                children: isSubmitting ? 'Creating...' : 'Create Task'
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                lineNumber: 208,
                columnNumber: 11
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
        lineNumber: 64,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),

};
exports.runtime = function(__webpack_require__) {
// webpack/runtime/get_full_hash
(() => {
__webpack_require__.h = () => ("7f49cdcf9e4f24f5")
})();

}
;
;

})();
    });
    return tt.require("main__main-thread.7fe26f9bd98b1dc6.hot-update.js");
  };
  if (g && g.bundleSupportLoadScript){
    var res = {init: __init_card_bundle__};
    g.__bundle__holder = res;
    return res;
  } else {
    __init_card_bundle__({"tt": tt});
  };
})();

//# sourceMappingURL=http://**************:3000/main__main-thread.7fe26f9bd98b1dc6.hot-update.js.map
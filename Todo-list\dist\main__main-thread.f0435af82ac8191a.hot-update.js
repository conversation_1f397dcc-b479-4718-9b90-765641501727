(function(){
  'use strict';
  var g = (new Function('return this;'))();
  function __init_card_bundle__(lynxCoreInject) {
    g.__bundle__holder = undefined;
    var globDynamicComponentEntry = g.globDynamicComponentEntry || '__Card__';
    var tt = lynxCoreInject.tt;
    tt.define("main__main-thread.f0435af82ac8191a.hot-update.js", function(require, module, exports, __Card,setTimeout,setInterval,clearInterval,clearTimeout,NativeModules,tt,console,__Component,__ReactLynx,nativeAppId,__Behavior,LynxJSBI,lynx,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,fetch,XMLHttpRequest,__WebSocket__,webkit,Reporter,print,global,requestAnimationFrame,cancelAnimationFrame) {
lynx = lynx || {};
lynx.targetSdkVersion=lynx.targetSdkVersion||"3.2";
var Promise = lynx.Promise;
fetch = fetch || lynx.fetch;
requestAnimationFrame = requestAnimationFrame || lynx.requestAnimationFrame;
cancelAnimationFrame = cancelAnimationFrame || lynx.cancelAnimationFrame;

// This needs to be wrapped in an IIFE because it needs to be isolated against Lynx injected variables.
(() => {
// lynx chunks entries
if (!lynx.__chunk_entries__) {
  // Initialize once
  lynx.__chunk_entries__ = {};
}
if (!lynx.__chunk_entries__["main__main-thread"]) {
  lynx.__chunk_entries__["main__main-thread"] = globDynamicComponentEntry;
} else {
  globDynamicComponentEntry = lynx.__chunk_entries__["main__main-thread"];
}

"use strict";
exports.ids = ["main__main-thread"];
exports.modules = {
"(react:main-thread)/./src/App.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  App: () => (App)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _App_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/App.css");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");



const __snapshot_835da_d4da4_2 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_2", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_3 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_3", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_4 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_4", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_6 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_6", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "template-option");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "template-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_5 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_5", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "quick-templates");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_8 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_8", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "priority-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_7 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_7", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "priority-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_10 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_10", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_11 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_11", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_9 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_9", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_12 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_12", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "input-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_16 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_16", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-item");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "task-content");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "task-header");
    __AppendElement(el1, el2);
    const el3 = __CreateView(pageId);
    __SetClasses(el3, "task-checkbox");
    __AppendElement(el2, el3);
    const el4 = __CreateText(pageId);
    __SetClasses(el4, "checkbox-text");
    __AppendElement(el3, el4);
    const el5 = __CreateRawText("\u2610");
    __AppendElement(el4, el5);
    const el6 = __CreateText(pageId);
    __SetClasses(el6, "task-title");
    __AppendElement(el2, el6);
    const el7 = __CreateView(pageId);
    __SetClasses(el7, "task-actions");
    __AppendElement(el2, el7);
    const el8 = __CreateText(pageId);
    __SetClasses(el8, "action-button delete");
    __AppendElement(el7, el8);
    const el9 = __CreateRawText("\u{1F5D1}\uFE0F");
    __AppendElement(el8, el9);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 3, "bindEvent", "tap", ''),
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 8, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        6
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_15 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_15", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-items");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_14 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_14", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-section");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "section-title");
    __AppendElement(el, el1);
    const el2 = __CreateRawText("Pending Tasks (");
    __AppendElement(el1, el2);
    const el3 = __CreateWrapperElement(pageId);
    __AppendElement(el1, el3);
    const el4 = __CreateRawText(")");
    __AppendElement(el1, el4);
    const el5 = __CreateWrapperElement(pageId);
    __AppendElement(el, el5);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        3
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        5
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_19 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_19", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-item completed");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "task-content");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "task-header");
    __AppendElement(el1, el2);
    const el3 = __CreateView(pageId);
    __SetClasses(el3, "task-checkbox checked");
    __AppendElement(el2, el3);
    const el4 = __CreateText(pageId);
    __SetClasses(el4, "checkbox-text");
    __AppendElement(el3, el4);
    const el5 = __CreateRawText("\u2611");
    __AppendElement(el4, el5);
    const el6 = __CreateText(pageId);
    __SetClasses(el6, "task-title completed");
    __AppendElement(el2, el6);
    const el7 = __CreateView(pageId);
    __SetClasses(el7, "task-actions");
    __AppendElement(el2, el7);
    const el8 = __CreateText(pageId);
    __SetClasses(el8, "action-button delete");
    __AppendElement(el7, el8);
    const el9 = __CreateRawText("\u{1F5D1}\uFE0F");
    __AppendElement(el8, el9);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 3, "bindEvent", "tap", ''),
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 8, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        6
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_18 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_18", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-items");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_17 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_17", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-section");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "section-title");
    __AppendElement(el, el1);
    const el2 = __CreateRawText("Completed Tasks (");
    __AppendElement(el1, el2);
    const el3 = __CreateWrapperElement(pageId);
    __AppendElement(el1, el3);
    const el4 = __CreateRawText(")");
    __AppendElement(el1, el4);
    const el5 = __CreateWrapperElement(pageId);
    __AppendElement(el, el5);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        3
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        5
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_20 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_20", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "empty-state");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "empty-icon");
    __AppendElement(el, el1);
    const el2 = __CreateRawText("\u{1F4DD}");
    __AppendElement(el1, el2);
    const el3 = __CreateText(pageId);
    __SetClasses(el3, "empty-title");
    __AppendElement(el, el3);
    const el4 = __CreateRawText("No tasks yet");
    __AppendElement(el3, el4);
    const el5 = __CreateText(pageId);
    __SetClasses(el5, "empty-description");
    __AppendElement(el, el5);
    const el6 = __CreateRawText("Select a template above to create your first task!");
    __AppendElement(el5, el6);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6
    ];
}, null, null, undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_13 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_13", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateScrollView(pageId);
    __SetClasses(el, "task-list-content");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_835da_d4da4_1 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_d4da4_1", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "Background");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "App");
    __AppendElement(el, el2);
    const el3 = __CreateView(pageId);
    __SetClasses(el3, "task-list-header");
    __AppendElement(el2, el3);
    const el4 = __CreateText(pageId);
    __SetClasses(el4, "header-title");
    __AppendElement(el3, el4);
    const el5 = __CreateRawText("My Todo List");
    __AppendElement(el4, el5);
    const el6 = __CreateView(pageId);
    __SetClasses(el6, "task-stats");
    __AppendElement(el3, el6);
    const el7 = __CreateView(pageId);
    __SetClasses(el7, "stat-item");
    __AppendElement(el6, el7);
    const el8 = __CreateWrapperElement(pageId);
    __AppendElement(el7, el8);
    const el9 = __CreateText(pageId);
    __SetClasses(el9, "stat-label");
    __AppendElement(el7, el9);
    const el10 = __CreateRawText("Total");
    __AppendElement(el9, el10);
    const el11 = __CreateView(pageId);
    __SetClasses(el11, "stat-item");
    __AppendElement(el6, el11);
    const el12 = __CreateWrapperElement(pageId);
    __AppendElement(el11, el12);
    const el13 = __CreateText(pageId);
    __SetClasses(el13, "stat-label");
    __AppendElement(el11, el13);
    const el14 = __CreateRawText("Pending");
    __AppendElement(el13, el14);
    const el15 = __CreateView(pageId);
    __SetClasses(el15, "stat-item");
    __AppendElement(el6, el15);
    const el16 = __CreateWrapperElement(pageId);
    __AppendElement(el15, el16);
    const el17 = __CreateText(pageId);
    __SetClasses(el17, "stat-label");
    __AppendElement(el15, el17);
    const el18 = __CreateRawText("Completed");
    __AppendElement(el17, el18);
    const el19 = __CreateView(pageId);
    __SetClasses(el19, "add-task-section");
    __AppendElement(el2, el19);
    const el20 = __CreateText(pageId);
    __SetClasses(el20, "section-title");
    __AppendElement(el19, el20);
    const el21 = __CreateRawText("Add New Task");
    __AppendElement(el20, el21);
    const el22 = __CreateWrapperElement(pageId);
    __AppendElement(el19, el22);
    const el23 = __CreateView(pageId);
    __SetClasses(el23, "form-field");
    __AppendElement(el19, el23);
    const el24 = __CreateText(pageId);
    __SetClasses(el24, "field-label");
    __AppendElement(el23, el24);
    const el25 = __CreateRawText("Priority");
    __AppendElement(el24, el25);
    const el26 = __CreateWrapperElement(pageId);
    __AppendElement(el23, el26);
    const el27 = __CreateView(pageId);
    __SetClasses(el27, "form-field");
    __AppendElement(el19, el27);
    const el28 = __CreateText(pageId);
    __SetClasses(el28, "field-label");
    __AppendElement(el27, el28);
    const el29 = __CreateRawText("Category");
    __AppendElement(el28, el29);
    const el30 = __CreateView(pageId);
    __SetClasses(el30, "category-options");
    __AppendElement(el27, el30);
    const el31 = __CreateView(pageId);
    __AppendElement(el30, el31);
    const el32 = __CreateText(pageId);
    __SetClasses(el32, "category-text");
    __AppendElement(el31, el32);
    const el33 = __CreateRawText("No Category");
    __AppendElement(el32, el33);
    const el34 = __CreateWrapperElement(pageId);
    __AppendElement(el30, el34);
    const el35 = __CreateView(pageId);
    __SetClasses(el35, "add-task-actions");
    __AppendElement(el19, el35);
    const el36 = __CreateView(pageId);
    __SetClasses(el36, "simple-input");
    __AppendElement(el35, el36);
    const el37 = __CreateWrapperElement(pageId);
    __AppendElement(el36, el37);
    const el38 = __CreateView(pageId);
    __AppendElement(el35, el38);
    const el39 = __CreateText(pageId);
    __SetClasses(el39, "button-text");
    __AppendElement(el38, el39);
    const el40 = __CreateRawText("Add Task");
    __AppendElement(el39, el40);
    const el41 = __CreateWrapperElement(pageId);
    __AppendElement(el2, el41);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17,
        el18,
        el19,
        el20,
        el21,
        el22,
        el23,
        el24,
        el25,
        el26,
        el27,
        el28,
        el29,
        el30,
        el31,
        el32,
        el33,
        el34,
        el35,
        el36,
        el37,
        el38,
        el39,
        el40,
        el41
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[31], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 31, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[38], ctx.__values[2] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 38, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        8
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        12
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        16
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        22
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        26
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        34
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        37
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        41
    ]
], undefined, globDynamicComponentEntry, null);
function App(props) {
    const [tasks, setTasks] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [newTaskTitle, setNewTaskTitle] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [selectedPriority, setSelectedPriority] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');
    const [selectedCategory, setSelectedCategory] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const categories = [
        {
            id: 'work',
            name: 'Work',
            icon: "\u{1F4BC}",
            color: '#3B82F6'
        },
        {
            id: 'personal',
            name: 'Personal',
            icon: "\u{1F3E0}",
            color: '#10B981'
        },
        {
            id: 'shopping',
            name: 'Shopping',
            icon: "\u{1F6D2}",
            color: '#F59E0B'
        },
        {
            id: 'health',
            name: 'Health',
            icon: "\u{1F3E5}",
            color: '#EF4444'
        }
    ];
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useEffect)();
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        if (newTaskTitle.trim()) {
            const newTask = {
                id: Date.now().toString(),
                title: newTaskTitle.trim(),
                completed: false,
                priority: selectedPriority,
                category: selectedCategory || undefined,
                createdAt: new Date()
            };
            setTasks((prev)=>[
                    ...prev,
                    newTask
                ]);
            setNewTaskTitle('');
            setSelectedPriority('medium');
            setSelectedCategory('');
        }
    }, [
        newTaskTitle,
        selectedPriority,
        selectedCategory
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{
        setTasks((prev)=>prev.map((task)=>task.id === id ? {
                    ...task,
                    completed: !task.completed
                } : task));
    }, []);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{
        setTasks((prev)=>prev.filter((task)=>task.id !== id));
    }, []);
    const pendingTasks = tasks.filter((task)=>!task.completed);
    const completedTasks = tasks.filter((task)=>task.completed);
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_1, {
        values: [
            `category-option ${!selectedCategory ? 'selected' : ''}`,
            1,
            `action-button primary ${!newTaskTitle.trim() ? 'disabled' : ''}`,
            1
        ],
        children: [
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_2, {
                children: tasks.length
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 74,
                columnNumber: 15
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_3, {
                children: pendingTasks.length
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 78,
                columnNumber: 15
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_4, {
                children: completedTasks.length
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 82,
                columnNumber: 15
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_5, {
                children: [
                    'Buy groceries',
                    'Call dentist',
                    'Review project',
                    'Exercise',
                    'Read book'
                ].map((template)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_6, {
                        values: [
                            1
                        ],
                        children: template
                    }, template, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 101,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 93,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_7, {
                children: [
                    'low',
                    'medium',
                    'high'
                ].map((priority)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_8, {
                        values: [
                            `priority-option ${selectedPriority === priority ? 'selected' : ''}`,
                            1
                        ],
                        children: priority.toUpperCase()
                    }, priority, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 116,
                        columnNumber: 17
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 114,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: categories.map((category)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_9, {
                        values: [
                            `category-option ${selectedCategory === category.id ? 'selected' : ''}`,
                            {
                                borderColor: category.color
                            },
                            1
                        ],
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_10, {
                                children: category.icon
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 144,
                                columnNumber: 19
                            }, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_11, {
                                children: category.name
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 145,
                                columnNumber: 19
                            }, this)
                        ]
                    }, category.id, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 138,
                        columnNumber: 17
                    }, this))
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_12, {
                children: newTaskTitle || 'Select a template above or tap here to enter custom task...'
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 154,
                columnNumber: 15
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_13, {
                children: [
                    pendingTasks.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_14, {
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: pendingTasks.length
                            }, void 0, false, void 0, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_15, {
                                children: pendingTasks.map((task)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_16, {
                                        values: [
                                            1,
                                            1
                                        ],
                                        children: task.title
                                    }, task.id, false, {
                                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                        lineNumber: 173,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 171,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 169,
                        columnNumber: 13
                    }, this),
                    completedTasks.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_17, {
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: completedTasks.length
                            }, void 0, false, void 0, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_18, {
                                children: completedTasks.map((task)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_19, {
                                        values: [
                                            1,
                                            1
                                        ],
                                        children: task.title
                                    }, task.id, false, {
                                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                        lineNumber: 205,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                                lineNumber: 203,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 201,
                        columnNumber: 13
                    }, this),
                    tasks.length === 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_d4da4_20, {}, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                        lineNumber: 233,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 166,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),

};
exports.runtime = function(__webpack_require__) {
// webpack/runtime/get_full_hash
(() => {
__webpack_require__.h = () => ("4edc12241fe0649c")
})();
// webpack/runtime/lynx css hot update
(() => {

__webpack_require__.cssHotUpdateList = [["main__main-thread",".rspeedy/main__main-thread/main__main-thread.f0435af82ac8191a.css.hot-update.json"]];

})();

}
;
;

})();
    });
    return tt.require("main__main-thread.f0435af82ac8191a.hot-update.js");
  };
  if (g && g.bundleSupportLoadScript){
    var res = {init: __init_card_bundle__};
    g.__bundle__holder = res;
    return res;
  } else {
    __init_card_bundle__({"tt": tt});
  };
})();

//# sourceMappingURL=http://**************:3000/main__main-thread.f0435af82ac8191a.hot-update.js.map
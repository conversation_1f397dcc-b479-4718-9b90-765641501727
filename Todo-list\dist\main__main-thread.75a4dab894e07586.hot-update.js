(function(){
  'use strict';
  var g = (new Function('return this;'))();
  function __init_card_bundle__(lynxCoreInject) {
    g.__bundle__holder = undefined;
    var globDynamicComponentEntry = g.globDynamicComponentEntry || '__Card__';
    var tt = lynxCoreInject.tt;
    tt.define("main__main-thread.75a4dab894e07586.hot-update.js", function(require, module, exports, __Card,setTimeout,setInterval,clearInterval,clearTimeout,NativeModules,tt,console,__Component,__ReactLynx,nativeAppId,__Behavior,LynxJSBI,lynx,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,fetch,XMLHttpRequest,__WebSocket__,webkit,Reporter,print,global,requestAnimationFrame,cancelAnimationFrame) {
lynx = lynx || {};
lynx.targetSdkVersion=lynx.targetSdkVersion||"3.2";
var Promise = lynx.Promise;
fetch = fetch || lynx.fetch;
requestAnimationFrame = requestAnimationFrame || lynx.requestAnimationFrame;
cancelAnimationFrame = cancelAnimationFrame || lynx.cancelAnimationFrame;

// This needs to be wrapped in an IIFE because it needs to be isolated against Lynx injected variables.
(() => {
// lynx chunks entries
if (!lynx.__chunk_entries__) {
  // Initialize once
  lynx.__chunk_entries__ = {};
}
if (!lynx.__chunk_entries__["main__main-thread"]) {
  lynx.__chunk_entries__["main__main-thread"] = globDynamicComponentEntry;
} else {
  globDynamicComponentEntry = lynx.__chunk_entries__["main__main-thread"];
}

"use strict";
exports.ids = ["main__main-thread"];
exports.modules = {
"(react:main-thread)/./src/App.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  App: () => (App)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _App_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/App.css");
/* ESM import */var _context_AppContext_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("(react:main-thread)/./src/context/AppContext.tsx");
/* ESM import */var _components_TaskList_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("(react:main-thread)/./src/components/TaskList.tsx");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");





const __snapshot_835da_93962_1 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_93962_1", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "Background");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "App");
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        2
    ]
], undefined, globDynamicComponentEntry, null);
function App(props) {
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useEffect)();
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AppContext_js__WEBPACK_IMPORTED_MODULE_3__.AppProvider, {
        children: /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_93962_1, {
            children: /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TaskList_js__WEBPACK_IMPORTED_MODULE_4__.TaskList, {}, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 19,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
            lineNumber: 16,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),

};
exports.runtime = function(__webpack_require__) {
// webpack/runtime/get_full_hash
(() => {
__webpack_require__.h = () => ("c6fc495dbb83e1b0")
})();

}
;
;

})();
    });
    return tt.require("main__main-thread.75a4dab894e07586.hot-update.js");
  };
  if (g && g.bundleSupportLoadScript){
    var res = {init: __init_card_bundle__};
    g.__bundle__holder = res;
    return res;
  } else {
    __init_card_bundle__({"tt": tt});
  };
})();

//# sourceMappingURL=http://**************:3000/main__main-thread.75a4dab894e07586.hot-update.js.map
{"version": 3, "file": "main.2204caf890eedfe7.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\context\\appReducer.ts", "file://webpack/runtime/get_full_hash"], "sourcesContent": ["import type { AppState, AppAction, Task, Category, Tag } from '../types/index.js';\nimport { generateId } from '../utils/helpers.js';\n\nexport function appReducer(state: AppState, action: AppAction): AppState {\n  switch (action.type) {\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n\n    case 'SET_ERROR':\n      return {\n        ...state,\n        error: action.payload\n      };\n\n    case 'ADD_TASK': {\n      const now = new Date();\n      const newTask: Task = {\n        ...action.payload,\n        id: generateId(),\n        createdAt: now,\n        updatedAt: now,\n        status: action.payload.status || 'pending',\n        priority: action.payload.priority || 'medium',\n        tags: action.payload.tags || [],\n        subtasks: action.payload.subtasks || []\n      };\n\n      return {\n        ...state,\n        tasks: [...state.tasks, newTask]\n      };\n    }\n\n    case 'UPDATE_TASK': {\n      const { id, updates } = action.payload;\n      return {\n        ...state,\n        tasks: state.tasks.map(task =>\n          task.id === id\n            ? { ...task, ...updates, updatedAt: new Date() }\n            : task\n        )\n      };\n    }\n\n    case 'DELETE_TASK':\n      return {\n        ...state,\n        tasks: state.tasks.filter(task => task.id !== action.payload),\n        selectedTaskId: state.selectedTaskId === action.payload ? undefined : state.selectedTaskId\n      };\n\n    case 'TOGGLE_TASK_STATUS': {\n      const taskId = action.payload;\n      return {\n        ...state,\n        tasks: state.tasks.map(task => {\n          if (task.id === taskId) {\n            const newStatus = task.status === 'completed' ? 'pending' : 'completed';\n            const now = new Date();\n            return {\n              ...task,\n              status: newStatus,\n              completedAt: newStatus === 'completed' ? now : undefined,\n              updatedAt: now\n            };\n          }\n          return task;\n        })\n      };\n    }\n\n    case 'ADD_SUBTASK': {\n      const { taskId, subtask } = action.payload;\n      const newSubtask = {\n        ...subtask,\n        id: generateId(),\n        createdAt: new Date()\n      };\n\n      return {\n        ...state,\n        tasks: state.tasks.map(task =>\n          task.id === taskId\n            ? {\n                ...task,\n                subtasks: [...task.subtasks, newSubtask],\n                updatedAt: new Date()\n              }\n            : task\n        )\n      };\n    }\n\n    case 'UPDATE_SUBTASK': {\n      const { taskId, subtaskId, updates } = action.payload;\n      return {\n        ...state,\n        tasks: state.tasks.map(task =>\n          task.id === taskId\n            ? {\n                ...task,\n                subtasks: task.subtasks.map(subtask =>\n                  subtask.id === subtaskId\n                    ? { ...subtask, ...updates }\n                    : subtask\n                ),\n                updatedAt: new Date()\n              }\n            : task\n        )\n      };\n    }\n\n    case 'DELETE_SUBTASK': {\n      const { taskId, subtaskId } = action.payload;\n      return {\n        ...state,\n        tasks: state.tasks.map(task =>\n          task.id === taskId\n            ? {\n                ...task,\n                subtasks: task.subtasks.filter(subtask => subtask.id !== subtaskId),\n                updatedAt: new Date()\n              }\n            : task\n        )\n      };\n    }\n\n    case 'ADD_CATEGORY': {\n      const newCategory: Category = {\n        ...action.payload,\n        id: generateId()\n      };\n\n      return {\n        ...state,\n        categories: [...state.categories, newCategory]\n      };\n    }\n\n    case 'UPDATE_CATEGORY': {\n      const { id, updates } = action.payload;\n      return {\n        ...state,\n        categories: state.categories.map(category =>\n          category.id === id\n            ? { ...category, ...updates }\n            : category\n        )\n      };\n    }\n\n    case 'DELETE_CATEGORY': {\n      const categoryId = action.payload;\n      return {\n        ...state,\n        categories: state.categories.filter(category => category.id !== categoryId),\n        // Remove category from tasks\n        tasks: state.tasks.map(task =>\n          task.categoryId === categoryId\n            ? { ...task, categoryId: undefined, updatedAt: new Date() }\n            : task\n        )\n      };\n    }\n\n    case 'ADD_TAG': {\n      const newTag: Tag = {\n        ...action.payload,\n        id: generateId()\n      };\n\n      return {\n        ...state,\n        tags: [...state.tags, newTag]\n      };\n    }\n\n    case 'UPDATE_TAG': {\n      const { id, updates } = action.payload;\n      return {\n        ...state,\n        tags: state.tags.map(tag =>\n          tag.id === id\n            ? { ...tag, ...updates }\n            : tag\n        )\n      };\n    }\n\n    case 'DELETE_TAG': {\n      const tagId = action.payload;\n      return {\n        ...state,\n        tags: state.tags.filter(tag => tag.id !== tagId),\n        // Remove tag from tasks\n        tasks: state.tasks.map(task => ({\n          ...task,\n          tags: task.tags.filter(id => id !== tagId),\n          updatedAt: task.tags.includes(tagId) ? new Date() : task.updatedAt\n        }))\n      };\n    }\n\n    case 'SET_FILTER':\n      return {\n        ...state,\n        filter: { ...state.filter, ...action.payload }\n      };\n\n    case 'CLEAR_FILTER':\n      return {\n        ...state,\n        filter: {}\n      };\n\n    case 'SET_SORT':\n      return {\n        ...state,\n        sort: action.payload\n      };\n\n    case 'SELECT_TASK':\n      return {\n        ...state,\n        selectedTaskId: action.payload\n      };\n\n    case 'UPDATE_SETTINGS':\n      return {\n        ...state,\n        settings: { ...state.settings, ...action.payload }\n      };\n\n    case 'LOAD_DATA': {\n      const { tasks, categories, tags } = action.payload;\n      return {\n        ...state,\n        tasks,\n        categories,\n        tags\n      };\n    }\n\n    case 'RESET_APP':\n      return {\n        ...state,\n        tasks: [],\n        categories: state.categories, // Keep default categories\n        tags: state.tags, // Keep default tags\n        filter: {},\n        selectedTaskId: undefined,\n        error: undefined\n      };\n\n    default:\n      return state;\n  }\n}\n", "__webpack_require__.h = () => (\"97a164f40675830d\")"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;AAAA;AAAA;AAGA;AACA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAGA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;ACvQA"}
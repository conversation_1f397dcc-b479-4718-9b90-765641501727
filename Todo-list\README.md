# Todo List Application

A modern, feature-rich todo list application built with Lynx React framework and styled with modern CSS.

## Features

### Core Functionality
- ✅ Add, edit, and delete tasks
- ✅ Mark tasks as complete/incomplete
- ✅ Task persistence with localStorage
- ✅ Real-time task statistics

### Advanced Features
- 🏷️ **Categories**: Organize tasks by Work, Personal, Shopping, Health, Learning, Finance
- 🎯 **Priority Levels**: High, Medium, Low priority tasks with color coding
- 🏷️ **Tags**: Add multiple tags to tasks (Urgent, Important, Quick Task, etc.)
- 📋 **Subtasks**: Break down complex tasks into smaller subtasks
- 🔄 **Recurring Tasks**: Daily, weekly, or monthly recurring tasks
- 🔍 **Search & Filter**: Search tasks and filter by category, priority, or tags
- 📊 **Statistics**: View task completion statistics by category
- 💾 **Data Management**: Export/import tasks as JSON, clear all data

### User Experience
- 📱 **Responsive Design**: Works on desktop, tablet, and mobile devices
- 🎨 **Modern UI**: Clean, intuitive interface with smooth animations
- ⚡ **Fast Performance**: Optimized for quick task management
- 💾 **Auto-save**: All changes are automatically saved to localStorage

## Technology Stack

- **Framework**: Lynx React
- **Styling**: Modern CSS with responsive design
- **Build Tool**: Rspeedy (Rsbuild + Rspack)
- **Testing**: Vitest
- **Language**: TypeScript

## Getting Started

### Prerequisites
- Node.js 18 or higher
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Scan the QRCode in the terminal with your LynxExplorer App to see the result.

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests
- `npm run check` - Run code quality checks
- `npm run format` - Format code

## Usage

### Adding Tasks
1. Use quick templates or enter a custom task title
2. Select priority level (High, Medium, Low)
3. Choose a category (optional)
4. Add tags (optional)
5. Enable recurring if needed
6. Click "Add Task"

### Managing Tasks
- Click the checkbox to mark tasks as complete
- Click the delete button (🗑️) to remove tasks
- Use the search bar to find specific tasks
- Filter by category, priority, or tags

### Data Management
- **Export**: Download your tasks as a JSON file
- **Import**: Upload a previously exported JSON file
- **Clear**: Remove all tasks (with confirmation)

## Testing

Run the test suite:
```bash
npm test
```

Tests cover core task functionality, data operations, filtering and sorting.

## Deployment

### Production Build
```bash
npm run build
```

The built files will be in the `dist` directory and can be deployed to any static hosting service.

## License

This project is licensed under the MIT License.

{"version": 3, "file": "main.b647481b1fa3dd48.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx", "file://webpack/runtime/get_full_hash", "file://webpack/runtime/lynx css hot update"], "sourcesContent": ["import { useEffect, useState, useCallback, useMemo } from '@lynx-js/react'\nimport './App.css'\n\ninterface Task {\n  id: string;\n  title: string;\n  description?: string;\n  completed: boolean;\n  dueDate?: Date;\n  priority: 'low' | 'medium' | 'high';\n  category?: string;\n  tags: string[];\n  createdAt: Date;\n}\n\nexport function App(props: {\n  onMounted?: () => void\n}) {\n  const [tasks, setTasks] = useState<Task[]>([]);\n  const [newTaskTitle, setNewTaskTitle] = useState('');\n  const [selectedPriority, setSelectedPriority] = useState<'low' | 'medium' | 'high'>('medium');\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterPriority, setFilterPriority] = useState<string>('');\n  const [filterTag, setFilterTag] = useState<string>('');\n  const [sortBy, setSortBy] = useState<'created' | 'priority' | 'category'>('created');\n\n  const categories = [\n    { id: 'work', name: 'Work', icon: '💼', color: '#3B82F6' },\n    { id: 'personal', name: 'Personal', icon: '🏠', color: '#10B981' },\n    { id: 'shopping', name: 'Shopping', icon: '🛒', color: '#F59E0B' },\n    { id: 'health', name: 'Health', icon: '🏥', color: '#EF4444' },\n    { id: 'learning', name: 'Learning', icon: '📚', color: '#8B5CF6' },\n    { id: 'finance', name: 'Finance', icon: '💰', color: '#06B6D4' }\n  ];\n\n  useEffect(() => {\n    console.info('Hello, Todo App with ReactLynx!')\n    props.onMounted?.()\n  }, [])\n\n  const addTask = useCallback(() => {\n    if (newTaskTitle.trim()) {\n      const newTask: Task = {\n        id: Date.now().toString(),\n        title: newTaskTitle.trim(),\n        completed: false,\n        priority: selectedPriority,\n        category: selectedCategory || undefined,\n        createdAt: new Date()\n      };\n      setTasks(prev => [...prev, newTask]);\n      setNewTaskTitle('');\n      setSelectedPriority('medium');\n      setSelectedCategory('');\n    }\n  }, [newTaskTitle, selectedPriority, selectedCategory]);\n\n  const toggleTask = useCallback((id: string) => {\n    setTasks(prev => prev.map(task =>\n      task.id === id ? { ...task, completed: !task.completed } : task\n    ));\n  }, []);\n\n  const deleteTask = useCallback((id: string) => {\n    setTasks(prev => prev.filter(task => task.id !== id));\n  }, []);\n\n  // Filter and sort tasks\n  const filteredTasks = useMemo(() => {\n    let filtered = [...tasks];\n\n    // Apply category filter\n    if (filterCategory) {\n      filtered = filtered.filter(task => task.category === filterCategory);\n    }\n\n    // Apply priority filter\n    if (filterPriority) {\n      filtered = filtered.filter(task => task.priority === filterPriority);\n    }\n\n    // Sort tasks\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case 'priority':\n          const priorityOrder = { high: 3, medium: 2, low: 1 };\n          return priorityOrder[b.priority] - priorityOrder[a.priority];\n        case 'category':\n          const aCat = a.category || 'zzz';\n          const bCat = b.category || 'zzz';\n          return aCat.localeCompare(bCat);\n        case 'created':\n        default:\n          return b.createdAt.getTime() - a.createdAt.getTime();\n      }\n    });\n\n    return filtered;\n  }, [tasks, filterCategory, filterPriority, sortBy]);\n\n  const pendingTasks = filteredTasks.filter(task => !task.completed);\n  const completedTasks = filteredTasks.filter(task => task.completed);\n\n  // Get task statistics by category\n  const categoryStats = useMemo(() => {\n    return categories.map(category => {\n      const categoryTasks = tasks.filter(task => task.category === category.id);\n      return {\n        ...category,\n        total: categoryTasks.length,\n        completed: categoryTasks.filter(task => task.completed).length,\n        pending: categoryTasks.filter(task => !task.completed).length\n      };\n    });\n  }, [tasks, categories]);\n\n  return (\n    <view>\n      <view className='Background' />\n      <view className='App'>\n        {/* Header */}\n        <view className=\"task-list-header\">\n          <text className=\"header-title\">My Todo List</text>\n          <view className=\"task-stats\">\n            <view className=\"stat-item\">\n              <text className=\"stat-number\">{tasks.length}</text>\n              <text className=\"stat-label\">Total</text>\n            </view>\n            <view className=\"stat-item\">\n              <text className=\"stat-number\">{pendingTasks.length}</text>\n              <text className=\"stat-label\">Pending</text>\n            </view>\n            <view className=\"stat-item\">\n              <text className=\"stat-number\">{completedTasks.length}</text>\n              <text className=\"stat-label\">Completed</text>\n            </view>\n          </view>\n        </view>\n\n        {/* Filter and Sort Section */}\n        <view className=\"filter-section\">\n          <text className=\"section-title\">Filter & Sort</text>\n\n          {/* Category Filter */}\n          <view className=\"filter-group\">\n            <text className=\"filter-label\">Filter by Category:</text>\n            <view className=\"filter-options\">\n              <view\n                className={`filter-option ${!filterCategory ? 'selected' : ''}`}\n                bindtap={() => setFilterCategory('')}\n              >\n                <text className=\"filter-text\">All</text>\n              </view>\n              {categories.map(category => (\n                <view\n                  key={category.id}\n                  className={`filter-option ${filterCategory === category.id ? 'selected' : ''}`}\n                  style={{ borderColor: category.color }}\n                  bindtap={() => setFilterCategory(category.id)}\n                >\n                  <text className=\"category-icon\">{category.icon}</text>\n                  <text className=\"filter-text\">{category.name}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Priority Filter */}\n          <view className=\"filter-group\">\n            <text className=\"filter-label\">Filter by Priority:</text>\n            <view className=\"filter-options\">\n              <view\n                className={`filter-option ${!filterPriority ? 'selected' : ''}`}\n                bindtap={() => setFilterPriority('')}\n              >\n                <text className=\"filter-text\">All</text>\n              </view>\n              {(['high', 'medium', 'low'] as const).map(priority => (\n                <view\n                  key={priority}\n                  className={`filter-option ${filterPriority === priority ? 'selected' : ''}`}\n                  bindtap={() => setFilterPriority(priority)}\n                >\n                  <text className=\"filter-text\">{priority.toUpperCase()}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Sort Options */}\n          <view className=\"filter-group\">\n            <text className=\"filter-label\">Sort by:</text>\n            <view className=\"filter-options\">\n              {[\n                { id: 'created', label: 'Created Date' },\n                { id: 'priority', label: 'Priority' },\n                { id: 'category', label: 'Category' }\n              ].map(option => (\n                <view\n                  key={option.id}\n                  className={`filter-option ${sortBy === option.id ? 'selected' : ''}`}\n                  bindtap={() => setSortBy(option.id as 'created' | 'priority' | 'category')}\n                >\n                  <text className=\"filter-text\">{option.label}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Category Statistics */}\n          <view className=\"category-stats\">\n            <text className=\"filter-label\">Category Overview:</text>\n            <view className=\"stats-grid\">\n              {categoryStats.map(stat => (\n                <view key={stat.id} className=\"category-stat\" style={{ borderColor: stat.color }}>\n                  <text className=\"stat-icon\">{stat.icon}</text>\n                  <text className=\"stat-name\">{stat.name}</text>\n                  <text className=\"stat-count\">{stat.pending}/{stat.total}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n        </view>\n\n        {/* Add Task Section */}\n        <view className=\"add-task-section\">\n          <text className=\"section-title\">Add New Task</text>\n\n          {/* Quick Templates */}\n          <view className=\"quick-templates\">\n            {[\n              'Buy groceries',\n              'Call dentist',\n              'Review project',\n              'Exercise',\n              'Read book'\n            ].map(template => (\n              <view\n                key={template}\n                className=\"template-option\"\n                bindtap={() => setNewTaskTitle(template)}\n              >\n                <text className=\"template-text\">{template}</text>\n              </view>\n            ))}\n          </view>\n\n          {/* Priority Selection */}\n          <view className=\"form-field\">\n            <text className=\"field-label\">Priority</text>\n            <view className=\"priority-options\">\n              {(['low', 'medium', 'high'] as const).map(priority => (\n                <view\n                  key={priority}\n                  className={`priority-option ${selectedPriority === priority ? 'selected' : ''}`}\n                  bindtap={() => setSelectedPriority(priority)}\n                >\n                  <text className=\"priority-text\">{priority.toUpperCase()}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Category Selection */}\n          <view className=\"form-field\">\n            <text className=\"field-label\">Category</text>\n            <view className=\"category-options\">\n              <view\n                className={`category-option ${!selectedCategory ? 'selected' : ''}`}\n                bindtap={() => setSelectedCategory('')}\n              >\n                <text className=\"category-text\">No Category</text>\n              </view>\n              {categories.map(category => (\n                <view\n                  key={category.id}\n                  className={`category-option ${selectedCategory === category.id ? 'selected' : ''}`}\n                  style={{ borderColor: category.color }}\n                  bindtap={() => setSelectedCategory(category.id)}\n                >\n                  <text className=\"category-icon\">{category.icon}</text>\n                  <text className=\"category-text\">{category.name}</text>\n                </view>\n              ))}\n            </view>\n          </view>\n\n          {/* Add Task Actions */}\n          <view className=\"add-task-actions\">\n            <view className=\"simple-input\">\n              <text className=\"input-text\">{newTaskTitle || 'Select a template above or tap here to enter custom task...'}</text>\n            </view>\n            <view\n              className={`action-button primary ${!newTaskTitle.trim() ? 'disabled' : ''}`}\n              bindtap={!newTaskTitle.trim() ? undefined : addTask}\n            >\n              <text className=\"button-text\">Add Task</text>\n            </view>\n          </view>\n        </view>\n\n        {/* Task List */}\n        <scroll-view className=\"task-list-content\">\n          {/* Pending Tasks */}\n          {pendingTasks.length > 0 && (\n            <view className=\"task-section\">\n              <text className=\"section-title\">Pending Tasks ({pendingTasks.length})</text>\n              <view className=\"task-items\">\n                {pendingTasks.map(task => {\n                  const category = categories.find(c => c.id === task.category);\n                  const priorityColor = task.priority === 'high' ? '#EF4444' :\n                                       task.priority === 'medium' ? '#F59E0B' : '#10B981';\n\n                  return (\n                    <view key={task.id} className=\"task-item\">\n                      {/* Priority indicator */}\n                      <view\n                        className=\"priority-indicator\"\n                        style={{ backgroundColor: priorityColor }}\n                      />\n\n                      <view className=\"task-content\">\n                        <view className=\"task-header\">\n                          <view\n                            className=\"task-checkbox\"\n                            bindtap={() => toggleTask(task.id)}\n                          >\n                            <text className=\"checkbox-text\">☐</text>\n                          </view>\n                          <text className=\"task-title\">{task.title}</text>\n                          <view className=\"task-actions\">\n                            <text\n                              className=\"action-button delete\"\n                              bindtap={() => deleteTask(task.id)}\n                            >\n                              🗑️\n                            </text>\n                          </view>\n                        </view>\n\n                        {/* Task metadata */}\n                        <view className=\"task-metadata\">\n                          {/* Category */}\n                          {category && (\n                            <view className=\"task-category\" style={{ backgroundColor: category.color }}>\n                              <text className=\"category-icon\">{category.icon}</text>\n                              <text className=\"category-name\">{category.name}</text>\n                            </view>\n                          )}\n\n                          {/* Priority */}\n                          <view className=\"task-priority\" style={{ color: priorityColor }}>\n                            <text className=\"priority-text\">{task.priority.toUpperCase()}</text>\n                          </view>\n\n                          {/* Created date */}\n                          <view className=\"task-date\">\n                            <text className=\"date-text\">Created: {task.createdAt.toLocaleDateString()}</text>\n                          </view>\n                        </view>\n                      </view>\n                    </view>\n                  );\n                })}\n              </view>\n            </view>\n          )}\n\n          {/* Completed Tasks */}\n          {completedTasks.length > 0 && (\n            <view className=\"task-section\">\n              <text className=\"section-title\">Completed Tasks ({completedTasks.length})</text>\n              <view className=\"task-items\">\n                {completedTasks.map(task => {\n                  const category = categories.find(c => c.id === task.category);\n                  const priorityColor = task.priority === 'high' ? '#EF4444' :\n                                       task.priority === 'medium' ? '#F59E0B' : '#10B981';\n\n                  return (\n                    <view key={task.id} className=\"task-item completed\">\n                      {/* Priority indicator */}\n                      <view\n                        className=\"priority-indicator\"\n                        style={{ backgroundColor: priorityColor, opacity: 0.5 }}\n                      />\n\n                      <view className=\"task-content\">\n                        <view className=\"task-header\">\n                          <view\n                            className=\"task-checkbox checked\"\n                            bindtap={() => toggleTask(task.id)}\n                          >\n                            <text className=\"checkbox-text\">☑</text>\n                          </view>\n                          <text className=\"task-title completed\">{task.title}</text>\n                          <view className=\"task-actions\">\n                            <text\n                              className=\"action-button delete\"\n                              bindtap={() => deleteTask(task.id)}\n                            >\n                              🗑️\n                            </text>\n                          </view>\n                        </view>\n\n                        {/* Task metadata */}\n                        <view className=\"task-metadata\">\n                          {/* Category */}\n                          {category && (\n                            <view className=\"task-category\" style={{ backgroundColor: category.color, opacity: 0.7 }}>\n                              <text className=\"category-icon\">{category.icon}</text>\n                              <text className=\"category-name\">{category.name}</text>\n                            </view>\n                          )}\n\n                          {/* Priority */}\n                          <view className=\"task-priority\" style={{ color: priorityColor, opacity: 0.7 }}>\n                            <text className=\"priority-text\">{task.priority.toUpperCase()}</text>\n                          </view>\n\n                          {/* Created date */}\n                          <view className=\"task-date\">\n                            <text className=\"date-text\">Completed</text>\n                          </view>\n                        </view>\n                      </view>\n                    </view>\n                  );\n                })}\n              </view>\n            </view>\n          )}\n\n          {/* Empty State */}\n          {tasks.length === 0 && (\n            <view className=\"empty-state\">\n              <text className=\"empty-icon\">📝</text>\n              <text className=\"empty-title\">No tasks yet</text>\n              <text className=\"empty-description\">\n                Select a template above to create your first task!\n              </text>\n            </view>\n          )}\n        </scroll-view>\n      </view>\n    </view>\n  )\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "__webpack_require__.h = () => (\"918a96de342b1885\")", "\n__webpack_require__.cssHotUpdateList = [[\"main\",\".rspeedy/main/main.b647481b1fa3dd48.css.hot-update.json\"]];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;;AA8HA;;;;;;;;AAIA;;;;;;;;AAIA;;;;;;;;AA2BA;;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;;;;;;;;;;;;;;;AAXA;;;;;;;;AAuBA;;;;;;;;AACA;;;;;;;;AAFA;;;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALA;;;;;;;;AA2BA;;AAGA;;;;;;;;;;;;;;;;;AAbA;;;;;;;;;AA4BA;;;;;;;;;;;;;;;;;;;;AAPA;;;;;;;;AA8BA;;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA;;;;;;;;AAuCA;;;;;;;;AAgBA;;;;;;;;AACA;;;;;;;;AAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;;;;;;;;AAtCA;;AAGA;;;AAIA;;;AACA;;;AAEA;;;AAGA;;;;;;;AAGA;;;AAEA;;;;;AASA;;;;;AAUA;;;;;AAKA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlDA;;;;;;;;AAFA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;;;;;;;;AAgBA;;;;;;;;AACA;;;;;;;;AAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;;;;;;;;AAtCA;;AAGA;;;AAIA;;;AACA;;;AAEA;;;AAGA;;;;;;;AAGA;;;AAEA;;;;;AASA;;;;;AAUA;;;;;AAKA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlDA;;;;;;;;AAFA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEA;;AACA;;;;;AACA;;;;;AACA;;;;;;;;;;;;;;;;;AAxIA;;;;;;;;;AAxLA;;;AACA;;;AAEA;;;AACA;;;;;AACA;;;AACA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAMA;;;AACA;;;;;AAGA;;;AACA;;;;;AACA;;;;;AAKA;;;;;;;AAiBA;;;AACA;;;;;AACA;;;;;AAKA;;;;;;;AAeA;;;AACA;;;;;;;AAmBA;;;AACA;;;;;;;AAcA;;;AACA;;;;;;;AAsBA;;;AACA;;;;;;;AAeA;;;AACA;;;;;AACA;;;;;AAKA;;;;;;;AAiBA;;;AACA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3RA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAIA;AACA;AAIA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;;AAgCA;AACA;AAuBA;AACA;AA+FA;AACA;AAwBA;AACA;;;AAzKA;AAAA;;;;;;AAIA;AAAA;;;;;;AAIA;AAAA;;;;;;;AAoBA;;AAGA;AACA;AAAA;AAAA;AACA;;;AAEA;AAAA;;;;;;AACA;AAAA;;;;;;;AANA;;;;;;;AAsBA;AAAA;AAAA;AAAA;AAAA;;AAGA;AACA;;AAEA;AAJA;;;;;;AAaA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;AAGA;AACA;;AAEA;AAJA;;;;;;;;;;AAaA;AACA;;AACA;AAAA;AAAA;;;AACA;AAAA;;;;;;AACA;AAAA;;;;;;;AACA;;;AAAA;;;AAHA;;;;;;;;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;;AAEA;AAJA;;;;;;;;;;AAYA;AACA;AAAA;AAAA;AAAA;AAAA;;AAGA;AACA;;AAEA;AAJA;;;;;;;;;;;AAoBA;;AAGA;AACA;AAAA;AAAA;AACA;;;AAEA;AAAA;;;;;;AACA;AAAA;;;;;;;AANA;;;;;;AAeA;AAAA;;;;;;AAYA;;AAEA;;;AAEA;;AACA;AACA;AACA;AACA;AAGA;;AAKA;AAAA;AAAA;AAOA;AAQA;AAkBA;AAAA;AAAA;;;AAtBA;AAAA;;;;;;;AAcA;;AACA;AAAA;AAAA;;;AACA;AAAA;;;;;;AACA;AAAA;;;;;;;;;;;;;AAMA;AAAA;;;;;;;AAKA;;;AA3CA;;;;;AAiDA;;;;;;;;;;;;AAMA;;;AAEA;;AACA;AACA;AACA;AACA;AAGA;;AAKA;AAAA;AAAA;AAAA;AAOA;AAQA;AAkBA;AAAA;AAAA;AAAA;;;AAtBA;AAAA;;;;;;;AAcA;;AACA;AAAA;AAAA;AAAA;;;AACA;AAAA;;;;;;AACA;AAAA;;;;;;;;;;;;;AAMA;AAAA;;;;;;;AAtCA;;;;;AAiDA;;;;;;;;;;;;AAMA;;;;;;;;;;;;;;;;;AAaA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;ACrfA;;;;ACAA;AACA"}
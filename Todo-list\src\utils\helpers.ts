import { Task, Priority, TaskStatus } from '../types';

// Generate unique ID
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Format date for display
export function formatDate(date: Date): string {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

  const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  if (taskDate.getTime() === today.getTime()) {
    return 'Today';
  } else if (taskDate.getTime() === yesterday.getTime()) {
    return 'Yesterday';
  } else if (taskDate.getTime() === tomorrow.getTime()) {
    return 'Tomorrow';
  } else {
    return date.toLocaleDateString();
  }
}

// Format date and time for display
export function formatDateTime(date: Date): string {
  return `${formatDate(date)} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
}

// Check if a task is overdue
export function isTaskOverdue(task: Task): boolean {
  if (!task.dueDate || task.status === 'completed') {
    return false;
  }
  
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const taskDate = new Date(task.dueDate.getFullYear(), task.dueDate.getMonth(), task.dueDate.getDate());
  
  return taskDate < today;
}

// Check if a task is due today
export function isTaskDueToday(task: Task): boolean {
  if (!task.dueDate || task.status === 'completed') {
    return false;
  }
  
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const taskDate = new Date(task.dueDate.getFullYear(), task.dueDate.getMonth(), task.dueDate.getDate());
  
  return taskDate.getTime() === today.getTime();
}

// Check if a task is due this week
export function isTaskDueThisWeek(task: Task): boolean {
  if (!task.dueDate || task.status === 'completed') {
    return false;
  }
  
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
  
  return task.dueDate >= today && task.dueDate < weekFromNow;
}

// Get priority color
export function getPriorityColor(priority: Priority): string {
  switch (priority) {
    case 'high':
      return '#EF4444'; // Red
    case 'medium':
      return '#F59E0B'; // Orange
    case 'low':
      return '#10B981'; // Green
    default:
      return '#6B7280'; // Gray
  }
}

// Get status color
export function getStatusColor(status: TaskStatus): string {
  switch (status) {
    case 'completed':
      return '#10B981'; // Green
    case 'pending':
      return '#3B82F6'; // Blue
    case 'cancelled':
      return '#6B7280'; // Gray
    default:
      return '#6B7280'; // Gray
  }
}

// Calculate task completion percentage
export function getTaskCompletionPercentage(task: Task): number {
  if (task.status === 'completed') {
    return 100;
  }
  
  if (task.subtasks.length === 0) {
    return 0;
  }
  
  const completedSubtasks = task.subtasks.filter(subtask => subtask.completed).length;
  return Math.round((completedSubtasks / task.subtasks.length) * 100);
}

// Sort tasks by priority
export function sortTasksByPriority(tasks: Task[]): Task[] {
  const priorityOrder: Record<Priority, number> = {
    high: 3,
    medium: 2,
    low: 1
  };
  
  return [...tasks].sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
}

// Sort tasks by due date
export function sortTasksByDueDate(tasks: Task[]): Task[] {
  return [...tasks].sort((a, b) => {
    if (!a.dueDate && !b.dueDate) return 0;
    if (!a.dueDate) return 1;
    if (!b.dueDate) return -1;
    return a.dueDate.getTime() - b.dueDate.getTime();
  });
}

// Filter tasks by search query
export function filterTasksBySearch(tasks: Task[], query: string): Task[] {
  if (!query.trim()) {
    return tasks;
  }
  
  const searchTerm = query.toLowerCase().trim();
  
  return tasks.filter(task => 
    task.title.toLowerCase().includes(searchTerm) ||
    task.description?.toLowerCase().includes(searchTerm) ||
    task.subtasks.some(subtask => subtask.title.toLowerCase().includes(searchTerm))
  );
}

// Get tasks due in a specific time range
export function getTasksDueInRange(tasks: Task[], startDate: Date, endDate: Date): Task[] {
  return tasks.filter(task => {
    if (!task.dueDate || task.status === 'completed') {
      return false;
    }
    
    return task.dueDate >= startDate && task.dueDate <= endDate;
  });
}

// Create a new task with default values
export function createDefaultTask(overrides: Partial<Task> = {}): Omit<Task, 'id' | 'createdAt' | 'updatedAt'> {
  return {
    title: '',
    status: 'pending',
    priority: 'medium',
    tags: [],
    subtasks: [],
    ...overrides
  };
}

// Validate task data
export function validateTask(task: Partial<Task>): string[] {
  const errors: string[] = [];
  
  if (!task.title || task.title.trim().length === 0) {
    errors.push('Task title is required');
  }
  
  if (task.title && task.title.length > 200) {
    errors.push('Task title must be less than 200 characters');
  }
  
  if (task.description && task.description.length > 1000) {
    errors.push('Task description must be less than 1000 characters');
  }
  
  if (task.dueDate && task.dueDate < new Date()) {
    // Allow past dates for editing existing tasks
    // errors.push('Due date cannot be in the past');
  }
  
  return errors;
}

// Export task data as JSON
export function exportTasksAsJSON(tasks: Task[]): string {
  return JSON.stringify(tasks, null, 2);
}

// Import tasks from JSON
export function importTasksFromJSON(jsonString: string): Task[] {
  try {
    const data = JSON.parse(jsonString);
    
    if (!Array.isArray(data)) {
      throw new Error('Invalid format: expected an array of tasks');
    }
    
    return data.map((task: any) => ({
      ...task,
      id: task.id || generateId(),
      createdAt: new Date(task.createdAt || Date.now()),
      updatedAt: new Date(task.updatedAt || Date.now()),
      dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
      completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
      subtasks: (task.subtasks || []).map((subtask: any) => ({
        ...subtask,
        id: subtask.id || generateId(),
        createdAt: new Date(subtask.createdAt || Date.now())
      }))
    }));
  } catch (error) {
    throw new Error('Failed to parse JSON data');
  }
}

// Debounce function for search
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

{"version": 3, "file": "main__main-thread.4af1300c048f14f1.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx", "file://webpack/runtime/get_full_hash", "file://webpack/runtime/lynx css hot update"], "sourcesContent": ["import { useEffect, useState, useCallback } from '@lynx-js/react'\nimport './App.css'\n\ninterface Task {\n  id: string;\n  title: string;\n  description?: string;\n  completed: boolean;\n  dueDate?: Date;\n  priority: 'low' | 'medium' | 'high';\n  category?: string;\n  createdAt: Date;\n}\n\nexport function App(props: {\n  onMounted?: () => void\n}) {\n  const [tasks, setTasks] = useState<Task[]>([]);\n  const [newTaskTitle, setNewTaskTitle] = useState('');\n\n  useEffect(() => {\n    console.info('Hello, Todo App with ReactLynx!')\n    props.onMounted?.()\n  }, [])\n\n  const addTask = useCallback(() => {\n    if (newTaskTitle.trim()) {\n      const newTask: Task = {\n        id: Date.now().toString(),\n        title: newTaskTitle.trim(),\n        completed: false,\n        priority: 'medium',\n        createdAt: new Date()\n      };\n      setTasks(prev => [...prev, newTask]);\n      setNewTaskTitle('');\n    }\n  }, [newTaskTitle]);\n\n  const toggleTask = useCallback((id: string) => {\n    setTasks(prev => prev.map(task =>\n      task.id === id ? { ...task, completed: !task.completed } : task\n    ));\n  }, []);\n\n  const deleteTask = useCallback((id: string) => {\n    setTasks(prev => prev.filter(task => task.id !== id));\n  }, []);\n\n  const pendingTasks = tasks.filter(task => !task.completed);\n  const completedTasks = tasks.filter(task => task.completed);\n\n  return (\n    <view>\n      <view className='Background' />\n      <view className='App'>\n        {/* Header */}\n        <view className=\"task-list-header\">\n          <text className=\"header-title\">My Todo List</text>\n          <view className=\"task-stats\">\n            <view className=\"stat-item\">\n              <text className=\"stat-number\">{tasks.length}</text>\n              <text className=\"stat-label\">Total</text>\n            </view>\n            <view className=\"stat-item\">\n              <text className=\"stat-number\">{pendingTasks.length}</text>\n              <text className=\"stat-label\">Pending</text>\n            </view>\n            <view className=\"stat-item\">\n              <text className=\"stat-number\">{completedTasks.length}</text>\n              <text className=\"stat-label\">Completed</text>\n            </view>\n          </view>\n        </view>\n\n        {/* Add Task Section */}\n        <view className=\"add-task-section\">\n          <text className=\"section-title\">Add New Task</text>\n          <view className=\"quick-templates\">\n            {[\n              'Buy groceries',\n              'Call dentist',\n              'Review project',\n              'Exercise',\n              'Read book'\n            ].map(template => (\n              <view\n                key={template}\n                className=\"template-option\"\n                bindtap={() => setNewTaskTitle(template)}\n              >\n                <text className=\"template-text\">{template}</text>\n              </view>\n            ))}\n          </view>\n          <view className=\"add-task-actions\">\n            <view className=\"simple-input\">\n              <text className=\"input-text\">{newTaskTitle || 'Select a template above or tap here to enter custom task...'}</text>\n            </view>\n            <view\n              className={`action-button primary ${!newTaskTitle.trim() ? 'disabled' : ''}`}\n              bindtap={!newTaskTitle.trim() ? undefined : addTask}\n            >\n              <text className=\"button-text\">Add Task</text>\n            </view>\n          </view>\n        </view>\n\n        {/* Task List */}\n        <scroll-view className=\"task-list-content\">\n          {/* Pending Tasks */}\n          {pendingTasks.length > 0 && (\n            <view className=\"task-section\">\n              <text className=\"section-title\">Pending Tasks ({pendingTasks.length})</text>\n              <view className=\"task-items\">\n                {pendingTasks.map(task => (\n                  <view key={task.id} className=\"task-item\">\n                    <view className=\"task-content\">\n                      <view className=\"task-header\">\n                        <view\n                          className=\"task-checkbox\"\n                          bindtap={() => toggleTask(task.id)}\n                        >\n                          <text className=\"checkbox-text\">☐</text>\n                        </view>\n                        <text className=\"task-title\">{task.title}</text>\n                        <view className=\"task-actions\">\n                          <text\n                            className=\"action-button delete\"\n                            bindtap={() => deleteTask(task.id)}\n                          >\n                            🗑️\n                          </text>\n                        </view>\n                      </view>\n                    </view>\n                  </view>\n                ))}\n              </view>\n            </view>\n          )}\n\n          {/* Completed Tasks */}\n          {completedTasks.length > 0 && (\n            <view className=\"task-section\">\n              <text className=\"section-title\">Completed Tasks ({completedTasks.length})</text>\n              <view className=\"task-items\">\n                {completedTasks.map(task => (\n                  <view key={task.id} className=\"task-item completed\">\n                    <view className=\"task-content\">\n                      <view className=\"task-header\">\n                        <view\n                          className=\"task-checkbox checked\"\n                          bindtap={() => toggleTask(task.id)}\n                        >\n                          <text className=\"checkbox-text\">☑</text>\n                        </view>\n                        <text className=\"task-title completed\">{task.title}</text>\n                        <view className=\"task-actions\">\n                          <text\n                            className=\"action-button delete\"\n                            bindtap={() => deleteTask(task.id)}\n                          >\n                            🗑️\n                          </text>\n                        </view>\n                      </view>\n                    </view>\n                  </view>\n                ))}\n              </view>\n            </view>\n          )}\n\n          {/* Empty State */}\n          {tasks.length === 0 && (\n            <view className=\"empty-state\">\n              <text className=\"empty-icon\">📝</text>\n              <text className=\"empty-title\">No tasks yet</text>\n              <text className=\"empty-description\">\n                Select a template above to create your first task!\n              </text>\n            </view>\n          )}\n        </scroll-view>\n      </view>\n    </view>\n  )\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "__webpack_require__.h = () => (\"596912170dcd8ad4\")", "\n__webpack_require__.cssHotUpdateList = [[\"main__main-thread\",\".rspeedy/main__main-thread/main__main-thread.4af1300c048f14f1.css.hot-update.json\"]];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;;AA4DA;;;;;;;;AAIA;;;;;;;;AAIA;;;;;;;;AAmBA;;AAGA;;;;;;;;;;;;;;;;;AAbA;;;;;;;;AAmBA;;;;;;;;AAmBA;;AACA;;;AACA;;;AAEA;;;AAGA;;;;;AAEA;;;AACA;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdA;;;;;;;;AAFA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;;AACA;;;AACA;;;AAEA;;;AAGA;;;;;AAEA;;;AACA;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdA;;;;;;;;AAFA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;;AACA;;;;;AACA;;;;;AACA;;;;;;;;;;;;;;;;;AAtEA;;;;;;;;;AAvDA;;;AACA;;;AAEA;;;AACA;;;;;AACA;;;AACA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAMA;;;AACA;;;;;;;AAkBA;;;AACA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAzFA;AAGA;AACA;AAEA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AAEA;;AAgDA;;;;AAvCA;AAAA;;;;;;AAIA;AAAA;;;;;;AAIA;AAAA;;;;;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAMA;AAJA;;;;;;;;;;AAUA;AAAA;;;;;;AAYA;;AAEA;;;AAEA;;AACA;AACA;;;;;AAUA;AATA;;;;;;;;;;;;;;;;AA2BA;;;AAEA;;AACA;AACA;;;;;AAUA;AATA;;;;;;;;;;;;;;;;AA2BA;;;;;;;;;;;;;;;;;AAaA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AChPA;;;;ACAA;AACA"}
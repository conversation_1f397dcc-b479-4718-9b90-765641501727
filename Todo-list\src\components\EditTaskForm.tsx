import { useState, useCallback } from '@lynx-js/react';
import type { Task, Priority } from '../types/index.js';
import { useApp } from '../context/AppContext.js';

interface EditTaskFormProps {
  task: Task;
  onClose: () => void;
  onTaskUpdated?: () => void;
}

export function EditTaskForm({ task, onClose, onTaskUpdated }: EditTaskFormProps) {
  const { updateTask, state } = useApp();

  const [title, setTitle] = useState(task.title);
  const [priority, setPriority] = useState(task.priority);
  const [categoryId, setCategoryId] = useState(task.categoryId || '');
  const [selectedTags, setSelectedTags] = useState(task.tags);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = useCallback(() => {
    if (!title.trim()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Update task
      const updates = {
        title: title.trim(),
        priority,
        categoryId: categoryId || undefined,
        tags: selectedTags
      };

      updateTask(task.id, updates);
      onTaskUpdated?.();
      onClose();
    } catch (error) {
      console.error('Failed to update task:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [title, priority, categoryId, selectedTags, task.id, updateTask, onTaskUpdated, onClose]);

  const handleTagToggle = useCallback((tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  }, []);

  const handlePriorityChange = useCallback((newPriority: Priority) => {
    setPriority(newPriority);
  }, []);

  const handleCategoryChange = useCallback((newCategoryId: string) => {
    setCategoryId(newCategoryId);
  }, []);

  return (
    <view className="edit-task-form">
      <view className="form-header">
        <text className="form-title">Edit Task</text>
        <text className="close-button" bindtap={onClose}>✕</text>
      </view>

      <view className="form-content">
        {/* Title */}
        <view className="form-field">
          <text className="field-label">Task Title *</text>
          <view className="simple-input">
            <text className="input-text">{title || 'Tap to enter title...'}</text>
          </view>
        </view>

        {/* Priority Selection */}
        <view className="form-field">
          <text className="field-label">Priority</text>
          <view className="priority-options">
            {(['low', 'medium', 'high'] as Priority[]).map(priorityOption => (
              <view
                key={priorityOption}
                className={`priority-option ${priority === priorityOption ? 'selected' : ''}`}
                bindtap={() => handlePriorityChange(priorityOption)}
              >
                <text className="priority-text">{priorityOption.toUpperCase()}</text>
              </view>
            ))}
          </view>
        </view>

        {/* Category Selection */}
        <view className="form-field">
          <text className="field-label">Category</text>
          <view className="category-options">
            <view
              className={`category-option ${!categoryId ? 'selected' : ''}`}
              bindtap={() => handleCategoryChange('')}
            >
              <text className="category-text">No Category</text>
            </view>
            {state.categories.map(category => (
              <view
                key={category.id}
                className={`category-option ${categoryId === category.id ? 'selected' : ''}`}
                style={{ borderColor: category.color }}
                bindtap={() => handleCategoryChange(category.id)}
              >
                <text className="category-icon">{category.icon}</text>
                <text className="category-text">{category.name}</text>
              </view>
            ))}
          </view>
        </view>

        {/* Tags Selection */}
        <view className="form-field">
          <text className="field-label">Tags</text>
          <view className="tag-options">
            {state.tags.map(tag => (
              <view
                key={tag.id}
                className={`tag-option ${selectedTags.includes(tag.id) ? 'selected' : ''}`}
                style={{
                  backgroundColor: selectedTags.includes(tag.id) ? tag.color : 'transparent',
                  borderColor: tag.color
                }}
                bindtap={() => handleTagToggle(tag.id)}
              >
                <text
                  className="tag-text"
                  style={{
                    color: selectedTags.includes(tag.id) ? 'white' : tag.color
                  }}
                >
                  {tag.name}
                </text>
              </view>
            ))}
          </view>
        </view>

        {/* Task Status */}
        <view className="form-field">
          <text className="field-label">Status</text>
          <view className="status-info">
            <text className="status-text">
              Current status: <text className="status-value">{task.status}</text>
            </text>
            <text className="status-note">
              Use the checkbox in the task list to change completion status
            </text>
          </view>
        </view>

        {/* Quick Title Presets */}
        <view className="form-field">
          <text className="field-label">Quick Templates</text>
          <view className="quick-templates">
            {[
              'Buy groceries',
              'Call dentist',
              'Review project',
              'Exercise',
              'Read book'
            ].map(template => (
              <view
                key={template}
                className="template-option"
                bindtap={() => setTitle(template)}
              >
                <text className="template-text">{template}</text>
              </view>
            ))}
          </view>
        </view>
      </view>

      {/* Actions */}
      <view className="form-actions">
        <view className="action-button secondary" bindtap={onClose}>
          <text className="button-text">Cancel</text>
        </view>
        <view
          className={`action-button primary ${isSubmitting || !title.trim() ? 'disabled' : ''}`}
          bindtap={isSubmitting || !title.trim() ? undefined : handleSubmit}
        >
          <text className="button-text">
            {isSubmitting ? 'Updating...' : 'Update Task'}
          </text>
        </view>
      </view>
    </view>
  );
}

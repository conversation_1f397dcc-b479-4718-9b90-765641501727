/* Todo App Styles */

/* Global Styles */
:root {
  background-color: #f5f7fa;
  --color-text: #333;
}

.Background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.App {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

text {
  color: var(--color-text);
}

/* Task List Styles */
.task-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
}

.task-list-header {
  padding: 20px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-title {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
}

.add-task-button {
  background: #3b82f6;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
}

.add-button-text {
  font-weight: 600;
  color: white;
}

.task-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
  min-width: 60px;
}

@keyframes Logo--shake {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

.Content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.Arrow {
  width: 24px;
  height: 24px;
}

.Title {
  font-size: 36px;
  font-weight: 700;
}

.Subtitle {
  font-style: italic;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 8px;
}

.Description {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.85);
  margin: 15rpx;
}

.Hint {
  font-size: 12px;
  margin: 5px;
  color: rgba(255, 255, 255, 0.65);
}

/* Additional Task App Styles */
.stat-item.overdue {
  background: #fef2f2;
  color: #dc2626;
}

.stat-item.due-today {
  background: #fef3c7;
  color: #d97706;
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
}

.stat-label {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 2px;
}

.task-list-content {
  flex: 1;
  padding: 20px;
}

/* Task Section Styles */
.task-section {
  margin-bottom: 24px;
}

.section-header {
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.task-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Task Item Styles */
.task-item {
  display: flex;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
}

.task-item.completed {
  opacity: 0.7;
  background: #f9fafb;
}

.task-item.overdue {
  border-left: 4px solid #dc2626;
}

.priority-indicator {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  border-radius: 4px 0 0 4px;
}

.task-content {
  flex: 1;
  margin-left: 8px;
}

.task-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
}

.task-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;
  margin-top: 2px;
}

.task-checkbox.checked {
  background: #10b981;
  border-color: #10b981;
}

.checkmark {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.task-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  cursor: pointer;
}

.task-title.completed {
  text-decoration: line-through;
  color: #6b7280;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-button.edit {
  background: #f3f4f6;
}

.action-button.delete {
  background: #fef2f2;
}

.task-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  line-height: 1.4;
}

.task-metadata {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.task-category {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
}

.category-icon {
  font-size: 12px;
}

.category-name {
  font-weight: 500;
  color: white;
}

.task-due-date {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f3f4f6;
  border-radius: 12px;
  font-size: 12px;
}

.task-due-date.overdue {
  background: #fef2f2;
  color: #dc2626;
}

.task-due-date.due-today {
  background: #fef3c7;
  color: #d97706;
}

.due-date-icon {
  font-size: 10px;
}

.due-date-text {
  font-weight: 500;
}

.task-priority {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  background: #f3f4f6;
}

.priority-text {
  font-weight: 600;
}

.task-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.task-tag {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  color: white;
}

.tag-text {
  font-weight: 500;
  color: white;
}

.subtasks-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #10b981;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 24px;
  max-width: 300px;
  line-height: 1.5;
}

.empty-action {
  background: #3b82f6;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
}

.empty-action-text {
  font-weight: 600;
  color: white;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.add-task-form,
.edit-task-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 20px;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.close-button {
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
}

.form-content {
  flex: 1;
  padding: 0 20px;
}

.form-errors {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.error-text {
  color: #dc2626;
  font-size: 14px;
  display: block;
  margin-bottom: 4px;
}

.error-text:last-child {
  margin-bottom: 0;
}

.form-field {
  margin-bottom: 20px;
}

.field-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.field-input,
.field-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #1f2937;
  background: white;
}

.field-input:focus,
.field-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.field-textarea {
  min-height: 80px;
}

.priority-options,
.category-options,
.tag-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.priority-option,
.category-option,
.tag-option {
  padding: 8px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.priority-option.selected {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.category-option.selected {
  background: #f3f4f6;
}

.tag-option.selected {
  color: white;
}

.priority-text,
.category-text {
  font-weight: 500;
}

.status-info {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.status-text {
  font-size: 14px;
  color: #374151;
  display: block;
  margin-bottom: 4px;
}

.status-value {
  font-weight: 600;
}

.status-note {
  font-size: 12px;
  color: #6b7280;
}

.form-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
}

.action-button {
  flex: 1;
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s;
}

.action-button.primary {
  background: #3b82f6;
  color: white;
}

.action-button.primary:hover {
  background: #2563eb;
}

.action-button.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.action-button.secondary:hover {
  background: #e5e7eb;
}

.action-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button-text {
  font-weight: 600;
}

/* Simplified Form Elements */
.simple-input {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #f9fafb;
  min-height: 20px;
}

.input-text {
  font-size: 14px;
  color: #6b7280;
}

.quick-templates {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.template-option {
  padding: 8px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
}

.template-text {
  font-size: 12px;
  color: #374151;
}

/* Simplified App Styles */
.add-task-section {
  padding: 20px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
}

.add-task-actions {
  display: flex;
  gap: 12px;
  margin-top: 12px;
  align-items: center;
}

.add-task-actions .simple-input {
  flex: 1;
}

.checkbox-text {
  font-size: 16px;
  color: #374151;
}

.task-checkbox.checked .checkbox-text {
  color: #10b981;
}

.task-title.completed {
  text-decoration: line-through;
  color: #6b7280;
  opacity: 0.7;
}

/* Enhanced Task Styles */
.form-field {
  margin-bottom: 16px;
}

.field-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.priority-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.priority-option {
  padding: 8px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  background: white;
  transition: all 0.2s;
}

.priority-option.selected {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.priority-text {
  font-weight: 600;
}

.category-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-option {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  background: white;
  transition: all 0.2s;
}

.category-option.selected {
  background: #f3f4f6;
  border-color: currentColor;
}

.category-icon {
  font-size: 14px;
}

.category-text {
  font-weight: 500;
}

.task-date {
  padding: 4px 8px;
  background: #f3f4f6;
  border-radius: 12px;
  font-size: 10px;
}

.date-text {
  color: #6b7280;
  font-size: 10px;
}

/* Filter and Organization Styles */
.filter-section {
  padding: 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.filter-group {
  margin-bottom: 16px;
}

.filter-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.filter-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  background: white;
  transition: all 0.2s;
}

.filter-option.selected {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.filter-text {
  font-weight: 500;
}

.category-stats {
  margin-top: 16px;
}

.stats-grid {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.category-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  min-width: 80px;
}

.stat-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.stat-name {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 2px;
}

.stat-count {
  font-size: 14px;
  font-weight: bold;
  color: #6b7280;
}

/* Tag Styles */
.tag-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag-option {
  padding: 6px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  font-size: 11px;
  background: white;
  transition: all 0.2s;
}

.tag-option.selected {
  color: white;
}

.tag-text {
  font-weight: 500;
}

.task-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.task-tag {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
}

.task-tag .tag-text {
  color: white;
  font-weight: 500;
  font-size: 10px;
}

/* Search Styles */
.search-group {
  margin-bottom: 16px;
}

.search-bar {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.search-input {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  min-height: 20px;
}

.search-text {
  font-size: 14px;
  color: #6b7280;
}

.search-actions {
  display: flex;
  gap: 8px;
}

.search-button {
  padding: 10px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
}

.search-button-text {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.quick-search {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.search-suggestion {
  padding: 4px 8px;
  background: #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  font-size: 11px;
}

.suggestion-text {
  color: #6b7280;
  font-weight: 500;
}

/* Filter Status Styles */
.filter-status {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.filter-summary {
  margin-bottom: 12px;
}

.filter-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #3b82f6;
  color: white;
  border-radius: 12px;
  font-size: 11px;
}

.chip-text {
  color: white;
  font-weight: 500;
}

.chip-remove {
  color: white;
  font-weight: bold;
  cursor: pointer;
  padding: 0 4px;
}

.clear-all-filters {
  padding: 6px 12px;
  background: #ef4444;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: inline-block;
}

.clear-text {
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.results-count {
  text-align: center;
  padding: 8px;
}

.count-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

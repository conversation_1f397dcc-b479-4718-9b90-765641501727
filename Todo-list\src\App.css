/* Basic CSS for Todo App */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.min-h-screen { min-height: 100vh; }
.bg-gradient-to-br { background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); }
.max-w-4xl { max-width: 56rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.bg-white { background-color: white; }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.border-b { border-bottom-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.font-bold { font-weight: 700; }
.text-gray-900 { color: #111827; }
.mb-4 { margin-bottom: 1rem; }
.rounded-lg { border-radius: 0.5rem; }
.cursor-pointer { cursor: pointer; }
.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }
.hover\:bg-blue-600:hover { background-color: #2563eb; }
.bg-blue-500 { background-color: #3b82f6; }
.text-white { color: white; }
.font-semibold { font-weight: 600; }
.text-sm { font-size: 0.875rem; }
.font-medium { font-weight: 500; }
.hidden { display: none; }
.grid { display: grid; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.items-center { align-items: center; }
.text-lg { font-size: 1.125rem; }
.text-blue-600 { color: #2563eb; }
.opacity-70 { opacity: 0.7; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-yellow-50 { background-color: #fefce8; }
.bg-green-50 { background-color: #f0fdf4; }
.text-yellow-600 { color: #d97706; }
.text-green-600 { color: #16a34a; }
.text-xs { font-size: 0.75rem; }
.min-w-\[60px\] { min-width: 60px; }
.min-w-\[80px\] { min-width: 80px; }
.bg-gray-50 { background-color: #f9fafb; }
.text-gray-700 { color: #374151; }
.flex-wrap { flex-wrap: wrap; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-gray-300 { border-color: #d1d5db; }
.hover\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.bg-gray-100 { background-color: #f3f4f6; }
.text-gray-600 { color: #4b5563; }
.text-gray-500 { color: #6b7280; }
.rounded-full { border-radius: 9999px; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.relative { position: relative; }
.absolute { position: absolute; }
.left-0 { left: 0; }
.top-0 { top: 0; }
.bottom-0 { bottom: 0; }
.w-1 { width: 0.25rem; }
.rounded-l-lg { border-top-left-radius: 0.5rem; border-bottom-left-radius: 0.5rem; }
.bg-red-500 { background-color: #ef4444; }
.bg-yellow-500 { background-color: #eab308; }
.bg-green-500 { background-color: #22c55e; }
.flex-1 { flex: 1 1 0%; }
.ml-2 { margin-left: 0.5rem; }
.w-5 { width: 1.25rem; }
.h-5 { height: 1.25rem; }
.border-blue-500 { border-color: #3b82f6; }
.hover\:border-blue-500:hover { border-color: #3b82f6; }
.mt-0\.5 { margin-top: 0.125rem; }
.text-base { font-size: 1rem; }
.p-1 { padding: 0.25rem; }
.text-red-500 { color: #ef4444; }
.hover\:bg-red-50:hover { background-color: #fef2f2; }
.rounded { border-radius: 0.25rem; }

@media (min-width: 640px) {
  .sm\:px-4 { padding-left: 1rem; padding-right: 1rem; }
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:justify-between { justify-content: space-between; }
  .sm\:items-start { align-items: flex-start; }
  .sm\:text-2xl { font-size: 1.5rem; }
  .sm\:gap-2 { gap: 0.5rem; }
  .sm\:text-sm { font-size: 0.875rem; }
  .sm\:inline { display: inline; }
  .sm\:gap-4 { gap: 1rem; }
  .sm\:flex { display: flex; }
  .sm\:flex-wrap { flex-wrap: wrap; }
  .sm\:text-xl { font-size: 1.25rem; }
  .sm\:text-base { font-size: 1rem; }
  .sm\:min-w-\[80px\] { min-width: 80px; }
  .sm\:items-center { align-items: center; }
  .sm\:p-4 { padding: 1rem; }
}

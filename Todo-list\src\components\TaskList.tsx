import { useState, useCallback, useMemo } from '@lynx-js/react';
import type { Task } from '../types/index.js';
import { useApp } from '../context/AppContext.js';
import { TaskItem } from './TaskItem.js';
import { AddTaskForm } from './AddTaskForm.js';
import { EditTaskForm } from './EditTaskForm.js';

export function TaskList() {
  const { getFilteredTasks, getTaskStats } = useApp();
  
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);

  const tasks = useMemo(() => getFilteredTasks(), [getFilteredTasks]);
  const stats = useMemo(() => getTaskStats(), [getTaskStats]);

  const handleAddTask = useCallback(() => {
    setShowAddForm(true);
  }, []);

  const handleCloseAddForm = useCallback(() => {
    setShowAddForm(false);
  }, []);

  const handleEditTask = useCallback((task: Task) => {
    setEditingTask(task);
  }, []);

  const handleCloseEditForm = useCallback(() => {
    setEditingTask(null);
  }, []);

  const handleTaskAdded = useCallback(() => {
    setShowAddForm(false);
  }, []);

  const handleTaskUpdated = useCallback(() => {
    setEditingTask(null);
  }, []);

  // Group tasks by status for better organization
  const pendingTasks = useMemo(() => 
    tasks.filter(task => task.status === 'pending'), [tasks]
  );
  
  const completedTasks = useMemo(() => 
    tasks.filter(task => task.status === 'completed'), [tasks]
  );

  return (
    <view className="task-list">
      {/* Header */}
      <view className="task-list-header">
        <view className="header-content">
          <text className="header-title">My Tasks</text>
          <view className="add-task-button" bindtap={handleAddTask}>
            <text className="add-button-text">+ Add Task</text>
          </view>
        </view>
        
        {/* Stats */}
        <view className="task-stats">
          <view className="stat-item">
            <text className="stat-number">{stats.total}</text>
            <text className="stat-label">Total</text>
          </view>
          <view className="stat-item">
            <text className="stat-number">{stats.pending}</text>
            <text className="stat-label">Pending</text>
          </view>
          <view className="stat-item">
            <text className="stat-number">{stats.completed}</text>
            <text className="stat-label">Completed</text>
          </view>
          <view className="stat-item overdue">
            <text className="stat-number">{stats.overdue}</text>
            <text className="stat-label">Overdue</text>
          </view>
          <view className="stat-item due-today">
            <text className="stat-number">{stats.dueToday}</text>
            <text className="stat-label">Due Today</text>
          </view>
        </view>
      </view>

      {/* Task sections */}
      <scroll-view className="task-list-content">
        {/* Pending Tasks */}
        {pendingTasks.length > 0 && (
          <view className="task-section">
            <view className="section-header">
              <text className="section-title">Pending Tasks ({pendingTasks.length})</text>
            </view>
            <view className="task-items">
              {pendingTasks.map(task => (
                <TaskItem
                  key={task.id}
                  task={task}
                  onEdit={handleEditTask}
                />
              ))}
            </view>
          </view>
        )}

        {/* Completed Tasks */}
        {completedTasks.length > 0 && (
          <view className="task-section">
            <view className="section-header">
              <text className="section-title">Completed Tasks ({completedTasks.length})</text>
            </view>
            <view className="task-items">
              {completedTasks.map(task => (
                <TaskItem
                  key={task.id}
                  task={task}
                  onEdit={handleEditTask}
                />
              ))}
            </view>
          </view>
        )}

        {/* Empty state */}
        {tasks.length === 0 && (
          <view className="empty-state">
            <text className="empty-icon">📝</text>
            <text className="empty-title">No tasks yet</text>
            <text className="empty-description">
              Create your first task to get started with organizing your work!
            </text>
            <view className="empty-action" bindtap={handleAddTask}>
              <text className="empty-action-text">Create First Task</text>
            </view>
          </view>
        )}
      </scroll-view>

      {/* Add Task Form Modal */}
      {showAddForm && (
        <view className="modal-overlay">
          <view className="modal-content">
            <AddTaskForm
              onClose={handleCloseAddForm}
              onTaskAdded={handleTaskAdded}
            />
          </view>
        </view>
      )}

      {/* Edit Task Form Modal */}
      {editingTask && (
        <view className="modal-overlay">
          <view className="modal-content">
            <EditTaskForm
              task={editingTask}
              onClose={handleCloseEditForm}
              onTaskUpdated={handleTaskUpdated}
            />
          </view>
        </view>
      )}
    </view>
  );
}

import { useState, useCallback } from '@lynx-js/react';
import type { Priority } from '../types/index.js';
import { useApp } from '../context/AppContext.js';

interface AddTaskFormProps {
  onClose: () => void;
  onTaskAdded?: () => void;
}

export function AddTaskForm({ onClose, onTaskAdded }: AddTaskFormProps) {
  const { addTask, state } = useApp();

  const [title, setTitle] = useState('');
  const [priority, setPriority] = useState<Priority>('medium');
  const [categoryId, setCategoryId] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = useCallback(() => {
    if (!title.trim()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Create task with simplified data
      const taskData = {
        title: title.trim(),
        status: 'pending' as const,
        priority,
        categoryId: categoryId || undefined,
        tags: selectedTags,
        subtasks: []
      };

      addTask(taskData);
      onTaskAdded?.();
      onClose();
    } catch (error) {
      console.error('Failed to create task:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [title, priority, categoryId, selectedTags, addTask, onTaskAdded, onClose]);

  const handleTagToggle = useCallback((tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  }, []);

  const handlePriorityChange = useCallback((newPriority: Priority) => {
    setPriority(newPriority);
  }, []);

  const handleCategoryChange = useCallback((newCategoryId: string) => {
    setCategoryId(newCategoryId);
  }, []);

  return (
    <view className="add-task-form">
      <view className="form-header">
        <text className="form-title">Add New Task</text>
        <text className="close-button" bindtap={onClose}>✕</text>
      </view>

      <view className="form-content">
        {/* Title Input (Simplified) */}
        <view className="form-field">
          <text className="field-label">Task Title *</text>
          <view className="simple-input">
            <text className="input-text">{title || 'Tap to enter title...'}</text>
          </view>
        </view>

        {/* Priority Selection */}
        <view className="form-field">
          <text className="field-label">Priority</text>
          <view className="priority-options">
            {(['low', 'medium', 'high'] as Priority[]).map(priorityOption => (
              <view
                key={priorityOption}
                className={`priority-option ${priority === priorityOption ? 'selected' : ''}`}
                bindtap={() => handlePriorityChange(priorityOption)}
              >
                <text className="priority-text">{priorityOption.toUpperCase()}</text>
              </view>
            ))}
          </view>
        </view>

        {/* Category Selection */}
        <view className="form-field">
          <text className="field-label">Category</text>
          <view className="category-options">
            <view
              className={`category-option ${!categoryId ? 'selected' : ''}`}
              bindtap={() => handleCategoryChange('')}
            >
              <text className="category-text">No Category</text>
            </view>
            {state.categories.map(category => (
              <view
                key={category.id}
                className={`category-option ${categoryId === category.id ? 'selected' : ''}`}
                style={{ borderColor: category.color }}
                bindtap={() => handleCategoryChange(category.id)}
              >
                <text className="category-icon">{category.icon}</text>
                <text className="category-text">{category.name}</text>
              </view>
            ))}
          </view>
        </view>

        {/* Tags Selection */}
        <view className="form-field">
          <text className="field-label">Tags</text>
          <view className="tag-options">
            {state.tags.map(tag => (
              <view
                key={tag.id}
                className={`tag-option ${selectedTags.includes(tag.id) ? 'selected' : ''}`}
                style={{
                  backgroundColor: selectedTags.includes(tag.id) ? tag.color : 'transparent',
                  borderColor: tag.color
                }}
                bindtap={() => handleTagToggle(tag.id)}
              >
                <text
                  className="tag-text"
                  style={{
                    color: selectedTags.includes(tag.id) ? 'white' : tag.color
                  }}
                >
                  {tag.name}
                </text>
              </view>
            ))}
          </view>
        </view>

        {/* Quick Title Presets */}
        <view className="form-field">
          <text className="field-label">Quick Templates</text>
          <view className="quick-templates">
            {[
              'Buy groceries',
              'Call dentist',
              'Review project',
              'Exercise',
              'Read book'
            ].map(template => (
              <view
                key={template}
                className="template-option"
                bindtap={() => setTitle(template)}
              >
                <text className="template-text">{template}</text>
              </view>
            ))}
          </view>
        </view>
      </view>

      {/* Actions */}
      <view className="form-actions">
        <view className="action-button secondary" bindtap={onClose}>
          <text className="button-text">Cancel</text>
        </view>
        <view
          className={`action-button primary ${isSubmitting || !title.trim() ? 'disabled' : ''}`}
          bindtap={isSubmitting || !title.trim() ? undefined : handleSubmit}
        >
          <text className="button-text">
            {isSubmitting ? 'Creating...' : 'Create Task'}
          </text>
        </view>
      </view>
    </view>
  );
}

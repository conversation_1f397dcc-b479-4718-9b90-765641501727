import { createContext, useContext, useReducer, useEffect, type ReactNode } from '@lynx-js/react';
import type { AppState, AppAction, Task, Category, Tag, AppSettings, TaskSort } from '../types/index.js';
import { appReducer } from './appReducer.js';
import { generateId } from '../utils/helpers.js';

// Initial state
const initialSettings: AppSettings = {
  theme: 'light',
  defaultPriority: 'medium',
  enableNotifications: true,
  autoMarkOverdue: false,
  showCompletedTasks: true,
  taskSortOrder: { field: 'dueDate', direction: 'asc' }
};

const initialState: AppState = {
  tasks: [],
  categories: [
    { id: 'work', name: 'Work', color: '#3B82F6', icon: '💼' },
    { id: 'personal', name: 'Personal', color: '#10B981', icon: '🏠' },
    { id: 'shopping', name: 'Shopping', color: '#F59E0B', icon: '🛒' },
    { id: 'health', name: 'Health', color: '#EF4444', icon: '🏥' }
  ],
  tags: [
    { id: 'urgent', name: 'Urgent', color: '#DC2626' },
    { id: 'important', name: 'Important', color: '#7C3AED' },
    { id: 'quick', name: 'Quick Task', color: '#059669' },
    { id: 'meeting', name: 'Meeting', color: '#2563EB' }
  ],
  filter: {},
  sort: { field: 'dueDate', direction: 'asc' },
  settings: initialSettings,
  isLoading: false
};

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  // Helper functions
  addTask: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateTask: (id: string, updates: Partial<Task>) => void;
  deleteTask: (id: string) => void;
  toggleTaskStatus: (id: string) => void;
  addCategory: (category: Omit<Category, 'id'>) => void;
  addTag: (tag: Omit<Tag, 'id'>) => void;
  getTaskById: (id: string) => Task | undefined;
  getCategoryById: (id: string) => Category | undefined;
  getTagById: (id: string) => Tag | undefined;
  getFilteredTasks: () => Task[];
  getTaskStats: () => {
    total: number;
    completed: number;
    pending: number;
    overdue: number;
    dueToday: number;
    dueThisWeek: number;
  };
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider component
interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('todoApp');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        // Convert date strings back to Date objects
        const tasks = parsedData.tasks?.map((task: any) => ({
          ...task,
          createdAt: new Date(task.createdAt),
          updatedAt: new Date(task.updatedAt),
          dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
          completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
          subtasks: task.subtasks?.map((subtask: any) => ({
            ...subtask,
            createdAt: new Date(subtask.createdAt)
          })) || []
        })) || [];
        
        dispatch({
          type: 'LOAD_DATA',
          payload: {
            tasks,
            categories: parsedData.categories || initialState.categories,
            tags: parsedData.tags || initialState.tags
          }
        });
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    const dataToSave = {
      tasks: state.tasks,
      categories: state.categories,
      tags: state.tags,
      settings: state.settings
    };
    localStorage.setItem('todoApp', JSON.stringify(dataToSave));
  }, [state.tasks, state.categories, state.tags, state.settings]);

  // Helper functions
  const addTask = (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => {
    dispatch({ type: 'ADD_TASK', payload: task });
  };

  const updateTask = (id: string, updates: Partial<Task>) => {
    dispatch({ type: 'UPDATE_TASK', payload: { id, updates } });
  };

  const deleteTask = (id: string) => {
    dispatch({ type: 'DELETE_TASK', payload: id });
  };

  const toggleTaskStatus = (id: string) => {
    dispatch({ type: 'TOGGLE_TASK_STATUS', payload: id });
  };

  const addCategory = (category: Omit<Category, 'id'>) => {
    dispatch({ type: 'ADD_CATEGORY', payload: category });
  };

  const addTag = (tag: Omit<Tag, 'id'>) => {
    dispatch({ type: 'ADD_TAG', payload: tag });
  };

  const getTaskById = (id: string): Task | undefined => {
    return state.tasks.find(task => task.id === id);
  };

  const getCategoryById = (id: string): Category | undefined => {
    return state.categories.find(category => category.id === id);
  };

  const getTagById = (id: string): Tag | undefined => {
    return state.tags.find(tag => tag.id === id);
  };

  const getFilteredTasks = (): Task[] => {
    let filteredTasks = [...state.tasks];
    const { filter } = state;

    // Apply filters
    if (filter.status && filter.status.length > 0) {
      filteredTasks = filteredTasks.filter(task => filter.status!.includes(task.status));
    }

    if (filter.priority && filter.priority.length > 0) {
      filteredTasks = filteredTasks.filter(task => filter.priority!.includes(task.priority));
    }

    if (filter.categoryId) {
      filteredTasks = filteredTasks.filter(task => task.categoryId === filter.categoryId);
    }

    if (filter.tagIds && filter.tagIds.length > 0) {
      filteredTasks = filteredTasks.filter(task => 
        filter.tagIds!.some(tagId => task.tags.includes(tagId))
      );
    }

    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase();
      filteredTasks = filteredTasks.filter(task => 
        task.title.toLowerCase().includes(query) ||
        task.description?.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    filteredTasks.sort((a, b) => {
      const { field, direction } = state.sort;
      let aValue: any = a[field];
      let bValue: any = b[field];

      if (field === 'dueDate') {
        aValue = a.dueDate?.getTime() || Infinity;
        bValue = b.dueDate?.getTime() || Infinity;
      } else if (field === 'priority') {
        const priorityOrder = { low: 1, medium: 2, high: 3 };
        aValue = priorityOrder[a.priority];
        bValue = priorityOrder[b.priority];
      } else if (aValue instanceof Date) {
        aValue = aValue.getTime();
        bValue = bValue.getTime();
      }

      if (direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filteredTasks;
  };

  const getTaskStats = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

    const stats = {
      total: state.tasks.length,
      completed: state.tasks.filter(task => task.status === 'completed').length,
      pending: state.tasks.filter(task => task.status === 'pending').length,
      overdue: state.tasks.filter(task => 
        task.status === 'pending' && task.dueDate && task.dueDate < today
      ).length,
      dueToday: state.tasks.filter(task => 
        task.status === 'pending' && task.dueDate && 
        task.dueDate >= today && task.dueDate < new Date(today.getTime() + 24 * 60 * 60 * 1000)
      ).length,
      dueThisWeek: state.tasks.filter(task => 
        task.status === 'pending' && task.dueDate && 
        task.dueDate >= today && task.dueDate < weekFromNow
      ).length
    };

    return stats;
  };

  const contextValue: AppContextType = {
    state,
    dispatch,
    addTask,
    updateTask,
    deleteTask,
    toggleTaskStatus,
    addCategory,
    addTag,
    getTaskById,
    getCategoryById,
    getTagById,
    getFilteredTasks,
    getTaskStats
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

// Hook to use the context
export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

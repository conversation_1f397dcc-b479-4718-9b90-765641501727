{"version": 3, "file": "main.5e5531d6cd592477.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx", "file://webpack/runtime/get_full_hash"], "sourcesContent": ["import { useState, useCallback, useEffect } from '@lynx-js/react';\nimport type { Task, Priority } from '../types/index.js';\nimport { useApp } from '../context/AppContext.js';\nimport { validateTask } from '../utils/helpers.js';\n\ninterface EditTaskFormProps {\n  task: Task;\n  onClose: () => void;\n  onTaskUpdated?: () => void;\n}\n\nexport function EditTaskForm({ task, onClose, onTaskUpdated }: EditTaskFormProps) {\n  const { updateTask, state } = useApp();\n  \n  const [formData, setFormData] = useState({\n    title: task.title,\n    description: task.description || '',\n    priority: task.priority,\n    dueDate: task.dueDate,\n    categoryId: task.categoryId || '',\n    tagIds: task.tags,\n    estimatedDuration: task.estimatedDuration\n  });\n  \n  const [errors, setErrors] = useState<string[]>([]);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleInputChange = useCallback((field: string, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // Clear errors when user starts typing\n    if (errors.length > 0) {\n      setErrors([]);\n    }\n  }, [errors.length]);\n\n  const handleSubmit = useCallback(async () => {\n    setIsSubmitting(true);\n    \n    // Validate form\n    const validationErrors = validateTask(formData);\n    if (validationErrors.length > 0) {\n      setErrors(validationErrors);\n      setIsSubmitting(false);\n      return;\n    }\n\n    try {\n      // Update task\n      const updates = {\n        title: formData.title,\n        description: formData.description || undefined,\n        priority: formData.priority,\n        dueDate: formData.dueDate,\n        categoryId: formData.categoryId || undefined,\n        tags: formData.tagIds,\n        estimatedDuration: formData.estimatedDuration\n      };\n\n      updateTask(task.id, updates);\n      onTaskUpdated?.();\n      onClose();\n    } catch (error) {\n      setErrors(['Failed to update task. Please try again.']);\n    } finally {\n      setIsSubmitting(false);\n    }\n  }, [formData, task.id, updateTask, onTaskUpdated, onClose]);\n\n  const handleTagToggle = useCallback((tagId: string) => {\n    const currentTags = formData.tagIds || [];\n    const newTags = currentTags.includes(tagId)\n      ? currentTags.filter(id => id !== tagId)\n      : [...currentTags, tagId];\n    \n    handleInputChange('tagIds', newTags);\n  }, [formData.tagIds, handleInputChange]);\n\n  return (\n    <view className=\"edit-task-form\">\n      <view className=\"form-header\">\n        <text className=\"form-title\">Edit Task</text>\n        <text className=\"close-button\" bindtap={onClose}>✕</text>\n      </view>\n\n      <view className=\"form-content\">\n        {/* Errors */}\n        {errors.length > 0 && (\n          <view className=\"form-errors\">\n            {errors.map((error, index) => (\n              <text key={index} className=\"error-text\">{error}</text>\n            ))}\n          </view>\n        )}\n\n        {/* Title */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Title *</text>\n          <input\n            className=\"field-input\"\n            placeholder=\"Enter task title...\"\n            value={formData.title}\n            onInput={(e: any) => handleInputChange('title', e.detail.value)}\n          />\n        </view>\n\n        {/* Description */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Description</text>\n          <textarea\n            className=\"field-textarea\"\n            placeholder=\"Enter task description...\"\n            value={formData.description}\n            onInput={(e: any) => handleInputChange('description', e.detail.value)}\n          />\n        </view>\n\n        {/* Priority */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Priority</text>\n          <view className=\"priority-options\">\n            {(['low', 'medium', 'high'] as Priority[]).map(priority => (\n              <view\n                key={priority}\n                className={`priority-option ${formData.priority === priority ? 'selected' : ''}`}\n                bindtap={() => handleInputChange('priority', priority)}\n              >\n                <text className=\"priority-text\">{priority.toUpperCase()}</text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Due Date */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Due Date</text>\n          <input\n            className=\"field-input\"\n            type=\"date\"\n            value={formData.dueDate ? formData.dueDate.toISOString().split('T')[0] : ''}\n            onInput={(e: any) => {\n              const value = e.detail.value;\n              handleInputChange('dueDate', value ? new Date(value) : undefined);\n            }}\n          />\n        </view>\n\n        {/* Category */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Category</text>\n          <view className=\"category-options\">\n            <view\n              className={`category-option ${!formData.categoryId ? 'selected' : ''}`}\n              bindtap={() => handleInputChange('categoryId', '')}\n            >\n              <text className=\"category-text\">No Category</text>\n            </view>\n            {state.categories.map(category => (\n              <view\n                key={category.id}\n                className={`category-option ${formData.categoryId === category.id ? 'selected' : ''}`}\n                style={{ borderColor: category.color }}\n                bindtap={() => handleInputChange('categoryId', category.id)}\n              >\n                <text className=\"category-icon\">{category.icon}</text>\n                <text className=\"category-text\">{category.name}</text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Tags */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Tags</text>\n          <view className=\"tag-options\">\n            {state.tags.map(tag => (\n              <view\n                key={tag.id}\n                className={`tag-option ${(formData.tagIds || []).includes(tag.id) ? 'selected' : ''}`}\n                style={{ \n                  backgroundColor: (formData.tagIds || []).includes(tag.id) ? tag.color : 'transparent',\n                  borderColor: tag.color\n                }}\n                bindtap={() => handleTagToggle(tag.id)}\n              >\n                <text \n                  className=\"tag-text\"\n                  style={{ \n                    color: (formData.tagIds || []).includes(tag.id) ? 'white' : tag.color \n                  }}\n                >\n                  {tag.name}\n                </text>\n              </view>\n            ))}\n          </view>\n        </view>\n\n        {/* Estimated Duration */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Estimated Duration (minutes)</text>\n          <input\n            className=\"field-input\"\n            type=\"number\"\n            placeholder=\"e.g., 30\"\n            value={formData.estimatedDuration?.toString() || ''}\n            onInput={(e: any) => {\n              const value = parseInt(e.detail.value);\n              handleInputChange('estimatedDuration', isNaN(value) ? undefined : value);\n            }}\n          />\n        </view>\n\n        {/* Task Status */}\n        <view className=\"form-field\">\n          <text className=\"field-label\">Status</text>\n          <view className=\"status-info\">\n            <text className=\"status-text\">\n              Current status: <text className=\"status-value\">{task.status}</text>\n            </text>\n            <text className=\"status-note\">\n              Use the checkbox in the task list to change completion status\n            </text>\n          </view>\n        </view>\n      </view>\n\n      {/* Actions */}\n      <view className=\"form-actions\">\n        <view className=\"action-button secondary\" bindtap={onClose}>\n          <text className=\"button-text\">Cancel</text>\n        </view>\n        <view \n          className={`action-button primary ${isSubmitting ? 'disabled' : ''}`}\n          bindtap={isSubmitting ? undefined : handleSubmit}\n        >\n          <text className=\"button-text\">\n            {isSubmitting ? 'Updating...' : 'Update Task'}\n          </text>\n        </view>\n      </view>\n    </view>\n  );\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "__webpack_require__.h = () => (\"95cda4259d0ad42e\")"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;;;;AAsFA;;;;;;;;AAFA;;;;;;;;;AAuCA;;;;;;;;;;;;;;;;;;;;AAPA;;;;;;;;AA4CA;;;;;;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;;;;;;;;;;;;;;;;;;;;;AAZA;;;;;;;;AA4CA;;;;;;;;AAkBA;;;;;;;;AA7JA;;AACA;;;AACA;;;;;AACA;;;;;AAGA;;;;;AAWA;;;AACA;;;;AACA;AACA;AACA;;;AAOA;;;AACA;;;;AACA;AACA;AACA;;;AAOA;;;AACA;;;;;;;AAeA;;;AACA;;;;AACA;AACA;AACA;;;AAUA;;;AACA;;;;;AACA;;;;;AAKA;;;;;;;AAiBA;;;AACA;;;;;;;AA0BA;;;AACA;;;;AACA;AACA;AACA;AACA;;;AAUA;;;AACA;;;;;AACA;;;AACA;;;;;;;AAGA;;;;;AAQA;;;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1NA;AAiMA;AAhMA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAGA;AAAA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;;AAIA;AAmBA;AACA;AAUA;AACA;AA0BA;AACA;AACA;AACA;AACA;AASA;AACA;;AAqDA;AACA;AACA;AACA;AAoBA;AAIA;AACA;;;;AAnJA;AAEA;AACA;AAAA;;;;;;;;;;;AA8BA;AACA;AAAA;AAAA;AAAA;AAAA;;AAGA;AACA;;AAEA;AAJA;;;;;;;;;;;AAkCA;;AAGA;AACA;AAAA;AAAA;AACA;;;AAEA;AAAA;;;;;;AACA;AAAA;;;;;;;AANA;;;;;;AAeA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;;AAEA;AAdA;;;;;;;;;;AAyCA;AAAA;;;;;;AAkBA;AACA;;;;;;;;;;;;AAMA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;ACtSA"}
{"version": 3, "file": "main.97a164f40675830d.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx", "file://webpack/runtime/get_full_hash"], "sourcesContent": ["import { useState, useCallback, useMemo } from '@lynx-js/react';\nimport type { Task } from '../types/index.js';\nimport { useApp } from '../context/AppContext.js';\nimport { TaskItem } from './TaskItem.js';\nimport { AddTaskForm } from './AddTaskForm.js';\nimport { EditTaskForm } from './EditTaskForm.js';\n\nexport function TaskList() {\n  const { getFilteredTasks, getTaskStats } = useApp();\n  \n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingTask, setEditingTask] = useState<Task | null>(null);\n\n  const tasks = useMemo(() => getFilteredTasks(), [getFilteredTasks]);\n  const stats = useMemo(() => getTaskStats(), [getTaskStats]);\n\n  const handleAddTask = useCallback(() => {\n    setShowAddForm(true);\n  }, []);\n\n  const handleCloseAddForm = useCallback(() => {\n    setShowAddForm(false);\n  }, []);\n\n  const handleEditTask = useCallback((task: Task) => {\n    setEditingTask(task);\n  }, []);\n\n  const handleCloseEditForm = useCallback(() => {\n    setEditingTask(null);\n  }, []);\n\n  const handleTaskAdded = useCallback(() => {\n    setShowAddForm(false);\n  }, []);\n\n  const handleTaskUpdated = useCallback(() => {\n    setEditingTask(null);\n  }, []);\n\n  // Group tasks by status for better organization\n  const pendingTasks = useMemo(() => \n    tasks.filter(task => task.status === 'pending'), [tasks]\n  );\n  \n  const completedTasks = useMemo(() => \n    tasks.filter(task => task.status === 'completed'), [tasks]\n  );\n\n  return (\n    <view className=\"task-list\">\n      {/* Header */}\n      <view className=\"task-list-header\">\n        <view className=\"header-content\">\n          <text className=\"header-title\">My Tasks</text>\n          <view className=\"add-task-button\" bindtap={handleAddTask}>\n            <text className=\"add-button-text\">+ Add Task</text>\n          </view>\n        </view>\n        \n        {/* Stats */}\n        <view className=\"task-stats\">\n          <view className=\"stat-item\">\n            <text className=\"stat-number\">{stats.total}</text>\n            <text className=\"stat-label\">Total</text>\n          </view>\n          <view className=\"stat-item\">\n            <text className=\"stat-number\">{stats.pending}</text>\n            <text className=\"stat-label\">Pending</text>\n          </view>\n          <view className=\"stat-item\">\n            <text className=\"stat-number\">{stats.completed}</text>\n            <text className=\"stat-label\">Completed</text>\n          </view>\n          <view className=\"stat-item overdue\">\n            <text className=\"stat-number\">{stats.overdue}</text>\n            <text className=\"stat-label\">Overdue</text>\n          </view>\n          <view className=\"stat-item due-today\">\n            <text className=\"stat-number\">{stats.dueToday}</text>\n            <text className=\"stat-label\">Due Today</text>\n          </view>\n        </view>\n      </view>\n\n      {/* Task sections */}\n      <scroll-view className=\"task-list-content\">\n        {/* Pending Tasks */}\n        {pendingTasks.length > 0 && (\n          <view className=\"task-section\">\n            <view className=\"section-header\">\n              <text className=\"section-title\">Pending Tasks ({pendingTasks.length})</text>\n            </view>\n            <view className=\"task-items\">\n              {pendingTasks.map(task => (\n                <TaskItem\n                  key={task.id}\n                  task={task}\n                  onEdit={handleEditTask}\n                />\n              ))}\n            </view>\n          </view>\n        )}\n\n        {/* Completed Tasks */}\n        {completedTasks.length > 0 && (\n          <view className=\"task-section\">\n            <view className=\"section-header\">\n              <text className=\"section-title\">Completed Tasks ({completedTasks.length})</text>\n            </view>\n            <view className=\"task-items\">\n              {completedTasks.map(task => (\n                <TaskItem\n                  key={task.id}\n                  task={task}\n                  onEdit={handleEditTask}\n                />\n              ))}\n            </view>\n          </view>\n        )}\n\n        {/* Empty state */}\n        {tasks.length === 0 && (\n          <view className=\"empty-state\">\n            <text className=\"empty-icon\">📝</text>\n            <text className=\"empty-title\">No tasks yet</text>\n            <text className=\"empty-description\">\n              Create your first task to get started with organizing your work!\n            </text>\n            <view className=\"empty-action\" bindtap={handleAddTask}>\n              <text className=\"empty-action-text\">Create First Task</text>\n            </view>\n          </view>\n        )}\n      </scroll-view>\n\n      {/* Add Task Form Modal */}\n      {showAddForm && (\n        <view className=\"modal-overlay\">\n          <view className=\"modal-content\">\n            <AddTaskForm\n              onClose={handleCloseAddForm}\n              onTaskAdded={handleTaskAdded}\n            />\n          </view>\n        </view>\n      )}\n\n      {/* Edit Task Form Modal */}\n      {editingTask && (\n        <view className=\"modal-overlay\">\n          <view className=\"modal-content\">\n            <EditTaskForm\n              task={editingTask}\n              onClose={handleCloseEditForm}\n              onTaskUpdated={handleTaskUpdated}\n            />\n          </view>\n        </view>\n      )}\n    </view>\n  );\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "__webpack_require__.h = () => (\"992f5f6f82a0312f\")"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;AACA;AACA;;;;AA0DA;;;;;;;;AAIA;;;;;;;;AAIA;;;;;;;;AAIA;;;;;;;;AAIA;;;;;;;;AAcA;;;;;;;;AAJA;;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;;;AAJA;;AACA;;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA;;AACA;;;;;AACA;;;;;AACA;;;;;AAGA;;;AACA;;;;;;;;;;;;;;;;;;;;;;AA9CA;;;;;;;;AAsDA;;AACA;;;;;;;;;;;;;;;AAWA;;AACA;;;;;;;;;;;;;;;AAvGA;;AAEA;;;AACA;;;AACA;;;;;AACA;;;AACA;;;;;AAKA;;;AACA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAzEA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAGA;AACA;AAAA;AAGA;;AAMA;;;AAQA;AAAA;;;;;;AAIA;AAAA;;;;;;AAIA;AAAA;;;;;;AAIA;AAAA;;;;;;AAIA;AAAA;;;;;;AAOA;;AAEA;;;AAGA;;AAEA;AACA;AAGA;AACA;AAFA;;;;;;;;;;;;;;;;AAUA;;;AAGA;;AAEA;AACA;AAGA;AACA;AAFA;;;;;;;;;;;;;;;;AAUA;;AAOA;;;;;;;;;;;;;;AAQA;AAGA;AACA;AACA;;;;;;;;;;;;;AAOA;AAGA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAOA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;ACxNA"}
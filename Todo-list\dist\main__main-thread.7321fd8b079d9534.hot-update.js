(function(){
  'use strict';
  var g = (new Function('return this;'))();
  function __init_card_bundle__(lynxCoreInject) {
    g.__bundle__holder = undefined;
    var globDynamicComponentEntry = g.globDynamicComponentEntry || '__Card__';
    var tt = lynxCoreInject.tt;
    tt.define("main__main-thread.7321fd8b079d9534.hot-update.js", function(require, module, exports, __Card,setTimeout,setInterval,clearInterval,clearTimeout,NativeModules,tt,console,__Component,__ReactLynx,nativeAppId,__Behavior,LynxJSBI,lynx,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,fetch,XMLHttpRequest,__WebSocket__,webkit,Reporter,print,global,requestAnimationFrame,cancelAnimationFrame) {
lynx = lynx || {};
lynx.targetSdkVersion=lynx.targetSdkVersion||"3.2";
var Promise = lynx.Promise;
fetch = fetch || lynx.fetch;
requestAnimationFrame = requestAnimationFrame || lynx.requestAnimationFrame;
cancelAnimationFrame = cancelAnimationFrame || lynx.cancelAnimationFrame;

// This needs to be wrapped in an IIFE because it needs to be isolated against Lynx injected variables.
(() => {
// lynx chunks entries
if (!lynx.__chunk_entries__) {
  // Initialize once
  lynx.__chunk_entries__ = {};
}
if (!lynx.__chunk_entries__["main__main-thread"]) {
  lynx.__chunk_entries__["main__main-thread"] = globDynamicComponentEntry;
} else {
  globDynamicComponentEntry = lynx.__chunk_entries__["main__main-thread"];
}

"use strict";
exports.ids = ["main__main-thread"];
exports.modules = {
"(react:main-thread)/./src/context/appReducer.ts": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  appReducer: () => (appReducer)
});
/* ESM import */var _utils_helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./src/utils/helpers.ts");

function appReducer(state, action) {
    switch(action.type){
        case 'SET_LOADING':
            return {
                ...state,
                isLoading: action.payload
            };
        case 'SET_ERROR':
            return {
                ...state,
                error: action.payload
            };
        case 'ADD_TASK':
            {
                const now = new Date();
                const newTask = {
                    ...action.payload,
                    id: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_0__.generateId)(),
                    createdAt: now,
                    updatedAt: now,
                    status: action.payload.status || 'pending',
                    priority: action.payload.priority || 'medium',
                    tags: action.payload.tags || [],
                    subtasks: action.payload.subtasks || []
                };
                return {
                    ...state,
                    tasks: [
                        ...state.tasks,
                        newTask
                    ]
                };
            }
        case 'UPDATE_TASK':
            {
                const { id, updates } = action.payload;
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>task.id === id ? {
                            ...task,
                            ...updates,
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'DELETE_TASK':
            return {
                ...state,
                tasks: state.tasks.filter((task)=>task.id !== action.payload),
                selectedTaskId: state.selectedTaskId === action.payload ? undefined : state.selectedTaskId
            };
        case 'TOGGLE_TASK_STATUS':
            {
                const taskId = action.payload;
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>{
                        if (task.id === taskId) {
                            const newStatus = task.status === 'completed' ? 'pending' : 'completed';
                            const now = new Date();
                            return {
                                ...task,
                                status: newStatus,
                                completedAt: newStatus === 'completed' ? now : undefined,
                                updatedAt: now
                            };
                        }
                        return task;
                    })
                };
            }
        case 'ADD_SUBTASK':
            {
                const { taskId, subtask } = action.payload;
                const newSubtask = {
                    ...subtask,
                    id: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_0__.generateId)(),
                    createdAt: new Date()
                };
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>task.id === taskId ? {
                            ...task,
                            subtasks: [
                                ...task.subtasks,
                                newSubtask
                            ],
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'UPDATE_SUBTASK':
            {
                const { taskId, subtaskId, updates } = action.payload;
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>task.id === taskId ? {
                            ...task,
                            subtasks: task.subtasks.map((subtask)=>subtask.id === subtaskId ? {
                                    ...subtask,
                                    ...updates
                                } : subtask),
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'DELETE_SUBTASK':
            {
                const { taskId, subtaskId } = action.payload;
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>task.id === taskId ? {
                            ...task,
                            subtasks: task.subtasks.filter((subtask)=>subtask.id !== subtaskId),
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'ADD_CATEGORY':
            {
                const newCategory = {
                    ...action.payload,
                    id: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_0__.generateId)()
                };
                return {
                    ...state,
                    categories: [
                        ...state.categories,
                        newCategory
                    ]
                };
            }
        case 'UPDATE_CATEGORY':
            {
                const { id, updates } = action.payload;
                return {
                    ...state,
                    categories: state.categories.map((category)=>category.id === id ? {
                            ...category,
                            ...updates
                        } : category)
                };
            }
        case 'DELETE_CATEGORY':
            {
                const categoryId = action.payload;
                return {
                    ...state,
                    categories: state.categories.filter((category)=>category.id !== categoryId),
                    // Remove category from tasks
                    tasks: state.tasks.map((task)=>task.categoryId === categoryId ? {
                            ...task,
                            categoryId: undefined,
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'ADD_TAG':
            {
                const newTag = {
                    ...action.payload,
                    id: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_0__.generateId)()
                };
                return {
                    ...state,
                    tags: [
                        ...state.tags,
                        newTag
                    ]
                };
            }
        case 'UPDATE_TAG':
            {
                const { id, updates } = action.payload;
                return {
                    ...state,
                    tags: state.tags.map((tag)=>tag.id === id ? {
                            ...tag,
                            ...updates
                        } : tag)
                };
            }
        case 'DELETE_TAG':
            {
                const tagId = action.payload;
                return {
                    ...state,
                    tags: state.tags.filter((tag)=>tag.id !== tagId),
                    // Remove tag from tasks
                    tasks: state.tasks.map((task)=>({
                            ...task,
                            tags: task.tags.filter((id)=>id !== tagId),
                            updatedAt: task.tags.includes(tagId) ? new Date() : task.updatedAt
                        }))
                };
            }
        case 'SET_FILTER':
            return {
                ...state,
                filter: {
                    ...state.filter,
                    ...action.payload
                }
            };
        case 'CLEAR_FILTER':
            return {
                ...state,
                filter: {}
            };
        case 'SET_SORT':
            return {
                ...state,
                sort: action.payload
            };
        case 'SELECT_TASK':
            return {
                ...state,
                selectedTaskId: action.payload
            };
        case 'UPDATE_SETTINGS':
            return {
                ...state,
                settings: {
                    ...state.settings,
                    ...action.payload
                }
            };
        case 'LOAD_DATA':
            {
                const { tasks, categories, tags } = action.payload;
                return {
                    ...state,
                    tasks,
                    categories,
                    tags
                };
            }
        case 'RESET_APP':
            return {
                ...state,
                tasks: [],
                categories: state.categories,
                tags: state.tags,
                filter: {},
                selectedTaskId: undefined,
                error: undefined
            };
        default:
            return state;
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),
"(react:main-thread)/./src/utils/helpers.ts": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  createDefaultTask: () => (createDefaultTask),
  debounce: () => (debounce),
  exportTasksAsJSON: () => (exportTasksAsJSON),
  filterTasksBySearch: () => (filterTasksBySearch),
  formatDate: () => (formatDate),
  formatDateTime: () => (formatDateTime),
  generateId: () => (generateId),
  getPriorityColor: () => (getPriorityColor),
  getStatusColor: () => (getStatusColor),
  getTaskCompletionPercentage: () => (getTaskCompletionPercentage),
  getTasksDueInRange: () => (getTasksDueInRange),
  importTasksFromJSON: () => (importTasksFromJSON),
  isTaskDueThisWeek: () => (isTaskDueThisWeek),
  isTaskDueToday: () => (isTaskDueToday),
  isTaskOverdue: () => (isTaskOverdue),
  sortTasksByDueDate: () => (sortTasksByDueDate),
  sortTasksByPriority: () => (sortTasksByPriority),
  validateTask: () => (validateTask)
});
// Generate unique ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
// Format date for display
function formatDate(date) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 86400000);
    const tomorrow = new Date(today.getTime() + 86400000);
    const taskDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    if (taskDate.getTime() === today.getTime()) return 'Today';
    else if (taskDate.getTime() === yesterday.getTime()) return 'Yesterday';
    else if (taskDate.getTime() === tomorrow.getTime()) return 'Tomorrow';
    else return date.toLocaleDateString();
}
// Format date and time for display
function formatDateTime(date) {
    return `${formatDate(date)} at ${date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
    })}`;
}
// Check if a task is overdue
function isTaskOverdue(task) {
    if (!task.dueDate || task.status === 'completed') return false;
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const taskDate = new Date(task.dueDate.getFullYear(), task.dueDate.getMonth(), task.dueDate.getDate());
    return taskDate < today;
}
// Check if a task is due today
function isTaskDueToday(task) {
    if (!task.dueDate || task.status === 'completed') return false;
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const taskDate = new Date(task.dueDate.getFullYear(), task.dueDate.getMonth(), task.dueDate.getDate());
    return taskDate.getTime() === today.getTime();
}
// Check if a task is due this week
function isTaskDueThisWeek(task) {
    if (!task.dueDate || task.status === 'completed') return false;
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekFromNow = new Date(today.getTime() + 604800000);
    return task.dueDate >= today && task.dueDate < weekFromNow;
}
// Get priority color
function getPriorityColor(priority) {
    switch(priority){
        case 'high':
            return '#EF4444'; // Red
        case 'medium':
            return '#F59E0B'; // Orange
        case 'low':
            return '#10B981'; // Green
        default:
            return '#6B7280'; // Gray
    }
}
// Get status color
function getStatusColor(status) {
    switch(status){
        case 'completed':
            return '#10B981'; // Green
        case 'pending':
            return '#3B82F6'; // Blue
        case 'cancelled':
            return '#6B7280'; // Gray
        default:
            return '#6B7280'; // Gray
    }
}
// Calculate task completion percentage
function getTaskCompletionPercentage(task) {
    if (task.status === 'completed') return 100;
    if (task.subtasks.length === 0) return 0;
    const completedSubtasks = task.subtasks.filter((subtask)=>subtask.completed).length;
    return Math.round(completedSubtasks / task.subtasks.length * 100);
}
// Sort tasks by priority
function sortTasksByPriority(tasks) {
    const priorityOrder = {
        high: 3,
        medium: 2,
        low: 1
    };
    return [
        ...tasks
    ].sort((a, b)=>priorityOrder[b.priority] - priorityOrder[a.priority]);
}
// Sort tasks by due date
function sortTasksByDueDate(tasks) {
    return [
        ...tasks
    ].sort((a, b)=>{
        if (!a.dueDate && !b.dueDate) return 0;
        if (!a.dueDate) return 1;
        if (!b.dueDate) return -1;
        return a.dueDate.getTime() - b.dueDate.getTime();
    });
}
// Filter tasks by search query
function filterTasksBySearch(tasks, query) {
    if (!query.trim()) return tasks;
    const searchTerm = query.toLowerCase().trim();
    return tasks.filter((task)=>{
        var _task_description;
        return task.title.toLowerCase().includes(searchTerm) || ((_task_description = task.description) === null || _task_description === void 0 ? void 0 : _task_description.toLowerCase().includes(searchTerm)) || task.subtasks.some((subtask)=>subtask.title.toLowerCase().includes(searchTerm));
    });
}
// Get tasks due in a specific time range
function getTasksDueInRange(tasks, startDate, endDate) {
    return tasks.filter((task)=>{
        if (!task.dueDate || task.status === 'completed') return false;
        return task.dueDate >= startDate && task.dueDate <= endDate;
    });
}
// Create a new task with default values
function createDefaultTask(overrides = {}) {
    return {
        title: '',
        status: 'pending',
        priority: 'medium',
        tags: [],
        subtasks: [],
        ...overrides
    };
}
// Validate task data
function validateTask(task) {
    const errors = [];
    if (!task.title || task.title.trim().length === 0) errors.push('Task title is required');
    if (task.title && task.title.length > 200) errors.push('Task title must be less than 200 characters');
    if (task.description && task.description.length > 1000) errors.push('Task description must be less than 1000 characters');
    task.dueDate && task.dueDate;
    return errors;
}
// Export task data as JSON
function exportTasksAsJSON(tasks) {
    return JSON.stringify(tasks, null, 2);
}
// Import tasks from JSON
function importTasksFromJSON(jsonString) {
    try {
        const data = JSON.parse(jsonString);
        if (!Array.isArray(data)) throw new Error('Invalid format: expected an array of tasks');
        return data.map((task)=>({
                ...task,
                id: task.id || generateId(),
                createdAt: new Date(task.createdAt || Date.now()),
                updatedAt: new Date(task.updatedAt || Date.now()),
                dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
                completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
                subtasks: (task.subtasks || []).map((subtask)=>({
                        ...subtask,
                        id: subtask.id || generateId(),
                        createdAt: new Date(subtask.createdAt || Date.now())
                    }))
            }));
    } catch (error) {
        throw new Error('Failed to parse JSON data');
    }
}
// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),
"(react:main-thread)/./src/App.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  App: () => (App)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _App_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/App.css");
/* ESM import */var _context_AppContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("(react:main-thread)/./src/context/AppContext.tsx");
/* ESM import */var _components_TaskList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("(react:main-thread)/./src/components/TaskList.tsx");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");





const __snapshot_835da_3dcfc_1 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_835da_3dcfc_1", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "Background");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "App");
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        2
    ]
], undefined, globDynamicComponentEntry, null);
function App(props) {
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useEffect)();
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AppContext__WEBPACK_IMPORTED_MODULE_3__.AppProvider, {
        children: /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_835da_3dcfc_1, {
            children: /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TaskList__WEBPACK_IMPORTED_MODULE_4__.TaskList, {}, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
                lineNumber: 19,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
            lineNumber: 16,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\App.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),
"(react:main-thread)/./src/components/AddTaskForm.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  AddTaskForm: () => (AddTaskForm)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _context_AppContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/context/AppContext.tsx");
/* ESM import */var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("(react:main-thread)/./src/utils/helpers.ts");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");




const __snapshot_1e839_10e3f_3 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_3", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "error-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_2 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_2", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "form-errors");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_5 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_5", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "priority-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_4 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_4", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "priority-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_7 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_7", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_8 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_8", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_6 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_6", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_10 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_10", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "tag-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[1], ctx.__values[3]);
    }
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_9 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_9", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "tag-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_11 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_11", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "button-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_1e839_10e3f_1 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_1e839_10e3f_1", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "add-task-form");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "form-header");
    __AppendElement(el, el1);
    const el2 = __CreateText(pageId);
    __SetClasses(el2, "form-title");
    __AppendElement(el1, el2);
    const el3 = __CreateRawText("Add New Task");
    __AppendElement(el2, el3);
    const el4 = __CreateText(pageId);
    __SetClasses(el4, "close-button");
    __AppendElement(el1, el4);
    const el5 = __CreateRawText("\u2715");
    __AppendElement(el4, el5);
    const el6 = __CreateView(pageId);
    __SetClasses(el6, "form-content");
    __AppendElement(el, el6);
    const el7 = __CreateWrapperElement(pageId);
    __AppendElement(el6, el7);
    const el8 = __CreateView(pageId);
    __SetClasses(el8, "form-field");
    __AppendElement(el6, el8);
    const el9 = __CreateText(pageId);
    __SetClasses(el9, "field-label");
    __AppendElement(el8, el9);
    const el10 = __CreateRawText("Title *");
    __AppendElement(el9, el10);
    const el11 = __CreateElement("input", pageId);
    __SetClasses(el11, "field-input");
    __SetAttribute(el11, "placeholder", "Enter task title...");
    __AppendElement(el8, el11);
    const el12 = __CreateView(pageId);
    __SetClasses(el12, "form-field");
    __AppendElement(el6, el12);
    const el13 = __CreateText(pageId);
    __SetClasses(el13, "field-label");
    __AppendElement(el12, el13);
    const el14 = __CreateRawText("Description");
    __AppendElement(el13, el14);
    const el15 = __CreateElement("textarea", pageId);
    __SetClasses(el15, "field-textarea");
    __SetAttribute(el15, "placeholder", "Enter task description...");
    __AppendElement(el12, el15);
    const el16 = __CreateView(pageId);
    __SetClasses(el16, "form-field");
    __AppendElement(el6, el16);
    const el17 = __CreateText(pageId);
    __SetClasses(el17, "field-label");
    __AppendElement(el16, el17);
    const el18 = __CreateRawText("Priority");
    __AppendElement(el17, el18);
    const el19 = __CreateWrapperElement(pageId);
    __AppendElement(el16, el19);
    const el20 = __CreateView(pageId);
    __SetClasses(el20, "form-field");
    __AppendElement(el6, el20);
    const el21 = __CreateText(pageId);
    __SetClasses(el21, "field-label");
    __AppendElement(el20, el21);
    const el22 = __CreateRawText("Due Date");
    __AppendElement(el21, el22);
    const el23 = __CreateElement("input", pageId);
    __SetClasses(el23, "field-input");
    __SetAttribute(el23, "type", "date");
    __AppendElement(el20, el23);
    const el24 = __CreateView(pageId);
    __SetClasses(el24, "form-field");
    __AppendElement(el6, el24);
    const el25 = __CreateText(pageId);
    __SetClasses(el25, "field-label");
    __AppendElement(el24, el25);
    const el26 = __CreateRawText("Category");
    __AppendElement(el25, el26);
    const el27 = __CreateView(pageId);
    __SetClasses(el27, "category-options");
    __AppendElement(el24, el27);
    const el28 = __CreateView(pageId);
    __AppendElement(el27, el28);
    const el29 = __CreateText(pageId);
    __SetClasses(el29, "category-text");
    __AppendElement(el28, el29);
    const el30 = __CreateRawText("No Category");
    __AppendElement(el29, el30);
    const el31 = __CreateWrapperElement(pageId);
    __AppendElement(el27, el31);
    const el32 = __CreateView(pageId);
    __SetClasses(el32, "form-field");
    __AppendElement(el6, el32);
    const el33 = __CreateText(pageId);
    __SetClasses(el33, "field-label");
    __AppendElement(el32, el33);
    const el34 = __CreateRawText("Tags");
    __AppendElement(el33, el34);
    const el35 = __CreateWrapperElement(pageId);
    __AppendElement(el32, el35);
    const el36 = __CreateView(pageId);
    __SetClasses(el36, "form-field");
    __AppendElement(el6, el36);
    const el37 = __CreateText(pageId);
    __SetClasses(el37, "field-label");
    __AppendElement(el36, el37);
    const el38 = __CreateRawText("Estimated Duration (minutes)");
    __AppendElement(el37, el38);
    const el39 = __CreateElement("input", pageId);
    __SetClasses(el39, "field-input");
    __SetAttribute(el39, "type", "number");
    __SetAttribute(el39, "placeholder", "e.g., 30");
    __AppendElement(el36, el39);
    const el40 = __CreateView(pageId);
    __SetClasses(el40, "form-actions");
    __AppendElement(el, el40);
    const el41 = __CreateView(pageId);
    __SetClasses(el41, "action-button secondary");
    __AppendElement(el40, el41);
    const el42 = __CreateText(pageId);
    __SetClasses(el42, "button-text");
    __AppendElement(el41, el42);
    const el43 = __CreateRawText("Cancel");
    __AppendElement(el42, el43);
    const el44 = __CreateView(pageId);
    __AppendElement(el40, el44);
    const el45 = __CreateWrapperElement(pageId);
    __AppendElement(el44, el45);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17,
        el18,
        el19,
        el20,
        el21,
        el22,
        el23,
        el24,
        el25,
        el26,
        el27,
        el28,
        el29,
        el30,
        el31,
        el32,
        el33,
        el34,
        el35,
        el36,
        el37,
        el38,
        el39,
        el40,
        el41,
        el42,
        el43,
        el44,
        el45
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 4, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[11], "value", ctx.__values[1]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[11], "onInput", ctx.__values[2]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[15], "value", ctx.__values[3]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[15], "onInput", ctx.__values[4]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[23], "value", ctx.__values[5]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[23], "onInput", ctx.__values[6]);
    },
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[28], ctx.__values[7] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 28, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[39], "value", ctx.__values[9]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[39], "onInput", ctx.__values[10]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 41, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[44], ctx.__values[12] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 44, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        7
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        19
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        31
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        35
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        45
    ]
], undefined, globDynamicComponentEntry, null);
function AddTaskForm({ onClose, onTaskAdded }) {
    var _formData_estimatedDuration;
    const { addTask, state } = (0,_context_AppContext__WEBPACK_IMPORTED_MODULE_2__.useApp)();
    const [formData, setFormData] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)({
        title: '',
        description: '',
        priority: 'medium',
        dueDate: undefined,
        categoryId: '',
        tagIds: [],
        estimatedDuration: undefined
    });
    const [errors, setErrors] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [isSubmitting, setIsSubmitting] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const handleInputChange = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{
        setFormData((prev)=>({
                ...prev,
                [field]: value
            }));
        // Clear errors when user starts typing
        if (errors.length > 0) setErrors([]);
    }, [
        errors.length
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{
        setIsSubmitting(true);
        // Validate form
        const validationErrors = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.validateTask)(formData);
        if (validationErrors.length > 0) {
            setErrors(validationErrors);
            setIsSubmitting(false);
            return;
        }
        try {
            // Create task
            const taskData = {
                ...formData,
                status: 'pending',
                tags: formData.tagIds || [],
                subtasks: []
            };
            addTask(taskData);
            onTaskAdded === null || onTaskAdded === void 0 ? void 0 : onTaskAdded();
            onClose();
        } catch (error1) {
            setErrors([
                'Failed to create task. Please try again.'
            ]);
        } finally{
            setIsSubmitting(false);
        }
    }, [
        formData,
        addTask,
        onTaskAdded,
        onClose
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((tagId)=>{
        const currentTags = formData.tagIds || [];
        const newTags = currentTags.includes(tagId) ? currentTags.filter((id)=>id !== tagId) : [
            ...currentTags,
            tagId
        ];
        handleInputChange('tagIds', newTags);
    }, [
        formData.tagIds,
        handleInputChange
    ]);
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_1, {
        values: [
            1,
            formData.title,
            (e)=>handleInputChange('title', e.detail.value),
            formData.description,
            (e)=>handleInputChange('description', e.detail.value),
            formData.dueDate ? formData.dueDate.toISOString().split('T')[0] : '',
            (e)=>{
                const value = e.detail.value;
                handleInputChange('dueDate', value ? new Date(value) : undefined);
            },
            `category-option ${!formData.categoryId ? 'selected' : ''}`,
            1,
            ((_formData_estimatedDuration = formData.estimatedDuration) === null || _formData_estimatedDuration === void 0 ? void 0 : _formData_estimatedDuration.toString()) || '',
            (e)=>{
                const value = parseInt(e.detail.value);
                handleInputChange('estimatedDuration', isNaN(value) ? undefined : value);
            },
            1,
            `action-button primary ${isSubmitting ? 'disabled' : ''}`,
            1
        ],
        children: [
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: errors.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_2, {
                    children: errors.map((error1, index)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_3, {
                            children: error1
                        }, index, false, {
                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                            lineNumber: 86,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                    lineNumber: 84,
                    columnNumber: 11
                }, this)
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_4, {
                children: [
                    'low',
                    'medium',
                    'high'
                ].map((priority)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_5, {
                        values: [
                            `priority-option ${formData.priority === priority ? 'selected' : ''}`,
                            1
                        ],
                        children: priority.toUpperCase()
                    }, priority, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                        lineNumber: 118,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                lineNumber: 116,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: state.categories.map((category)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_6, {
                        values: [
                            `category-option ${formData.categoryId === category.id ? 'selected' : ''}`,
                            {
                                borderColor: category.color
                            },
                            1
                        ],
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_7, {
                                children: category.icon
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                                lineNumber: 160,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_8, {
                                children: category.name
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                                lineNumber: 161,
                                columnNumber: 17
                            }, this)
                        ]
                    }, category.id, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                        lineNumber: 154,
                        columnNumber: 15
                    }, this))
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_9, {
                children: state.tags.map((tag)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_10, {
                        values: [
                            `tag-option ${(formData.tagIds || []).includes(tag.id) ? 'selected' : ''}`,
                            {
                                backgroundColor: (formData.tagIds || []).includes(tag.id) ? tag.color : 'transparent',
                                borderColor: tag.color
                            },
                            1,
                            {
                                color: (formData.tagIds || []).includes(tag.id) ? 'white' : tag.color
                            }
                        ],
                        children: tag.name
                    }, tag.id, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                        lineNumber: 172,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                lineNumber: 170,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_1e839_10e3f_11, {
                children: isSubmitting ? 'Creating...' : 'Create Task'
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
                lineNumber: 219,
                columnNumber: 11
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\AddTaskForm.tsx",
        lineNumber: 75,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),
"(react:main-thread)/./src/components/EditTaskForm.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  EditTaskForm: () => (EditTaskForm)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _context_AppContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/context/AppContext.tsx");
/* ESM import */var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("(react:main-thread)/./src/utils/helpers.ts");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");




const __snapshot_0e0cf_5a725_3 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_3", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "error-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_2 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_2", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "form-errors");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_5 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_5", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "priority-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_4 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_4", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "priority-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_7 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_7", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_8 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_8", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_6 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_6", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_10 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_10", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "tag-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[1], ctx.__values[3]);
    }
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_9 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_9", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "tag-options");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_11 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_11", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "status-value");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_12 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_12", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "button-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_0e0cf_5a725_1 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_0e0cf_5a725_1", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "edit-task-form");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "form-header");
    __AppendElement(el, el1);
    const el2 = __CreateText(pageId);
    __SetClasses(el2, "form-title");
    __AppendElement(el1, el2);
    const el3 = __CreateRawText("Edit Task");
    __AppendElement(el2, el3);
    const el4 = __CreateText(pageId);
    __SetClasses(el4, "close-button");
    __AppendElement(el1, el4);
    const el5 = __CreateRawText("\u2715");
    __AppendElement(el4, el5);
    const el6 = __CreateView(pageId);
    __SetClasses(el6, "form-content");
    __AppendElement(el, el6);
    const el7 = __CreateWrapperElement(pageId);
    __AppendElement(el6, el7);
    const el8 = __CreateView(pageId);
    __SetClasses(el8, "form-field");
    __AppendElement(el6, el8);
    const el9 = __CreateText(pageId);
    __SetClasses(el9, "field-label");
    __AppendElement(el8, el9);
    const el10 = __CreateRawText("Title *");
    __AppendElement(el9, el10);
    const el11 = __CreateElement("input", pageId);
    __SetClasses(el11, "field-input");
    __SetAttribute(el11, "placeholder", "Enter task title...");
    __AppendElement(el8, el11);
    const el12 = __CreateView(pageId);
    __SetClasses(el12, "form-field");
    __AppendElement(el6, el12);
    const el13 = __CreateText(pageId);
    __SetClasses(el13, "field-label");
    __AppendElement(el12, el13);
    const el14 = __CreateRawText("Description");
    __AppendElement(el13, el14);
    const el15 = __CreateElement("textarea", pageId);
    __SetClasses(el15, "field-textarea");
    __SetAttribute(el15, "placeholder", "Enter task description...");
    __AppendElement(el12, el15);
    const el16 = __CreateView(pageId);
    __SetClasses(el16, "form-field");
    __AppendElement(el6, el16);
    const el17 = __CreateText(pageId);
    __SetClasses(el17, "field-label");
    __AppendElement(el16, el17);
    const el18 = __CreateRawText("Priority");
    __AppendElement(el17, el18);
    const el19 = __CreateWrapperElement(pageId);
    __AppendElement(el16, el19);
    const el20 = __CreateView(pageId);
    __SetClasses(el20, "form-field");
    __AppendElement(el6, el20);
    const el21 = __CreateText(pageId);
    __SetClasses(el21, "field-label");
    __AppendElement(el20, el21);
    const el22 = __CreateRawText("Due Date");
    __AppendElement(el21, el22);
    const el23 = __CreateElement("input", pageId);
    __SetClasses(el23, "field-input");
    __SetAttribute(el23, "type", "date");
    __AppendElement(el20, el23);
    const el24 = __CreateView(pageId);
    __SetClasses(el24, "form-field");
    __AppendElement(el6, el24);
    const el25 = __CreateText(pageId);
    __SetClasses(el25, "field-label");
    __AppendElement(el24, el25);
    const el26 = __CreateRawText("Category");
    __AppendElement(el25, el26);
    const el27 = __CreateView(pageId);
    __SetClasses(el27, "category-options");
    __AppendElement(el24, el27);
    const el28 = __CreateView(pageId);
    __AppendElement(el27, el28);
    const el29 = __CreateText(pageId);
    __SetClasses(el29, "category-text");
    __AppendElement(el28, el29);
    const el30 = __CreateRawText("No Category");
    __AppendElement(el29, el30);
    const el31 = __CreateWrapperElement(pageId);
    __AppendElement(el27, el31);
    const el32 = __CreateView(pageId);
    __SetClasses(el32, "form-field");
    __AppendElement(el6, el32);
    const el33 = __CreateText(pageId);
    __SetClasses(el33, "field-label");
    __AppendElement(el32, el33);
    const el34 = __CreateRawText("Tags");
    __AppendElement(el33, el34);
    const el35 = __CreateWrapperElement(pageId);
    __AppendElement(el32, el35);
    const el36 = __CreateView(pageId);
    __SetClasses(el36, "form-field");
    __AppendElement(el6, el36);
    const el37 = __CreateText(pageId);
    __SetClasses(el37, "field-label");
    __AppendElement(el36, el37);
    const el38 = __CreateRawText("Estimated Duration (minutes)");
    __AppendElement(el37, el38);
    const el39 = __CreateElement("input", pageId);
    __SetClasses(el39, "field-input");
    __SetAttribute(el39, "type", "number");
    __SetAttribute(el39, "placeholder", "e.g., 30");
    __AppendElement(el36, el39);
    const el40 = __CreateView(pageId);
    __SetClasses(el40, "form-field");
    __AppendElement(el6, el40);
    const el41 = __CreateText(pageId);
    __SetClasses(el41, "field-label");
    __AppendElement(el40, el41);
    const el42 = __CreateRawText("Status");
    __AppendElement(el41, el42);
    const el43 = __CreateView(pageId);
    __SetClasses(el43, "status-info");
    __AppendElement(el40, el43);
    const el44 = __CreateText(pageId);
    __SetClasses(el44, "status-text");
    __AppendElement(el43, el44);
    const el45 = __CreateRawText("Current status: ");
    __AppendElement(el44, el45);
    const el46 = __CreateWrapperElement(pageId);
    __AppendElement(el44, el46);
    const el47 = __CreateText(pageId);
    __SetClasses(el47, "status-note");
    __AppendElement(el43, el47);
    const el48 = __CreateRawText("Use the checkbox in the task list to change completion status");
    __AppendElement(el47, el48);
    const el49 = __CreateView(pageId);
    __SetClasses(el49, "form-actions");
    __AppendElement(el, el49);
    const el50 = __CreateView(pageId);
    __SetClasses(el50, "action-button secondary");
    __AppendElement(el49, el50);
    const el51 = __CreateText(pageId);
    __SetClasses(el51, "button-text");
    __AppendElement(el50, el51);
    const el52 = __CreateRawText("Cancel");
    __AppendElement(el51, el52);
    const el53 = __CreateView(pageId);
    __AppendElement(el49, el53);
    const el54 = __CreateWrapperElement(pageId);
    __AppendElement(el53, el54);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17,
        el18,
        el19,
        el20,
        el21,
        el22,
        el23,
        el24,
        el25,
        el26,
        el27,
        el28,
        el29,
        el30,
        el31,
        el32,
        el33,
        el34,
        el35,
        el36,
        el37,
        el38,
        el39,
        el40,
        el41,
        el42,
        el43,
        el44,
        el45,
        el46,
        el47,
        el48,
        el49,
        el50,
        el51,
        el52,
        el53,
        el54
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 4, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[11], "value", ctx.__values[1]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[11], "onInput", ctx.__values[2]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[15], "value", ctx.__values[3]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[15], "onInput", ctx.__values[4]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[23], "value", ctx.__values[5]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[23], "onInput", ctx.__values[6]);
    },
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[28], ctx.__values[7] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 28, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[39], "value", ctx.__values[9]);
    },
    function(ctx) {
        if (ctx.__elements) __SetAttribute(ctx.__elements[39], "onInput", ctx.__values[10]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 50, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[53], ctx.__values[12] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 53, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        7
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        19
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        31
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        35
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        46
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        54
    ]
], undefined, globDynamicComponentEntry, null);
function EditTaskForm({ task, onClose, onTaskUpdated }) {
    var _formData_estimatedDuration;
    const { updateTask, state } = (0,_context_AppContext__WEBPACK_IMPORTED_MODULE_2__.useApp)();
    const [formData, setFormData] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)({
        title: task.title,
        description: task.description || '',
        priority: task.priority,
        dueDate: task.dueDate,
        categoryId: task.categoryId || '',
        tagIds: task.tags,
        estimatedDuration: task.estimatedDuration
    });
    const [errors, setErrors] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [isSubmitting, setIsSubmitting] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const handleInputChange = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field, value)=>{
        setFormData((prev)=>({
                ...prev,
                [field]: value
            }));
        // Clear errors when user starts typing
        if (errors.length > 0) setErrors([]);
    }, [
        errors.length
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{
        setIsSubmitting(true);
        // Validate form
        const validationErrors = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.validateTask)(formData);
        if (validationErrors.length > 0) {
            setErrors(validationErrors);
            setIsSubmitting(false);
            return;
        }
        try {
            // Update task
            const updates = {
                title: formData.title,
                description: formData.description || undefined,
                priority: formData.priority,
                dueDate: formData.dueDate,
                categoryId: formData.categoryId || undefined,
                tags: formData.tagIds,
                estimatedDuration: formData.estimatedDuration
            };
            updateTask(task.id, updates);
            onTaskUpdated === null || onTaskUpdated === void 0 ? void 0 : onTaskUpdated();
            onClose();
        } catch (error1) {
            setErrors([
                'Failed to update task. Please try again.'
            ]);
        } finally{
            setIsSubmitting(false);
        }
    }, [
        formData,
        task.id,
        updateTask,
        onTaskUpdated,
        onClose
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((tagId)=>{
        const currentTags = formData.tagIds || [];
        const newTags = currentTags.includes(tagId) ? currentTags.filter((id)=>id !== tagId) : [
            ...currentTags,
            tagId
        ];
        handleInputChange('tagIds', newTags);
    }, [
        formData.tagIds,
        handleInputChange
    ]);
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_1, {
        values: [
            1,
            formData.title,
            (e)=>handleInputChange('title', e.detail.value),
            formData.description,
            (e)=>handleInputChange('description', e.detail.value),
            formData.dueDate ? formData.dueDate.toISOString().split('T')[0] : '',
            (e)=>{
                const value = e.detail.value;
                handleInputChange('dueDate', value ? new Date(value) : undefined);
            },
            `category-option ${!formData.categoryId ? 'selected' : ''}`,
            1,
            ((_formData_estimatedDuration = formData.estimatedDuration) === null || _formData_estimatedDuration === void 0 ? void 0 : _formData_estimatedDuration.toString()) || '',
            (e)=>{
                const value = parseInt(e.detail.value);
                handleInputChange('estimatedDuration', isNaN(value) ? undefined : value);
            },
            1,
            `action-button primary ${isSubmitting ? 'disabled' : ''}`,
            1
        ],
        children: [
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: errors.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_2, {
                    children: errors.map((error1, index)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_3, {
                            children: error1
                        }, index, false, {
                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                            lineNumber: 90,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                    lineNumber: 88,
                    columnNumber: 11
                }, this)
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_4, {
                children: [
                    'low',
                    'medium',
                    'high'
                ].map((priority)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_5, {
                        values: [
                            `priority-option ${formData.priority === priority ? 'selected' : ''}`,
                            1
                        ],
                        children: priority.toUpperCase()
                    }, priority, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                        lineNumber: 122,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                lineNumber: 120,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: state.categories.map((category)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_6, {
                        values: [
                            `category-option ${formData.categoryId === category.id ? 'selected' : ''}`,
                            {
                                borderColor: category.color
                            },
                            1
                        ],
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_7, {
                                children: category.icon
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                                lineNumber: 164,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_8, {
                                children: category.name
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                                lineNumber: 165,
                                columnNumber: 17
                            }, this)
                        ]
                    }, category.id, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                        lineNumber: 158,
                        columnNumber: 15
                    }, this))
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_9, {
                children: state.tags.map((tag)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_10, {
                        values: [
                            `tag-option ${(formData.tagIds || []).includes(tag.id) ? 'selected' : ''}`,
                            {
                                backgroundColor: (formData.tagIds || []).includes(tag.id) ? tag.color : 'transparent',
                                borderColor: tag.color
                            },
                            1,
                            {
                                color: (formData.tagIds || []).includes(tag.id) ? 'white' : tag.color
                            }
                        ],
                        children: tag.name
                    }, tag.id, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                        lineNumber: 176,
                        columnNumber: 15
                    }, this))
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                lineNumber: 174,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_11, {
                children: task.status
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                lineNumber: 218,
                columnNumber: 31
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_0e0cf_5a725_12, {
                children: isSubmitting ? 'Updating...' : 'Update Task'
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
                lineNumber: 236,
                columnNumber: 11
            }, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\EditTaskForm.tsx",
        lineNumber: 79,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),
"(react:main-thread)/./src/components/TaskItem.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  TaskItem: () => (TaskItem)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _context_AppContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/context/AppContext.tsx");
/* ESM import */var _utils_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("(react:main-thread)/./src/utils/helpers.ts");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");




const __snapshot_66b74_0c624_3 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_3", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "checkmark");
    const el1 = __CreateRawText("\u2713");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, null, null, undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_2 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_2", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    return [
        el
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_4 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_4", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    return [
        el
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 0, "bindEvent", "tap", '')
], (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_5 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_5", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "task-description");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_7 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_7", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-icon");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_8 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_8", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "category-name");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_6 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_6", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-category");
    const el1 = __CreateWrapperElement(pageId);
    __AppendElement(el, el1);
    const el2 = __CreateWrapperElement(pageId);
    __AppendElement(el, el2);
    return [
        el,
        el1,
        el2
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[0]);
    }
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        1
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        2
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_9 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_9", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "due-date-icon");
    __AppendElement(el, el1);
    const el2 = __CreateRawText("\u{1F4C5}");
    __AppendElement(el1, el2);
    const el3 = __CreateText(pageId);
    __SetClasses(el3, "due-date-text");
    __AppendElement(el, el3);
    return [
        el,
        el1,
        el2,
        el3
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    }
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        3
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_10 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_10", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "priority-text");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_12 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_12", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-tag");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "tag-text");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[0], ctx.__values[0]);
    }
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_11 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_11", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-tags");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_13 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_13", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "subtasks-progress");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "progress-bar");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "progress-fill");
    __AppendElement(el1, el2);
    const el3 = __CreateText(pageId);
    __SetClasses(el3, "progress-text");
    __AppendElement(el, el3);
    const el4 = __CreateWrapperElement(pageId);
    __AppendElement(el3, el4);
    const el5 = __CreateRawText("/");
    __AppendElement(el3, el5);
    const el6 = __CreateWrapperElement(pageId);
    __AppendElement(el3, el6);
    const el7 = __CreateRawText(" subtasks");
    __AppendElement(el3, el7);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[2], ctx.__values[0]);
    }
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        4
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        6
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_66b74_0c624_1 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_66b74_0c624_1", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "priority-indicator");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "task-content");
    __AppendElement(el, el2);
    const el3 = __CreateView(pageId);
    __SetClasses(el3, "task-header");
    __AppendElement(el2, el3);
    const el4 = __CreateWrapperElement(pageId);
    __AppendElement(el3, el4);
    const el5 = __CreateWrapperElement(pageId);
    __AppendElement(el3, el5);
    const el6 = __CreateView(pageId);
    __SetClasses(el6, "task-actions");
    __AppendElement(el3, el6);
    const el7 = __CreateText(pageId);
    __SetClasses(el7, "action-button edit");
    __AppendElement(el6, el7);
    const el8 = __CreateRawText("\u270F\uFE0F");
    __AppendElement(el7, el8);
    const el9 = __CreateText(pageId);
    __SetClasses(el9, "action-button delete");
    __AppendElement(el6, el9);
    const el10 = __CreateRawText("\u{1F5D1}\uFE0F");
    __AppendElement(el9, el10);
    const el11 = __CreateWrapperElement(pageId);
    __AppendElement(el2, el11);
    const el12 = __CreateView(pageId);
    __SetClasses(el12, "task-metadata");
    __AppendElement(el2, el12);
    const el13 = __CreateWrapperElement(pageId);
    __AppendElement(el12, el13);
    const el14 = __CreateWrapperElement(pageId);
    __AppendElement(el12, el14);
    const el15 = __CreateView(pageId);
    __SetClasses(el15, "task-priority");
    __AppendElement(el12, el15);
    const el16 = __CreateWrapperElement(pageId);
    __AppendElement(el15, el16);
    const el17 = __CreateWrapperElement(pageId);
    __AppendElement(el2, el17);
    const el18 = __CreateWrapperElement(pageId);
    __AppendElement(el2, el18);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17,
        el18
    ];
}, [
    function(ctx) {
        if (ctx.__elements) __SetClasses(ctx.__elements[0], ctx.__values[0] || '');
    },
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[1], ctx.__values[1]);
    },
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 7, "bindEvent", "tap", ''),
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 9, "bindEvent", "tap", ''),
    function(ctx) {
        if (ctx.__elements) __SetInlineStyles(ctx.__elements[15], ctx.__values[4]);
    }
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        4
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        5
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        11
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        13
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        14
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        16
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        17
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        18
    ]
], undefined, globDynamicComponentEntry, null);
function TaskItem({ task, onEdit }) {
    const { toggleTaskStatus, deleteTask, getCategoryById, getTagById } = (0,_context_AppContext__WEBPACK_IMPORTED_MODULE_2__.useApp)();
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        toggleTaskStatus(task.id);
    }, [
        task.id,
        toggleTaskStatus
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        deleteTask(task.id);
    }, [
        task.id,
        deleteTask
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        onEdit === null || onEdit === void 0 ? void 0 : onEdit(task);
    }, [
        task,
        onEdit
    ]);
    const category = task.categoryId ? getCategoryById(task.categoryId) : null;
    const isOverdue = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.isTaskOverdue)(task);
    const isDueToday = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.isTaskDueToday)(task);
    const completionPercentage = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.getTaskCompletionPercentage)(task);
    const priorityColor = (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.getPriorityColor)(task.priority);
    const isCompleted = task.status === 'completed';
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_1, {
        values: [
            `task-item ${isCompleted ? 'completed' : ''} ${isOverdue ? 'overdue' : ''}`,
            {
                backgroundColor: priorityColor
            },
            1,
            1,
            {
                color: priorityColor
            }
        ],
        children: [
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_2, {
                values: [
                    `task-checkbox ${isCompleted ? 'checked' : ''}`,
                    1
                ],
                children: isCompleted && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_3, {}, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                    lineNumber: 51,
                    columnNumber: 29
                }, this)
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                lineNumber: 47,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_4, {
                values: [
                    `task-title ${isCompleted ? 'completed' : ''}`,
                    1
                ],
                children: task.title
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                lineNumber: 55,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: task.description && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_5, {
                    children: task.description
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                    lineNumber: 71,
                    columnNumber: 11
                }, this)
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: category && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_6, {
                    values: [
                        {
                            backgroundColor: category.color
                        }
                    ],
                    children: [
                        /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_7, {
                            children: category.icon
                        }, void 0, false, {
                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                            lineNumber: 79,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_8, {
                            children: category.name
                        }, void 0, false, {
                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                            lineNumber: 80,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                    lineNumber: 78,
                    columnNumber: 13
                }, this)
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: task.dueDate && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_9, {
                    values: [
                        `task-due-date ${isOverdue ? 'overdue' : isDueToday ? 'due-today' : ''}`
                    ],
                    children: (0,_utils_helpers__WEBPACK_IMPORTED_MODULE_3__.formatDate)(task.dueDate)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                    lineNumber: 86,
                    columnNumber: 13
                }, this)
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_10, {
                children: task.priority.toUpperCase()
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                lineNumber: 94,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: task.tags.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_11, {
                    children: task.tags.map((tagId)=>{
                        const tag = getTagById(tagId);
                        return tag ? /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_12, {
                            values: [
                                {
                                    backgroundColor: tag.color
                                }
                            ],
                            children: tag.name
                        }, tagId, false, {
                            fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                            lineNumber: 104,
                            columnNumber: 17
                        }, this) : null;
                    })
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                    lineNumber: 100,
                    columnNumber: 11
                }, this)
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: task.subtasks.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_66b74_0c624_13, {
                    values: [
                        {
                            width: `${completionPercentage}%`
                        }
                    ],
                    children: [
                        /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                            children: task.subtasks.filter((st)=>st.completed).length
                        }, void 0, false, void 0, this),
                        /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                            children: task.subtasks.length
                        }, void 0, false, void 0, this)
                    ]
                }, void 0, true, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
                    lineNumber: 118,
                    columnNumber: 11
                }, this)
            }, void 0, false, void 0, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),
"(react:main-thread)/./src/components/TaskList.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  TaskList: () => (TaskList)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _context_AppContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/context/AppContext.tsx");
/* ESM import */var _TaskItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__("(react:main-thread)/./src/components/TaskItem.tsx");
/* ESM import */var _AddTaskForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__("(react:main-thread)/./src/components/AddTaskForm.tsx");
/* ESM import */var _EditTaskForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__("(react:main-thread)/./src/components/EditTaskForm.tsx");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");






const __snapshot_36995_58dc4_2 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_2", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_3 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_3", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_4 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_4", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_5 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_5", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_6 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_6", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateText(pageId);
    __SetClasses(el, "stat-number");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_9 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_9", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-items");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_8 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_8", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-section");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "section-header");
    __AppendElement(el, el1);
    const el2 = __CreateText(pageId);
    __SetClasses(el2, "section-title");
    __AppendElement(el1, el2);
    const el3 = __CreateRawText("Pending Tasks (");
    __AppendElement(el2, el3);
    const el4 = __CreateWrapperElement(pageId);
    __AppendElement(el2, el4);
    const el5 = __CreateRawText(")");
    __AppendElement(el2, el5);
    const el6 = __CreateWrapperElement(pageId);
    __AppendElement(el, el6);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        4
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        6
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_11 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_11", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-items");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_10 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_10", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-section");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "section-header");
    __AppendElement(el, el1);
    const el2 = __CreateText(pageId);
    __SetClasses(el2, "section-title");
    __AppendElement(el1, el2);
    const el3 = __CreateRawText("Completed Tasks (");
    __AppendElement(el2, el3);
    const el4 = __CreateWrapperElement(pageId);
    __AppendElement(el2, el4);
    const el5 = __CreateRawText(")");
    __AppendElement(el2, el5);
    const el6 = __CreateWrapperElement(pageId);
    __AppendElement(el, el6);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        4
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        6
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_12 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_12", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "empty-state");
    const el1 = __CreateText(pageId);
    __SetClasses(el1, "empty-icon");
    __AppendElement(el, el1);
    const el2 = __CreateRawText("\u{1F4DD}");
    __AppendElement(el1, el2);
    const el3 = __CreateText(pageId);
    __SetClasses(el3, "empty-title");
    __AppendElement(el, el3);
    const el4 = __CreateRawText("No tasks yet");
    __AppendElement(el3, el4);
    const el5 = __CreateText(pageId);
    __SetClasses(el5, "empty-description");
    __AppendElement(el, el5);
    const el6 = __CreateRawText("Create your first task to get started with organizing your work!");
    __AppendElement(el5, el6);
    const el7 = __CreateView(pageId);
    __SetClasses(el7, "empty-action");
    __AppendElement(el, el7);
    const el8 = __CreateText(pageId);
    __SetClasses(el8, "empty-action-text");
    __AppendElement(el7, el8);
    const el9 = __CreateRawText("Create First Task");
    __AppendElement(el8, el9);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 7, "bindEvent", "tap", '')
], null, undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_7 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_7", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateScrollView(pageId);
    __SetClasses(el, "task-list-content");
    return [
        el
    ];
}, null, (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren_0 */.__DynamicPartChildren_0), undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_13 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_13", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "modal-overlay");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "modal-content");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_14 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_14", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "modal-overlay");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "modal-content");
    __AppendElement(el, el1);
    return [
        el,
        el1
    ];
}, null, [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartChildren */.__DynamicPartChildren),
        1
    ]
], undefined, globDynamicComponentEntry, null);
const __snapshot_36995_58dc4_1 = /*#__PURE__*/ (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .createSnapshot */.createSnapshot)("__snapshot_36995_58dc4_1", function() {
    const pageId = (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__pageId */.__pageId);
    const el = __CreateView(pageId);
    __SetClasses(el, "task-list");
    const el1 = __CreateView(pageId);
    __SetClasses(el1, "task-list-header");
    __AppendElement(el, el1);
    const el2 = __CreateView(pageId);
    __SetClasses(el2, "header-content");
    __AppendElement(el1, el2);
    const el3 = __CreateText(pageId);
    __SetClasses(el3, "header-title");
    __AppendElement(el2, el3);
    const el4 = __CreateRawText("My Tasks");
    __AppendElement(el3, el4);
    const el5 = __CreateView(pageId);
    __SetClasses(el5, "add-task-button");
    __AppendElement(el2, el5);
    const el6 = __CreateText(pageId);
    __SetClasses(el6, "add-button-text");
    __AppendElement(el5, el6);
    const el7 = __CreateRawText("+ Add Task");
    __AppendElement(el6, el7);
    const el8 = __CreateView(pageId);
    __SetClasses(el8, "task-stats");
    __AppendElement(el1, el8);
    const el9 = __CreateView(pageId);
    __SetClasses(el9, "stat-item");
    __AppendElement(el8, el9);
    const el10 = __CreateWrapperElement(pageId);
    __AppendElement(el9, el10);
    const el11 = __CreateText(pageId);
    __SetClasses(el11, "stat-label");
    __AppendElement(el9, el11);
    const el12 = __CreateRawText("Total");
    __AppendElement(el11, el12);
    const el13 = __CreateView(pageId);
    __SetClasses(el13, "stat-item");
    __AppendElement(el8, el13);
    const el14 = __CreateWrapperElement(pageId);
    __AppendElement(el13, el14);
    const el15 = __CreateText(pageId);
    __SetClasses(el15, "stat-label");
    __AppendElement(el13, el15);
    const el16 = __CreateRawText("Pending");
    __AppendElement(el15, el16);
    const el17 = __CreateView(pageId);
    __SetClasses(el17, "stat-item");
    __AppendElement(el8, el17);
    const el18 = __CreateWrapperElement(pageId);
    __AppendElement(el17, el18);
    const el19 = __CreateText(pageId);
    __SetClasses(el19, "stat-label");
    __AppendElement(el17, el19);
    const el20 = __CreateRawText("Completed");
    __AppendElement(el19, el20);
    const el21 = __CreateView(pageId);
    __SetClasses(el21, "stat-item overdue");
    __AppendElement(el8, el21);
    const el22 = __CreateWrapperElement(pageId);
    __AppendElement(el21, el22);
    const el23 = __CreateText(pageId);
    __SetClasses(el23, "stat-label");
    __AppendElement(el21, el23);
    const el24 = __CreateRawText("Overdue");
    __AppendElement(el23, el24);
    const el25 = __CreateView(pageId);
    __SetClasses(el25, "stat-item due-today");
    __AppendElement(el8, el25);
    const el26 = __CreateWrapperElement(pageId);
    __AppendElement(el25, el26);
    const el27 = __CreateText(pageId);
    __SetClasses(el27, "stat-label");
    __AppendElement(el25, el27);
    const el28 = __CreateRawText("Due Today");
    __AppendElement(el27, el28);
    const el29 = __CreateWrapperElement(pageId);
    __AppendElement(el, el29);
    const el30 = __CreateWrapperElement(pageId);
    __AppendElement(el, el30);
    const el31 = __CreateWrapperElement(pageId);
    __AppendElement(el, el31);
    return [
        el,
        el1,
        el2,
        el3,
        el4,
        el5,
        el6,
        el7,
        el8,
        el9,
        el10,
        el11,
        el12,
        el13,
        el14,
        el15,
        el16,
        el17,
        el18,
        el19,
        el20,
        el21,
        el22,
        el23,
        el24,
        el25,
        el26,
        el27,
        el28,
        el29,
        el30,
        el31
    ];
}, [
    (snapshot, index, oldValue)=>(__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .updateEvent */.updateEvent)(snapshot, index, oldValue, 5, "bindEvent", "tap", '')
], [
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        10
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        14
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        18
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        22
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        26
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        29
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        30
    ],
    [
        (__webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/internal.js")/* .__DynamicPartSlot */.__DynamicPartSlot),
        31
    ]
], undefined, globDynamicComponentEntry, null);
function TaskList() {
    const { getFilteredTasks, getTaskStats } = (0,_context_AppContext__WEBPACK_IMPORTED_MODULE_2__.useApp)();
    const [showAddForm, setShowAddForm] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [editingTask, setEditingTask] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const tasks = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getFilteredTasks(), [
        getFilteredTasks
    ]);
    const stats = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getTaskStats(), [
        getTaskStats
    ]);
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setShowAddForm(true);
    }, []);
    const handleCloseAddForm = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setShowAddForm(false);
    }, []);
    const handleEditTask = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((task)=>{
        setEditingTask(task);
    }, []);
    const handleCloseEditForm = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setEditingTask(null);
    }, []);
    const handleTaskAdded = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setShowAddForm(false);
    }, []);
    const handleTaskUpdated = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{
        setEditingTask(null);
    }, []);
    // Group tasks by status for better organization
    const pendingTasks = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>tasks.filter((task)=>task.status === 'pending'), [
        tasks
    ]);
    const completedTasks = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>tasks.filter((task)=>task.status === 'completed'), [
        tasks
    ]);
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_1, {
        values: [
            1
        ],
        children: [
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_2, {
                children: stats.total
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 64,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_3, {
                children: stats.pending
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 68,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_4, {
                children: stats.completed
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 72,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_5, {
                children: stats.overdue
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 76,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_6, {
                children: stats.dueToday
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 80,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_7, {
                children: [
                    pendingTasks.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_8, {
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: pendingTasks.length
                            }, void 0, false, void 0, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_9, {
                                children: pendingTasks.map((task)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskItem__WEBPACK_IMPORTED_MODULE_3__.TaskItem, {
                                        task: task,
                                        onEdit: handleEditTask
                                    }, task.id, false, {
                                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                                        lineNumber: 96,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                                lineNumber: 94,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 90,
                        columnNumber: 11
                    }, this),
                    completedTasks.length > 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_10, {
                        children: [
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                                children: completedTasks.length
                            }, void 0, false, void 0, this),
                            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_11, {
                                children: completedTasks.map((task)=>/*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskItem__WEBPACK_IMPORTED_MODULE_3__.TaskItem, {
                                        task: task,
                                        onEdit: handleEditTask
                                    }, task.id, false, {
                                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                                        lineNumber: 114,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                                lineNumber: 112,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 108,
                        columnNumber: 11
                    }, this),
                    tasks.length === 0 && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_12, {
                        values: [
                            1
                        ]
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 126,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                lineNumber: 87,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: showAddForm && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_13, {
                    children: /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddTaskForm__WEBPACK_IMPORTED_MODULE_4__.AddTaskForm, {
                        onClose: handleCloseAddForm,
                        onTaskAdded: handleTaskAdded
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 143,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, this)
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)("wrapper", {
                children: editingTask && /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(__snapshot_36995_58dc4_14, {
                    children: /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditTaskForm__WEBPACK_IMPORTED_MODULE_5__.EditTaskForm, {
                        task: editingTask,
                        onClose: handleCloseEditForm,
                        onTaskUpdated: handleTaskUpdated
                    }, void 0, false, {
                        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                        lineNumber: 155,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
                    lineNumber: 153,
                    columnNumber: 9
                }, this)
            }, void 0, false, void 0, this)
        ]
    }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskList.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),
"(react:main-thread)/./src/context/AppContext.tsx": (function (module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  AppProvider: () => (AppProvider),
  useApp: () => (useApp)
});
/* ESM import */var _lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lepus/jsx-runtime/index.js");
/* ESM import */var _lynx_js_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react/runtime/lib/index.js");
/* ESM import */var _appReducer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__("(react:main-thread)/./src/context/appReducer.ts");
/* module decorator */ module = __webpack_require__.hmd(module);
/* provided dependency */ var __prefresh_utils__ = __webpack_require__("(react:main-thread)/./node_modules/@lynx-js/react-refresh-webpack-plugin/runtime/refresh.cjs");



// Initial state
const initialSettings = {
    theme: 'light',
    defaultPriority: 'medium',
    enableNotifications: true,
    autoMarkOverdue: false,
    showCompletedTasks: true,
    taskSortOrder: {
        field: 'dueDate',
        direction: 'asc'
    }
};
const initialState = {
    tasks: [],
    categories: [
        {
            id: 'work',
            name: 'Work',
            color: '#3B82F6',
            icon: "\u{1F4BC}"
        },
        {
            id: 'personal',
            name: 'Personal',
            color: '#10B981',
            icon: "\u{1F3E0}"
        },
        {
            id: 'shopping',
            name: 'Shopping',
            color: '#F59E0B',
            icon: "\u{1F6D2}"
        },
        {
            id: 'health',
            name: 'Health',
            color: '#EF4444',
            icon: "\u{1F3E5}"
        }
    ],
    tags: [
        {
            id: 'urgent',
            name: 'Urgent',
            color: '#DC2626'
        },
        {
            id: 'important',
            name: 'Important',
            color: '#7C3AED'
        },
        {
            id: 'quick',
            name: 'Quick Task',
            color: '#059669'
        },
        {
            id: 'meeting',
            name: 'Meeting',
            color: '#2563EB'
        }
    ],
    filter: {},
    sort: {
        field: 'dueDate',
        direction: 'asc'
    },
    settings: initialSettings,
    isLoading: false
};
const AppContext = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);
function AppProvider({ children }) {
    const [state, dispatch] = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(_appReducer__WEBPACK_IMPORTED_MODULE_2__.appReducer, initialState);
    // Load data from localStorage on mount
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useEffect)();
    // Save data to localStorage whenever state changes
    (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useEffect)();
    // Helper functions
    const addTask = (task)=>{
        dispatch({
            type: 'ADD_TASK',
            payload: task
        });
    };
    const updateTask = (id, updates)=>{
        dispatch({
            type: 'UPDATE_TASK',
            payload: {
                id,
                updates
            }
        });
    };
    const deleteTask = (id)=>{
        dispatch({
            type: 'DELETE_TASK',
            payload: id
        });
    };
    const toggleTaskStatus = (id)=>{
        dispatch({
            type: 'TOGGLE_TASK_STATUS',
            payload: id
        });
    };
    const addCategory = (category)=>{
        dispatch({
            type: 'ADD_CATEGORY',
            payload: category
        });
    };
    const addTag = (tag)=>{
        dispatch({
            type: 'ADD_TAG',
            payload: tag
        });
    };
    const getTaskById = (id)=>{
        return state.tasks.find((task)=>task.id === id);
    };
    const getCategoryById = (id)=>{
        return state.categories.find((category)=>category.id === id);
    };
    const getTagById = (id)=>{
        return state.tags.find((tag)=>tag.id === id);
    };
    const getFilteredTasks = ()=>{
        let filteredTasks = [
            ...state.tasks
        ];
        const { filter } = state;
        // Apply filters
        if (filter.status && filter.status.length > 0) filteredTasks = filteredTasks.filter((task)=>filter.status.includes(task.status));
        if (filter.priority && filter.priority.length > 0) filteredTasks = filteredTasks.filter((task)=>filter.priority.includes(task.priority));
        if (filter.categoryId) filteredTasks = filteredTasks.filter((task)=>task.categoryId === filter.categoryId);
        if (filter.tagIds && filter.tagIds.length > 0) filteredTasks = filteredTasks.filter((task)=>filter.tagIds.some((tagId)=>task.tags.includes(tagId)));
        if (filter.searchQuery) {
            const query = filter.searchQuery.toLowerCase();
            filteredTasks = filteredTasks.filter((task)=>{
                var _task_description;
                return task.title.toLowerCase().includes(query) || ((_task_description = task.description) === null || _task_description === void 0 ? void 0 : _task_description.toLowerCase().includes(query));
            });
        }
        // Apply sorting
        filteredTasks.sort((a, b)=>{
            const { field, direction } = state.sort;
            let aValue = a[field];
            let bValue = b[field];
            if (field === 'dueDate') {
                var _a_dueDate, _b_dueDate;
                aValue = ((_a_dueDate = a.dueDate) === null || _a_dueDate === void 0 ? void 0 : _a_dueDate.getTime()) || Infinity;
                bValue = ((_b_dueDate = b.dueDate) === null || _b_dueDate === void 0 ? void 0 : _b_dueDate.getTime()) || Infinity;
            } else if (field === 'priority') {
                const priorityOrder = {
                    low: 1,
                    medium: 2,
                    high: 3
                };
                aValue = priorityOrder[a.priority];
                bValue = priorityOrder[b.priority];
            } else if (aValue instanceof Date) {
                aValue = aValue.getTime();
                bValue = bValue.getTime();
            }
            if (direction === 'asc') return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
            else return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        });
        return filteredTasks;
    };
    const getTaskStats = ()=>{
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const weekFromNow = new Date(today.getTime() + 604800000);
        const stats = {
            total: state.tasks.length,
            completed: state.tasks.filter((task)=>task.status === 'completed').length,
            pending: state.tasks.filter((task)=>task.status === 'pending').length,
            overdue: state.tasks.filter((task)=>task.status === 'pending' && task.dueDate && task.dueDate < today).length,
            dueToday: state.tasks.filter((task)=>task.status === 'pending' && task.dueDate && task.dueDate >= today && task.dueDate < new Date(today.getTime() + 86400000)).length,
            dueThisWeek: state.tasks.filter((task)=>task.status === 'pending' && task.dueDate && task.dueDate >= today && task.dueDate < weekFromNow).length
        };
        return stats;
    };
    const contextValue = {
        state,
        dispatch,
        addTask,
        updateTask,
        deleteTask,
        toggleTaskStatus,
        addCategory,
        addTag,
        getTaskById,
        getCategoryById,
        getTagById,
        getFilteredTasks,
        getTaskStats
    };
    return /*#__PURE__*/ (0,_lynx_js_react_lepus_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\context\\AppContext.tsx",
        lineNumber: 253,
        columnNumber: 5
    }, this);
}
// Hook to use the context
function useApp() {
    const context = (0,_lynx_js_react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext);
    if (context === undefined) throw new Error('useApp must be used within an AppProvider');
    return context;
}
// @ts-nocheck
const isPrefreshComponent = __prefresh_utils__.shouldBind(module);
const moduleHot = module.hot;
if (moduleHot) {
    const currentExports = __prefresh_utils__.getExports(module);
    const previousHotModuleExports = moduleHot.data && moduleHot.data.moduleExports;
    __prefresh_utils__.registerExports(currentExports, module.id);
    if (isPrefreshComponent) {
        if (previousHotModuleExports) try {
            __prefresh_utils__.flush();
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.clearRuntimeErrors) __prefresh_errors__.clearRuntimeErrors();
        } catch (e) {
            // Only available in newer webpack versions.
            if (moduleHot.invalidate) moduleHot.invalidate();
            else globalThis.location.reload();
        }
        moduleHot.dispose((data)=>{
            data.moduleExports = __prefresh_utils__.getExports(module);
        });
        moduleHot.accept(function errorRecovery() {
            if (typeof __prefresh_errors__ !== 'undefined' && __prefresh_errors__ && __prefresh_errors__.handleRuntimeError) __prefresh_errors__.handleRuntimeError(error);
            __webpack_require__.c[module.id].hot.accept(errorRecovery);
        });
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),

};
exports.runtime = function(__webpack_require__) {
// webpack/runtime/get_full_hash
(() => {
__webpack_require__.h = () => ("75a4dab894e07586")
})();

}
;
;

})();
    });
    return tt.require("main__main-thread.7321fd8b079d9534.hot-update.js");
  };
  if (g && g.bundleSupportLoadScript){
    var res = {init: __init_card_bundle__};
    g.__bundle__holder = res;
    return res;
  } else {
    __init_card_bundle__({"tt": tt});
  };
})();

//# sourceMappingURL=http://**************:3000/main__main-thread.7321fd8b079d9534.hot-update.js.map
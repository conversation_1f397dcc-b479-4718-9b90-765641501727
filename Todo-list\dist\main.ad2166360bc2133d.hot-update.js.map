{"version": 3, "file": "main.ad2166360bc2133d.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\context\\AppContext.tsx", "file://webpack/runtime/get_full_hash"], "sourcesContent": ["import { createContext, useContext, useReducer, useEffect, type ReactNode } from '@lynx-js/react';\nimport type { AppState, AppAction, Task, Category, Tag, AppSettings, TaskSort } from '../types/index.js';\nimport { appReducer } from './appReducer.js';\nimport { generateId } from '../utils/helpers.js';\n\n// Initial state\nconst initialSettings: AppSettings = {\n  theme: 'light',\n  defaultPriority: 'medium',\n  enableNotifications: true,\n  autoMarkOverdue: false,\n  showCompletedTasks: true,\n  taskSortOrder: { field: 'dueDate', direction: 'asc' }\n};\n\nconst initialState: AppState = {\n  tasks: [],\n  categories: [\n    { id: 'work', name: 'Work', color: '#3B82F6', icon: '💼' },\n    { id: 'personal', name: 'Personal', color: '#10B981', icon: '🏠' },\n    { id: 'shopping', name: 'Shopping', color: '#F59E0B', icon: '🛒' },\n    { id: 'health', name: 'Health', color: '#EF4444', icon: '🏥' }\n  ],\n  tags: [\n    { id: 'urgent', name: 'Urgent', color: '#DC2626' },\n    { id: 'important', name: 'Important', color: '#7C3AED' },\n    { id: 'quick', name: 'Quick Task', color: '#059669' },\n    { id: 'meeting', name: 'Meeting', color: '#2563EB' }\n  ],\n  filter: {},\n  sort: { field: 'dueDate', direction: 'asc' },\n  settings: initialSettings,\n  isLoading: false\n};\n\n// Context\ninterface AppContextType {\n  state: AppState;\n  dispatch: React.Dispatch<AppAction>;\n  // Helper functions\n  addTask: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateTask: (id: string, updates: Partial<Task>) => void;\n  deleteTask: (id: string) => void;\n  toggleTaskStatus: (id: string) => void;\n  addCategory: (category: Omit<Category, 'id'>) => void;\n  addTag: (tag: Omit<Tag, 'id'>) => void;\n  getTaskById: (id: string) => Task | undefined;\n  getCategoryById: (id: string) => Category | undefined;\n  getTagById: (id: string) => Tag | undefined;\n  getFilteredTasks: () => Task[];\n  getTaskStats: () => {\n    total: number;\n    completed: number;\n    pending: number;\n    overdue: number;\n    dueToday: number;\n    dueThisWeek: number;\n  };\n}\n\nconst AppContext = createContext<AppContextType | undefined>(undefined);\n\n// Provider component\ninterface AppProviderProps {\n  children: ReactNode;\n}\n\nexport function AppProvider({ children }: AppProviderProps) {\n  const [state, dispatch] = useReducer(appReducer, initialState);\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const savedData = localStorage.getItem('todoApp');\n    if (savedData) {\n      try {\n        const parsedData = JSON.parse(savedData);\n        // Convert date strings back to Date objects\n        const tasks = parsedData.tasks?.map((task: any) => ({\n          ...task,\n          createdAt: new Date(task.createdAt),\n          updatedAt: new Date(task.updatedAt),\n          dueDate: task.dueDate ? new Date(task.dueDate) : undefined,\n          completedAt: task.completedAt ? new Date(task.completedAt) : undefined,\n          subtasks: task.subtasks?.map((subtask: any) => ({\n            ...subtask,\n            createdAt: new Date(subtask.createdAt)\n          })) || []\n        })) || [];\n        \n        dispatch({\n          type: 'LOAD_DATA',\n          payload: {\n            tasks,\n            categories: parsedData.categories || initialState.categories,\n            tags: parsedData.tags || initialState.tags\n          }\n        });\n      } catch (error) {\n        console.error('Failed to load saved data:', error);\n      }\n    }\n  }, []);\n\n  // Save data to localStorage whenever state changes\n  useEffect(() => {\n    const dataToSave = {\n      tasks: state.tasks,\n      categories: state.categories,\n      tags: state.tags,\n      settings: state.settings\n    };\n    localStorage.setItem('todoApp', JSON.stringify(dataToSave));\n  }, [state.tasks, state.categories, state.tags, state.settings]);\n\n  // Helper functions\n  const addTask = (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => {\n    dispatch({ type: 'ADD_TASK', payload: task });\n  };\n\n  const updateTask = (id: string, updates: Partial<Task>) => {\n    dispatch({ type: 'UPDATE_TASK', payload: { id, updates } });\n  };\n\n  const deleteTask = (id: string) => {\n    dispatch({ type: 'DELETE_TASK', payload: id });\n  };\n\n  const toggleTaskStatus = (id: string) => {\n    dispatch({ type: 'TOGGLE_TASK_STATUS', payload: id });\n  };\n\n  const addCategory = (category: Omit<Category, 'id'>) => {\n    dispatch({ type: 'ADD_CATEGORY', payload: category });\n  };\n\n  const addTag = (tag: Omit<Tag, 'id'>) => {\n    dispatch({ type: 'ADD_TAG', payload: tag });\n  };\n\n  const getTaskById = (id: string): Task | undefined => {\n    return state.tasks.find(task => task.id === id);\n  };\n\n  const getCategoryById = (id: string): Category | undefined => {\n    return state.categories.find(category => category.id === id);\n  };\n\n  const getTagById = (id: string): Tag | undefined => {\n    return state.tags.find(tag => tag.id === id);\n  };\n\n  const getFilteredTasks = (): Task[] => {\n    let filteredTasks = [...state.tasks];\n    const { filter } = state;\n\n    // Apply filters\n    if (filter.status && filter.status.length > 0) {\n      filteredTasks = filteredTasks.filter(task => filter.status!.includes(task.status));\n    }\n\n    if (filter.priority && filter.priority.length > 0) {\n      filteredTasks = filteredTasks.filter(task => filter.priority!.includes(task.priority));\n    }\n\n    if (filter.categoryId) {\n      filteredTasks = filteredTasks.filter(task => task.categoryId === filter.categoryId);\n    }\n\n    if (filter.tagIds && filter.tagIds.length > 0) {\n      filteredTasks = filteredTasks.filter(task => \n        filter.tagIds!.some(tagId => task.tags.includes(tagId))\n      );\n    }\n\n    if (filter.searchQuery) {\n      const query = filter.searchQuery.toLowerCase();\n      filteredTasks = filteredTasks.filter(task => \n        task.title.toLowerCase().includes(query) ||\n        task.description?.toLowerCase().includes(query)\n      );\n    }\n\n    // Apply sorting\n    filteredTasks.sort((a, b) => {\n      const { field, direction } = state.sort;\n      let aValue: any = a[field];\n      let bValue: any = b[field];\n\n      if (field === 'dueDate') {\n        aValue = a.dueDate?.getTime() || Infinity;\n        bValue = b.dueDate?.getTime() || Infinity;\n      } else if (field === 'priority') {\n        const priorityOrder = { low: 1, medium: 2, high: 3 };\n        aValue = priorityOrder[a.priority];\n        bValue = priorityOrder[b.priority];\n      } else if (aValue instanceof Date) {\n        aValue = aValue.getTime();\n        bValue = bValue.getTime();\n      }\n\n      if (direction === 'asc') {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n\n    return filteredTasks;\n  };\n\n  const getTaskStats = () => {\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n\n    const stats = {\n      total: state.tasks.length,\n      completed: state.tasks.filter(task => task.status === 'completed').length,\n      pending: state.tasks.filter(task => task.status === 'pending').length,\n      overdue: state.tasks.filter(task => \n        task.status === 'pending' && task.dueDate && task.dueDate < today\n      ).length,\n      dueToday: state.tasks.filter(task => \n        task.status === 'pending' && task.dueDate && \n        task.dueDate >= today && task.dueDate < new Date(today.getTime() + 24 * 60 * 60 * 1000)\n      ).length,\n      dueThisWeek: state.tasks.filter(task => \n        task.status === 'pending' && task.dueDate && \n        task.dueDate >= today && task.dueDate < weekFromNow\n      ).length\n    };\n\n    return stats;\n  };\n\n  const contextValue: AppContextType = {\n    state,\n    dispatch,\n    addTask,\n    updateTask,\n    deleteTask,\n    toggleTaskStatus,\n    addCategory,\n    addTag,\n    getTaskById,\n    getCategoryById,\n    getTagById,\n    getFilteredTasks,\n    getTaskStats\n  };\n\n  return (\n    <AppContext.Provider value={contextValue}>\n      {children}\n    </AppContext.Provider>\n  );\n}\n\n// Hook to use the context\nexport function useApp() {\n  const context = useContext(AppContext);\n  if (context === undefined) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "__webpack_require__.h = () => (\"2204caf890eedfe7\")"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AA2BA;AAOA;AACA;AAEA;AACA;AACA;AACA;AAIA;AAFA;AACA;AACA;AAMA;AANA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AAIA;AAIA;AAIA;AAMA;AACA;AACA;AAAA;AAAA;;AAIA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAGA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAGA;AAIA;AAIA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;AAGA;AAEA;AACA;AACA;AACA;AAGA;AACA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;AC7TA"}
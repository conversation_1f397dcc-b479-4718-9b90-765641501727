(function(){
  'use strict';
  var g = (new Function('return this;'))();
  function __init_card_bundle__(lynxCoreInject) {
    g.__bundle__holder = undefined;
    var globDynamicComponentEntry = g.globDynamicComponentEntry || '__Card__';
    var tt = lynxCoreInject.tt;
    tt.define("main__main-thread.2204caf890eedfe7.hot-update.js", function(require, module, exports, __Card,setTimeout,setInterval,clearInterval,clearTimeout,NativeModules,tt,console,__Component,__ReactLynx,nativeAppId,__Behavior,LynxJSBI,lynx,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,fetch,XMLHttpRequest,__WebSocket__,webkit,Reporter,print,global,requestAnimationFrame,cancelAnimationFrame) {
lynx = lynx || {};
lynx.targetSdkVersion=lynx.targetSdkVersion||"3.2";
var Promise = lynx.Promise;
fetch = fetch || lynx.fetch;
requestAnimationFrame = requestAnimationFrame || lynx.requestAnimationFrame;
cancelAnimationFrame = cancelAnimationFrame || lynx.cancelAnimationFrame;

// This needs to be wrapped in an IIFE because it needs to be isolated against Lynx injected variables.
(() => {
// lynx chunks entries
if (!lynx.__chunk_entries__) {
  // Initialize once
  lynx.__chunk_entries__ = {};
}
if (!lynx.__chunk_entries__["main__main-thread"]) {
  lynx.__chunk_entries__["main__main-thread"] = globDynamicComponentEntry;
} else {
  globDynamicComponentEntry = lynx.__chunk_entries__["main__main-thread"];
}

"use strict";
exports.ids = ["main__main-thread"];
exports.modules = {
"(react:main-thread)/./src/context/appReducer.ts": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  appReducer: () => (appReducer)
});
/* ESM import */var _utils_helpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("(react:main-thread)/./src/utils/helpers.ts");

function appReducer(state, action) {
    switch(action.type){
        case 'SET_LOADING':
            return {
                ...state,
                isLoading: action.payload
            };
        case 'SET_ERROR':
            return {
                ...state,
                error: action.payload
            };
        case 'ADD_TASK':
            {
                const now = new Date();
                const newTask = {
                    ...action.payload,
                    id: (0,_utils_helpers_js__WEBPACK_IMPORTED_MODULE_0__.generateId)(),
                    createdAt: now,
                    updatedAt: now,
                    status: action.payload.status || 'pending',
                    priority: action.payload.priority || 'medium',
                    tags: action.payload.tags || [],
                    subtasks: action.payload.subtasks || []
                };
                return {
                    ...state,
                    tasks: [
                        ...state.tasks,
                        newTask
                    ]
                };
            }
        case 'UPDATE_TASK':
            {
                const { id, updates } = action.payload;
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>task.id === id ? {
                            ...task,
                            ...updates,
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'DELETE_TASK':
            return {
                ...state,
                tasks: state.tasks.filter((task)=>task.id !== action.payload),
                selectedTaskId: state.selectedTaskId === action.payload ? undefined : state.selectedTaskId
            };
        case 'TOGGLE_TASK_STATUS':
            {
                const taskId = action.payload;
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>{
                        if (task.id === taskId) {
                            const newStatus = task.status === 'completed' ? 'pending' : 'completed';
                            const now = new Date();
                            return {
                                ...task,
                                status: newStatus,
                                completedAt: newStatus === 'completed' ? now : undefined,
                                updatedAt: now
                            };
                        }
                        return task;
                    })
                };
            }
        case 'ADD_SUBTASK':
            {
                const { taskId, subtask } = action.payload;
                const newSubtask = {
                    ...subtask,
                    id: (0,_utils_helpers_js__WEBPACK_IMPORTED_MODULE_0__.generateId)(),
                    createdAt: new Date()
                };
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>task.id === taskId ? {
                            ...task,
                            subtasks: [
                                ...task.subtasks,
                                newSubtask
                            ],
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'UPDATE_SUBTASK':
            {
                const { taskId, subtaskId, updates } = action.payload;
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>task.id === taskId ? {
                            ...task,
                            subtasks: task.subtasks.map((subtask)=>subtask.id === subtaskId ? {
                                    ...subtask,
                                    ...updates
                                } : subtask),
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'DELETE_SUBTASK':
            {
                const { taskId, subtaskId } = action.payload;
                return {
                    ...state,
                    tasks: state.tasks.map((task)=>task.id === taskId ? {
                            ...task,
                            subtasks: task.subtasks.filter((subtask)=>subtask.id !== subtaskId),
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'ADD_CATEGORY':
            {
                const newCategory = {
                    ...action.payload,
                    id: (0,_utils_helpers_js__WEBPACK_IMPORTED_MODULE_0__.generateId)()
                };
                return {
                    ...state,
                    categories: [
                        ...state.categories,
                        newCategory
                    ]
                };
            }
        case 'UPDATE_CATEGORY':
            {
                const { id, updates } = action.payload;
                return {
                    ...state,
                    categories: state.categories.map((category)=>category.id === id ? {
                            ...category,
                            ...updates
                        } : category)
                };
            }
        case 'DELETE_CATEGORY':
            {
                const categoryId = action.payload;
                return {
                    ...state,
                    categories: state.categories.filter((category)=>category.id !== categoryId),
                    // Remove category from tasks
                    tasks: state.tasks.map((task)=>task.categoryId === categoryId ? {
                            ...task,
                            categoryId: undefined,
                            updatedAt: new Date()
                        } : task)
                };
            }
        case 'ADD_TAG':
            {
                const newTag = {
                    ...action.payload,
                    id: (0,_utils_helpers_js__WEBPACK_IMPORTED_MODULE_0__.generateId)()
                };
                return {
                    ...state,
                    tags: [
                        ...state.tags,
                        newTag
                    ]
                };
            }
        case 'UPDATE_TAG':
            {
                const { id, updates } = action.payload;
                return {
                    ...state,
                    tags: state.tags.map((tag)=>tag.id === id ? {
                            ...tag,
                            ...updates
                        } : tag)
                };
            }
        case 'DELETE_TAG':
            {
                const tagId = action.payload;
                return {
                    ...state,
                    tags: state.tags.filter((tag)=>tag.id !== tagId),
                    // Remove tag from tasks
                    tasks: state.tasks.map((task)=>({
                            ...task,
                            tags: task.tags.filter((id)=>id !== tagId),
                            updatedAt: task.tags.includes(tagId) ? new Date() : task.updatedAt
                        }))
                };
            }
        case 'SET_FILTER':
            return {
                ...state,
                filter: {
                    ...state.filter,
                    ...action.payload
                }
            };
        case 'CLEAR_FILTER':
            return {
                ...state,
                filter: {}
            };
        case 'SET_SORT':
            return {
                ...state,
                sort: action.payload
            };
        case 'SELECT_TASK':
            return {
                ...state,
                selectedTaskId: action.payload
            };
        case 'UPDATE_SETTINGS':
            return {
                ...state,
                settings: {
                    ...state.settings,
                    ...action.payload
                }
            };
        case 'LOAD_DATA':
            {
                const { tasks, categories, tags } = action.payload;
                return {
                    ...state,
                    tasks,
                    categories,
                    tags
                };
            }
        case 'RESET_APP':
            return {
                ...state,
                tasks: [],
                categories: state.categories,
                tags: state.tags,
                filter: {},
                selectedTaskId: undefined,
                error: undefined
            };
        default:
            return state;
    }
}
// noop fns to prevent runtime errors during initialization
if (typeof globalThis !== "undefined") {
    globalThis.$RefreshReg$ = function() {};
    globalThis.$RefreshSig$ = function() {
        return function(type) {
            return type;
        };
    };
}


}),

};
exports.runtime = function(__webpack_require__) {
// webpack/runtime/get_full_hash
(() => {
__webpack_require__.h = () => ("97a164f40675830d")
})();

}
;
;

})();
    });
    return tt.require("main__main-thread.2204caf890eedfe7.hot-update.js");
  };
  if (g && g.bundleSupportLoadScript){
    var res = {init: __init_card_bundle__};
    g.__bundle__holder = res;
    return res;
  } else {
    __init_card_bundle__({"tt": tt});
  };
})();

//# sourceMappingURL=http://**************:3000/main__main-thread.2204caf890eedfe7.hot-update.js.map
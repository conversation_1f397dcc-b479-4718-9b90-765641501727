{"version": 3, "file": "main.992f5f6f82a0312f.hot-update.js", "sources": ["file://C:\\Users\\<USER>\\OneDrive\\Desktop\\Todo-list\\Todo-list\\src\\components\\TaskItem.tsx", "file://webpack/runtime/get_full_hash"], "sourcesContent": ["import { useCallback } from '@lynx-js/react';\nimport type { Task, Priority } from '../types/index.js';\nimport { useApp } from '../context/AppContext.js';\nimport { formatDate, isTaskOverdue, isTaskDueToday, getPriorityColor, getTaskCompletionPercentage } from '../utils/helpers.js';\n\ninterface TaskItemProps {\n  task: Task;\n  onEdit?: (task: Task) => void;\n}\n\nexport function TaskItem({ task, onEdit }: TaskItemProps) {\n  const { toggleTaskStatus, deleteTask, getCategoryById, getTagById } = useApp();\n\n  const handleToggleComplete = useCallback(() => {\n    toggleTaskStatus(task.id);\n  }, [task.id, toggleTaskStatus]);\n\n  const handleDelete = useCallback(() => {\n    deleteTask(task.id);\n  }, [task.id, deleteTask]);\n\n  const handleEdit = useCallback(() => {\n    onEdit?.(task);\n  }, [task, onEdit]);\n\n  const category = task.categoryId ? getCategoryById(task.categoryId) : null;\n  const isOverdue = isTaskOverdue(task);\n  const isDueToday = isTaskDueToday(task);\n  const completionPercentage = getTaskCompletionPercentage(task);\n\n  const priorityColor = getPriorityColor(task.priority);\n  const isCompleted = task.status === 'completed';\n\n  return (\n    <view className={`task-item ${isCompleted ? 'completed' : ''} ${isOverdue ? 'overdue' : ''}`}>\n      {/* Priority indicator */}\n      <view \n        className=\"priority-indicator\"\n        style={{ backgroundColor: priorityColor }}\n      />\n      \n      {/* Main content */}\n      <view className=\"task-content\">\n        {/* Header */}\n        <view className=\"task-header\">\n          {/* Checkbox */}\n          <view \n            className={`task-checkbox ${isCompleted ? 'checked' : ''}`}\n            bindtap={handleToggleComplete}\n          >\n            {isCompleted && <text className=\"checkmark\">✓</text>}\n          </view>\n          \n          {/* Title */}\n          <text \n            className={`task-title ${isCompleted ? 'completed' : ''}`}\n            bindtap={handleEdit}\n          >\n            {task.title}\n          </text>\n          \n          {/* Actions */}\n          <view className=\"task-actions\">\n            <text className=\"action-button edit\" bindtap={handleEdit}>✏️</text>\n            <text className=\"action-button delete\" bindtap={handleDelete}>🗑️</text>\n          </view>\n        </view>\n\n        {/* Description */}\n        {task.description && (\n          <text className=\"task-description\">{task.description}</text>\n        )}\n\n        {/* Metadata */}\n        <view className=\"task-metadata\">\n          {/* Category */}\n          {category && (\n            <view className=\"task-category\" style={{ backgroundColor: category.color }}>\n              <text className=\"category-icon\">{category.icon}</text>\n              <text className=\"category-name\">{category.name}</text>\n            </view>\n          )}\n\n          {/* Due date */}\n          {task.dueDate && (\n            <view className={`task-due-date ${isOverdue ? 'overdue' : isDueToday ? 'due-today' : ''}`}>\n              <text className=\"due-date-icon\">📅</text>\n              <text className=\"due-date-text\">{formatDate(task.dueDate)}</text>\n            </view>\n          )}\n\n          {/* Priority */}\n          <view className=\"task-priority\" style={{ color: priorityColor }}>\n            <text className=\"priority-text\">{task.priority.toUpperCase()}</text>\n          </view>\n        </view>\n\n        {/* Tags */}\n        {task.tags.length > 0 && (\n          <view className=\"task-tags\">\n            {task.tags.map(tagId => {\n              const tag = getTagById(tagId);\n              return tag ? (\n                <view \n                  key={tagId}\n                  className=\"task-tag\"\n                  style={{ backgroundColor: tag.color }}\n                >\n                  <text className=\"tag-text\">{tag.name}</text>\n                </view>\n              ) : null;\n            })}\n          </view>\n        )}\n\n        {/* Subtasks progress */}\n        {task.subtasks.length > 0 && (\n          <view className=\"subtasks-progress\">\n            <view className=\"progress-bar\">\n              <view \n                className=\"progress-fill\"\n                style={{ width: `${completionPercentage}%` }}\n              />\n            </view>\n            <text className=\"progress-text\">\n              {task.subtasks.filter(st => st.completed).length}/{task.subtasks.length} subtasks\n            </text>\n          </view>\n        )}\n      </view>\n    </view>\n  );\n}\n\n\n// @ts-nocheck\nconst isPrefreshComponent = __prefresh_utils__.shouldBind(module);\n\nconst moduleHot = module.hot;\n\nif (moduleHot) {\n  const currentExports = __prefresh_utils__.getExports(module);\n  const previousHotModuleExports = moduleHot.data\n    && moduleHot.data.moduleExports;\n\n  __prefresh_utils__.registerExports(currentExports, module.id);\n\n  if (isPrefreshComponent) {\n    if (previousHotModuleExports) {\n      try {\n        __prefresh_utils__.flush();\n        if (\n          typeof __prefresh_errors__ !== 'undefined'\n          && __prefresh_errors__\n          && __prefresh_errors__.clearRuntimeErrors\n        ) {\n          __prefresh_errors__.clearRuntimeErrors();\n        }\n      } catch (e) {\n        // Only available in newer webpack versions.\n        if (moduleHot.invalidate) {\n          moduleHot.invalidate();\n        } else {\n          globalThis.location.reload();\n        }\n      }\n    }\n\n    moduleHot.dispose(data => {\n      data.moduleExports = __prefresh_utils__.getExports(module);\n    });\n\n    moduleHot.accept(function errorRecovery() {\n      if (\n        typeof __prefresh_errors__ !== 'undefined'\n        && __prefresh_errors__\n        && __prefresh_errors__.handleRuntimeError\n      ) {\n        __prefresh_errors__.handleRuntimeError(error);\n      }\n\n      require.cache[module.id].hot.accept(errorRecovery);\n    });\n  }\n}\n", "__webpack_require__.h = () => (\"bf6ccfc02c254786\")"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;;;;AA+CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;;;AAQA;;;;;;;;AACA;;;;;;;;AAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA;;;;;AACA;;;;;;;;;;;;;;;;;;;;;AAMA;;;;;;;;AAYA;;AAGA;;;;;;;;;;;;;;;;;;;AATA;;;;;;;;AAkBA;;AACA;;;AAEA;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvFA;;;AAKA;;;AAEA;;;;;;;AAkBA;;;AACA;;;;;AACA;;;;;;;AAUA;;;;;;;AAkBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAlFA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;;AACA;AAIA;AAAA;AAAA;AAyBA;AACA;AA4BA;AAAA;AAAA;;;AA9CA;;AACA;AACA;;AAEA;;;;;;;;;;AAIA;;AACA;AACA;;AAEA;;;;;;;AAWA;AACA;;;;;;;;AAMA;;AACA;AAAA;AAAA;;;AACA;AAAA;;;;;;AACA;AAAA;;;;;;;;;;;;;;AAKA;;AACA;;AAEA;;;;;;;AAMA;AAAA;;;;;;;AAKA;AAEA;AACA;AACA;;AAIA;AAAA;AAAA;;AAEA;AAJA;;;;AAMA;AACA;;;;;;;;AAKA;;AAKA;AAAA;AAAA;;;;AAIA;;;AAAA;;;;;;;;;;;;;;;AAOA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AACA;AAOA;AACA;AACA;AAGA;AAEA;AAGA;AACA;AACA;AAEA;AACA;AAQA;AACA;AACA;AACA;;;;;;;;;ACxLA"}
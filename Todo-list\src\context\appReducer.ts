import type { AppState, AppAction, Task, Category, Tag } from '../types/index.js';
import { generateId } from '../utils/helpers.js';

export function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload
      };

    case 'ADD_TASK': {
      const now = new Date();
      const newTask: Task = {
        ...action.payload,
        id: generateId(),
        createdAt: now,
        updatedAt: now,
        status: action.payload.status || 'pending',
        priority: action.payload.priority || 'medium',
        tags: action.payload.tags || [],
        subtasks: action.payload.subtasks || []
      };

      return {
        ...state,
        tasks: [...state.tasks, newTask]
      };
    }

    case 'UPDATE_TASK': {
      const { id, updates } = action.payload;
      return {
        ...state,
        tasks: state.tasks.map(task =>
          task.id === id
            ? { ...task, ...updates, updatedAt: new Date() }
            : task
        )
      };
    }

    case 'DELETE_TASK':
      return {
        ...state,
        tasks: state.tasks.filter(task => task.id !== action.payload),
        selectedTaskId: state.selectedTaskId === action.payload ? undefined : state.selectedTaskId
      };

    case 'TOGGLE_TASK_STATUS': {
      const taskId = action.payload;
      return {
        ...state,
        tasks: state.tasks.map(task => {
          if (task.id === taskId) {
            const newStatus = task.status === 'completed' ? 'pending' : 'completed';
            const now = new Date();
            return {
              ...task,
              status: newStatus,
              completedAt: newStatus === 'completed' ? now : undefined,
              updatedAt: now
            };
          }
          return task;
        })
      };
    }

    case 'ADD_SUBTASK': {
      const { taskId, subtask } = action.payload;
      const newSubtask = {
        ...subtask,
        id: generateId(),
        createdAt: new Date()
      };

      return {
        ...state,
        tasks: state.tasks.map(task =>
          task.id === taskId
            ? {
                ...task,
                subtasks: [...task.subtasks, newSubtask],
                updatedAt: new Date()
              }
            : task
        )
      };
    }

    case 'UPDATE_SUBTASK': {
      const { taskId, subtaskId, updates } = action.payload;
      return {
        ...state,
        tasks: state.tasks.map(task =>
          task.id === taskId
            ? {
                ...task,
                subtasks: task.subtasks.map(subtask =>
                  subtask.id === subtaskId
                    ? { ...subtask, ...updates }
                    : subtask
                ),
                updatedAt: new Date()
              }
            : task
        )
      };
    }

    case 'DELETE_SUBTASK': {
      const { taskId, subtaskId } = action.payload;
      return {
        ...state,
        tasks: state.tasks.map(task =>
          task.id === taskId
            ? {
                ...task,
                subtasks: task.subtasks.filter(subtask => subtask.id !== subtaskId),
                updatedAt: new Date()
              }
            : task
        )
      };
    }

    case 'ADD_CATEGORY': {
      const newCategory: Category = {
        ...action.payload,
        id: generateId()
      };

      return {
        ...state,
        categories: [...state.categories, newCategory]
      };
    }

    case 'UPDATE_CATEGORY': {
      const { id, updates } = action.payload;
      return {
        ...state,
        categories: state.categories.map(category =>
          category.id === id
            ? { ...category, ...updates }
            : category
        )
      };
    }

    case 'DELETE_CATEGORY': {
      const categoryId = action.payload;
      return {
        ...state,
        categories: state.categories.filter(category => category.id !== categoryId),
        // Remove category from tasks
        tasks: state.tasks.map(task =>
          task.categoryId === categoryId
            ? { ...task, categoryId: undefined, updatedAt: new Date() }
            : task
        )
      };
    }

    case 'ADD_TAG': {
      const newTag: Tag = {
        ...action.payload,
        id: generateId()
      };

      return {
        ...state,
        tags: [...state.tags, newTag]
      };
    }

    case 'UPDATE_TAG': {
      const { id, updates } = action.payload;
      return {
        ...state,
        tags: state.tags.map(tag =>
          tag.id === id
            ? { ...tag, ...updates }
            : tag
        )
      };
    }

    case 'DELETE_TAG': {
      const tagId = action.payload;
      return {
        ...state,
        tags: state.tags.filter(tag => tag.id !== tagId),
        // Remove tag from tasks
        tasks: state.tasks.map(task => ({
          ...task,
          tags: task.tags.filter(id => id !== tagId),
          updatedAt: task.tags.includes(tagId) ? new Date() : task.updatedAt
        }))
      };
    }

    case 'SET_FILTER':
      return {
        ...state,
        filter: { ...state.filter, ...action.payload }
      };

    case 'CLEAR_FILTER':
      return {
        ...state,
        filter: {}
      };

    case 'SET_SORT':
      return {
        ...state,
        sort: action.payload
      };

    case 'SELECT_TASK':
      return {
        ...state,
        selectedTaskId: action.payload
      };

    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload }
      };

    case 'LOAD_DATA': {
      const { tasks, categories, tags } = action.payload;
      return {
        ...state,
        tasks,
        categories,
        tags
      };
    }

    case 'RESET_APP':
      return {
        ...state,
        tasks: [],
        categories: state.categories, // Keep default categories
        tags: state.tags, // Keep default tags
        filter: {},
        selectedTaskId: undefined,
        error: undefined
      };

    default:
      return state;
  }
}

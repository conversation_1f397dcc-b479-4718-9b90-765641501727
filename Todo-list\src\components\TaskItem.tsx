import { useCallback } from '@lynx-js/react';
import type { Task, Priority } from '../types/index.js';
import { useApp } from '../context/AppContext.js';
import { formatDate, isTaskOverdue, isTaskDueToday, getPriorityColor, getTaskCompletionPercentage } from '../utils/helpers.js';

interface TaskItemProps {
  task: Task;
  onEdit?: (task: Task) => void;
}

export function TaskItem({ task, onEdit }: TaskItemProps) {
  const { toggleTaskStatus, deleteTask, getCategoryById, getTagById } = useApp();

  const handleToggleComplete = useCallback(() => {
    toggleTaskStatus(task.id);
  }, [task.id, toggleTaskStatus]);

  const handleDelete = useCallback(() => {
    deleteTask(task.id);
  }, [task.id, deleteTask]);

  const handleEdit = useCallback(() => {
    onEdit?.(task);
  }, [task, onEdit]);

  const category = task.categoryId ? getCategoryById(task.categoryId) : null;
  const isOverdue = isTaskOverdue(task);
  const isDueToday = isTaskDueToday(task);
  const completionPercentage = getTaskCompletionPercentage(task);

  const priorityColor = getPriorityColor(task.priority);
  const isCompleted = task.status === 'completed';

  return (
    <view className={`task-item ${isCompleted ? 'completed' : ''} ${isOverdue ? 'overdue' : ''}`}>
      {/* Priority indicator */}
      <view 
        className="priority-indicator"
        style={{ backgroundColor: priorityColor }}
      />
      
      {/* Main content */}
      <view className="task-content">
        {/* Header */}
        <view className="task-header">
          {/* Checkbox */}
          <view 
            className={`task-checkbox ${isCompleted ? 'checked' : ''}`}
            bindtap={handleToggleComplete}
          >
            {isCompleted && <text className="checkmark">✓</text>}
          </view>
          
          {/* Title */}
          <text 
            className={`task-title ${isCompleted ? 'completed' : ''}`}
            bindtap={handleEdit}
          >
            {task.title}
          </text>
          
          {/* Actions */}
          <view className="task-actions">
            <text className="action-button edit" bindtap={handleEdit}>✏️</text>
            <text className="action-button delete" bindtap={handleDelete}>🗑️</text>
          </view>
        </view>

        {/* Description */}
        {task.description && (
          <text className="task-description">{task.description}</text>
        )}

        {/* Metadata */}
        <view className="task-metadata">
          {/* Category */}
          {category && (
            <view className="task-category" style={{ backgroundColor: category.color }}>
              <text className="category-icon">{category.icon}</text>
              <text className="category-name">{category.name}</text>
            </view>
          )}

          {/* Due date */}
          {task.dueDate && (
            <view className={`task-due-date ${isOverdue ? 'overdue' : isDueToday ? 'due-today' : ''}`}>
              <text className="due-date-icon">📅</text>
              <text className="due-date-text">{formatDate(task.dueDate)}</text>
            </view>
          )}

          {/* Priority */}
          <view className="task-priority" style={{ color: priorityColor }}>
            <text className="priority-text">{task.priority.toUpperCase()}</text>
          </view>
        </view>

        {/* Tags */}
        {task.tags.length > 0 && (
          <view className="task-tags">
            {task.tags.map(tagId => {
              const tag = getTagById(tagId);
              return tag ? (
                <view 
                  key={tagId}
                  className="task-tag"
                  style={{ backgroundColor: tag.color }}
                >
                  <text className="tag-text">{tag.name}</text>
                </view>
              ) : null;
            })}
          </view>
        )}

        {/* Subtasks progress */}
        {task.subtasks.length > 0 && (
          <view className="subtasks-progress">
            <view className="progress-bar">
              <view 
                className="progress-fill"
                style={{ width: `${completionPercentage}%` }}
              />
            </view>
            <text className="progress-text">
              {task.subtasks.filter(st => st.completed).length}/{task.subtasks.length} subtasks
            </text>
          </view>
        )}
      </view>
    </view>
  );
}

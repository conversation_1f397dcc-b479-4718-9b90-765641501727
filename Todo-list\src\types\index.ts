// Core data types for the Todo List application

export type Priority = 'low' | 'medium' | 'high';

export type TaskStatus = 'pending' | 'completed' | 'cancelled';

export type RecurrenceType = 'none' | 'daily' | 'weekly' | 'monthly' | 'custom';

export interface Tag {
  id: string;
  name: string;
  color: string;
}

export interface Category {
  id: string;
  name: string;
  color: string;
  icon?: string;
}

export interface Subtask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: Date;
}

export interface RecurrenceConfig {
  type: RecurrenceType;
  interval?: number; // For custom intervals (e.g., every 3 days)
  daysOfWeek?: number[]; // For weekly recurrence (0-6, Sunday-Saturday)
  dayOfMonth?: number; // For monthly recurrence
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: Priority;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  categoryId?: string;
  tags: string[]; // Array of tag IDs
  subtasks: Subtask[];
  recurrence?: RecurrenceConfig;
  parentTaskId?: string; // For subtasks that are also full tasks
  estimatedDuration?: number; // In minutes
  actualDuration?: number; // In minutes
}

export interface TaskFilter {
  status?: TaskStatus[];
  priority?: Priority[];
  categoryId?: string;
  tagIds?: string[];
  dueDateRange?: {
    start?: Date;
    end?: Date;
  };
  searchQuery?: string;
  hasSubtasks?: boolean;
  isRecurring?: boolean;
}

export interface TaskSort {
  field: 'title' | 'dueDate' | 'priority' | 'createdAt' | 'updatedAt';
  direction: 'asc' | 'desc';
}

// Application state interfaces
export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  defaultCategory?: string;
  defaultPriority: Priority;
  enableNotifications: boolean;
  autoMarkOverdue: boolean;
  showCompletedTasks: boolean;
  taskSortOrder: TaskSort;
}

export interface AppState {
  tasks: Task[];
  categories: Category[];
  tags: Tag[];
  filter: TaskFilter;
  sort: TaskSort;
  settings: AppSettings;
  selectedTaskId?: string;
  isLoading: boolean;
  error?: string;
}

// Action types for state management
export type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | undefined }
  | { type: 'ADD_TASK'; payload: Omit<Task, 'id' | 'createdAt' | 'updatedAt'> }
  | { type: 'UPDATE_TASK'; payload: { id: string; updates: Partial<Task> } }
  | { type: 'DELETE_TASK'; payload: string }
  | { type: 'TOGGLE_TASK_STATUS'; payload: string }
  | { type: 'ADD_SUBTASK'; payload: { taskId: string; subtask: Omit<Subtask, 'id' | 'createdAt'> } }
  | { type: 'UPDATE_SUBTASK'; payload: { taskId: string; subtaskId: string; updates: Partial<Subtask> } }
  | { type: 'DELETE_SUBTASK'; payload: { taskId: string; subtaskId: string } }
  | { type: 'ADD_CATEGORY'; payload: Omit<Category, 'id'> }
  | { type: 'UPDATE_CATEGORY'; payload: { id: string; updates: Partial<Category> } }
  | { type: 'DELETE_CATEGORY'; payload: string }
  | { type: 'ADD_TAG'; payload: Omit<Tag, 'id'> }
  | { type: 'UPDATE_TAG'; payload: { id: string; updates: Partial<Tag> } }
  | { type: 'DELETE_TAG'; payload: string }
  | { type: 'SET_FILTER'; payload: Partial<TaskFilter> }
  | { type: 'CLEAR_FILTER' }
  | { type: 'SET_SORT'; payload: TaskSort }
  | { type: 'SELECT_TASK'; payload: string | undefined }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<AppSettings> }
  | { type: 'LOAD_DATA'; payload: { tasks: Task[]; categories: Category[]; tags: Tag[] } }
  | { type: 'RESET_APP' };

// Utility types
export interface CreateTaskInput {
  title: string;
  description?: string;
  priority?: Priority;
  dueDate?: Date;
  categoryId?: string;
  tagIds?: string[];
  recurrence?: RecurrenceConfig;
  estimatedDuration?: number;
}

export interface TaskStats {
  total: number;
  completed: number;
  pending: number;
  overdue: number;
  dueToday: number;
  dueThisWeek: number;
}
